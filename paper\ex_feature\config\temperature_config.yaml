# 温度预测特征提取配置文件
# 基于DeepMu项目优化的温度适应性特征提取参数

# 基本设置
genetic_code: 11  # NCBI遗传密码ID (11=细菌标准密码)
num_threads: 8    # 并行线程数
enable_checkpoint: true
checkpoint_interval: 50  # 每处理50个基因组保存一次断点
validation_enabled: true
log_level: INFO

# 特征提取设置
feature_types:
  - codon      # 密码子特征
  - genomic    # 基因组特征
  - protein    # 蛋白质特征
  - pathway    # 代谢途径特征
  - phylo      # 系统发育特征
  - rna        # RNA特征

output_format: npz  # 输出格式 (npz, hdf5, csv)

# 密码子特征设置
codon_features:
  use_heg_features: true  # 是否使用高表达基因特征
  heg_ko_list: null       # 高表达基因KO列表文件路径
  calculate_rscu: true    # 计算相对同义密码子使用
  include_synonymous_usage: true
  
  # 温度适应性相关设置
  thermostable_amino_acids:
    - G  # 甘氨酸
    - P  # 脯氨酸
    - A  # 丙氨酸
    - V  # 缬氨酸
    - I  # 异亮氨酸
    - L  # 亮氨酸
  
  thermolabile_amino_acids:
    - Q  # 谷氨酰胺
    - N  # 天冬酰胺
    - S  # 丝氨酸
    - T  # 苏氨酸
    - C  # 半胱氨酸
    - M  # 甲硫氨酸

# 基因组特征设置
genomic_features:
  gc_window_size: 1000    # GC含量计算窗口大小
  kmer_sizes: [2, 3, 4]   # k-mer大小
  include_repeats: true   # 包含重复序列分析
  include_complexity: true # 包含序列复杂度分析
  
  # 热稳定性相关设置
  calculate_hydrogen_bonds: true
  calculate_stacking_energy: true
  analyze_gc_skew: true

# 蛋白质特征设置
protein_features:
  include_physicochemical: true    # 理化性质
  include_secondary_structure: false  # 二级结构预测（计算密集）
  calculate_instability: true     # 不稳定性指数
  include_domains: false          # 蛋白质域分析（需要额外数据库）
  
  # 温度适应性相关
  calculate_thermostability_index: true
  analyze_amino_acid_composition: true
  include_hydrophobicity: true

# 代谢途径特征设置
pathway_features:
  kegg_database: null     # KEGG数据库路径
  include_modules: true   # 包含KEGG模块
  include_pathways: true  # 包含KEGG途径
  calculate_completeness: true  # 计算途径完整性
  
  # 温度相关代谢途径
  temperature_related_pathways:
    - "Heat shock response"
    - "Cold shock response"
    - "Membrane lipid metabolism"
    - "Protein folding"

# 系统发育特征设置
phylo_features:
  taxonomy_levels:
    - phylum
    - class
    - order
    - family
    - genus
  include_distances: false  # 系统发育距离（需要额外计算）
  use_ncbi_taxonomy: true

# RNA特征设置
rna_features:
  include_trna: true      # tRNA特征
  include_rrna: true      # rRNA特征
  include_secondary_structure: false  # RNA二级结构
  calculate_modifications: false      # RNA修饰
  
  # 温度适应性相关
  analyze_gc_content: true
  calculate_stability: true

# 缓存设置
cache_enabled: true
cache_dir: "feature_cache"
cache_compression: true
cache_max_size: "10GB"  # 最大缓存大小

# 并行处理设置
parallel_processing:
  mode: "thread"          # thread 或 process
  max_retries: 3          # 最大重试次数
  timeout: 300            # 任务超时时间（秒）
  chunk_size: 10          # 批处理块大小

# 验证设置
validation:
  strict_mode: false      # 严格验证模式
  outlier_threshold: 3.0  # 异常值检测阈值（标准差倍数）
  missing_threshold: 0.1  # 缺失值比例阈值

# 输出设置
output:
  precision: 6            # 数值精度
  include_metadata: true  # 包含元数据
  save_intermediate: false # 保存中间结果
  compression_level: 6    # 压缩级别 (0-9)

# 日志设置
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "temperature_feature_extraction.log"
  max_size: "100MB"
  backup_count: 5

# 性能优化设置
performance:
  memory_limit: "8GB"     # 内存使用限制
  disk_cache: true        # 磁盘缓存
  prefetch_data: true     # 数据预取
  batch_size: 32          # 批处理大小

# 特征选择设置（高级）
feature_selection:
  enabled: false          # 是否启用特征选择
  method: "variance"      # 选择方法 (variance, correlation, mutual_info)
  threshold: 0.01         # 选择阈值
  max_features: 1000      # 最大特征数

# 质量控制设置
quality_control:
  min_genome_size: 100000      # 最小基因组大小 (bp)
  max_genome_size: 20000000    # 最大基因组大小 (bp)
  min_gene_count: 50           # 最小基因数量
  max_n_content: 0.1           # 最大N含量比例
  
# 温度特异性设置
temperature_specific:
  # 温度范围分类
  psychrophile_max: 20     # 嗜冷菌最高温度
  mesophile_min: 20        # 中温菌最低温度
  mesophile_max: 45        # 中温菌最高温度
  thermophile_min: 45      # 嗜热菌最低温度
  thermophile_max: 80      # 嗜热菌最高温度
  hyperthermophile_min: 80 # 超嗜热菌最低温度
  
  # 温度相关特征权重
  feature_weights:
    gc_content: 1.5        # GC含量权重较高
    codon_stability: 1.2   # 密码子稳定性
    amino_acid_composition: 1.3  # 氨基酸组成
    membrane_features: 1.1 # 膜相关特征
