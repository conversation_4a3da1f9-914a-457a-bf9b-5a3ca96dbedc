"""
温度配置管理器

提供温度预测特征提取的配置管理功能。

主要功能：
1. 配置文件加载和解析
2. 默认配置管理
3. 配置验证和校正
4. 动态配置更新
"""

import os
import json
import yaml
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path

logger = logging.getLogger(__name__)

class TemperatureConfig:
    """
    温度配置管理器
    
    管理温度预测特征提取的所有配置参数。
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        参数:
            config_file: 配置文件路径（可选）
        """
        # 默认配置
        self._default_config = {
            # 基本设置
            'genetic_code': 11,
            'num_threads': 4,
            'enable_checkpoint': True,
            'checkpoint_interval': 100,
            'validation_enabled': True,
            'log_level': 'INFO',
            
            # 特征提取设置
            'feature_types': ['codon', 'genomic', 'protein', 'pathway', 'phylo', 'rna'],
            'output_format': 'npz',
            
            # 密码子特征设置
            'codon_features': {
                'use_heg_features': True,
                'heg_ko_list': None,
                'calculate_rscu': True,
                'include_synonymous_usage': True
            },
            
            # 基因组特征设置
            'genomic_features': {
                'gc_window_size': 1000,
                'kmer_sizes': [2, 3, 4],
                'include_repeats': True,
                'include_complexity': True
            },
            
            # 蛋白质特征设置
            'protein_features': {
                'include_physicochemical': True,
                'include_secondary_structure': False,
                'calculate_instability': True,
                'include_domains': False
            },
            
            # 代谢途径特征设置
            'pathway_features': {
                'kegg_database': None,
                'include_modules': True,
                'include_pathways': True,
                'calculate_completeness': True
            },
            
            # 系统发育特征设置
            'phylo_features': {
                'taxonomy_levels': ['phylum', 'class', 'order', 'family', 'genus'],
                'include_distances': False,
                'use_ncbi_taxonomy': True
            },
            
            # RNA特征设置
            'rna_features': {
                'include_trna': True,
                'include_rrna': True,
                'include_secondary_structure': False,
                'calculate_modifications': False
            },
            
            # 缓存设置
            'cache_enabled': True,
            'cache_dir': 'feature_cache',
            'cache_compression': True,
            
            # 输出设置
            'output_precision': 6,
            'include_metadata': True,
            'save_intermediate': False
        }
        
        # 当前配置
        self._config = self._default_config.copy()
        
        # 加载配置文件
        if config_file:
            self.load_config(config_file)
        
        logger.info("温度配置管理器初始化完成")
    
    def load_config(self, config_file: str) -> None:
        """
        从文件加载配置
        
        参数:
            config_file: 配置文件路径
        """
        config_path = Path(config_file)
        
        if not config_path.exists():
            logger.warning(f"配置文件不存在: {config_file}")
            return
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    file_config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    file_config = json.load(f)
                else:
                    logger.error(f"不支持的配置文件格式: {config_path.suffix}")
                    return
            
            # 递归更新配置
            self._update_config(self._config, file_config)
            
            logger.info(f"成功加载配置文件: {config_file}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    def _update_config(self, base_config: Dict[str, Any], new_config: Dict[str, Any]) -> None:
        """递归更新配置"""
        for key, value in new_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._update_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        参数:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        返回:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        参数:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def update(self, config_dict: Dict[str, Any]) -> None:
        """
        批量更新配置
        
        参数:
            config_dict: 配置字典
        """
        self._update_config(self._config, config_dict)
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self._config = self._default_config.copy()
        logger.info("配置已重置为默认值")
    
    def validate_config(self) -> Dict[str, Any]:
        """
        验证配置的有效性
        
        返回:
            验证结果字典
        """
        validation_result = {
            'valid': True,
            'warnings': [],
            'errors': []
        }
        
        # 验证基本设置
        if self.get('num_threads', 1) < 1:
            validation_result['errors'].append("num_threads 必须大于 0")
            validation_result['valid'] = False
        
        if self.get('genetic_code', 11) not in [1, 2, 3, 4, 5, 6, 9, 10, 11, 12, 13, 14, 15, 16, 21, 22, 23, 24, 25]:
            validation_result['warnings'].append("genetic_code 可能不是有效的NCBI遗传密码ID")
        
        # 验证特征类型
        valid_feature_types = ['codon', 'genomic', 'protein', 'pathway', 'phylo', 'rna']
        feature_types = self.get('feature_types', [])
        
        for ft in feature_types:
            if ft not in valid_feature_types:
                validation_result['warnings'].append(f"未知的特征类型: {ft}")
        
        # 验证文件路径
        cache_dir = self.get('cache_dir')
        if cache_dir and not os.path.exists(cache_dir):
            try:
                os.makedirs(cache_dir, exist_ok=True)
            except Exception as e:
                validation_result['warnings'].append(f"无法创建缓存目录 {cache_dir}: {e}")
        
        # 验证数值范围
        if self.get('checkpoint_interval', 100) < 1:
            validation_result['errors'].append("checkpoint_interval 必须大于 0")
            validation_result['valid'] = False
        
        gc_window_size = self.get('genomic_features.gc_window_size', 1000)
        if gc_window_size < 100:
            validation_result['warnings'].append("gc_window_size 可能过小，建议至少100bp")
        
        return validation_result
    
    def save_config(self, output_file: str, format: str = 'yaml') -> None:
        """
        保存当前配置到文件
        
        参数:
            output_file: 输出文件路径
            format: 输出格式 ('yaml' 或 'json')
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                if format.lower() == 'yaml':
                    yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
                elif format.lower() == 'json':
                    json.dump(self._config, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的格式: {format}")
            
            logger.info(f"配置已保存到: {output_file}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'feature_types': self.get('feature_types'),
            'num_threads': self.get('num_threads'),
            'genetic_code': self.get('genetic_code'),
            'cache_enabled': self.get('cache_enabled'),
            'checkpoint_enabled': self.get('enable_checkpoint'),
            'validation_enabled': self.get('validation_enabled'),
            'output_format': self.get('output_format')
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """返回完整配置字典"""
        return self._config.copy()
    
    def __str__(self) -> str:
        """字符串表示"""
        summary = self.get_summary()
        return f"TemperatureConfig({summary})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"TemperatureConfig(config={self._config})"
