"""DeepMu的tRNA注释模块。

该模块提供使用tRNAscan-SE在基因组序列中识别tRNA基因的功能，
并正确处理遗传密码。
"""

import subprocess
import os
import logging
from pathlib import Path
from typing import List, Dict, Optional

class tRNAscanSEWrapper:
    """tRNAscan-SE的包装器，具有可配置选项和结果解析功能。"""

    def __init__(self, tRNAscan_path: str = "tRNAscan-SE"):
        """
        使用tRNAscan-SE可执行文件路径初始化包装器。

        参数:
            tRNAscan_path: tRNAscan-SE可执行文件路径
        """
        self.executable = tRNAscan_path
        self.valid_kingdoms = ['bac', 'arc', 'euk', 'mito']

    def predict_tRNAs(
        self,
        input_fasta: str,
        output_prefix: Optional[str] = None,
        log_file: Optional[str] = None,
        kingdom: str = 'bac',
        genetic_code: int = 11,
        cove_cutoff: int = 20,
        show_progress: bool = False,
        output_dir: str = ".",
        overwrite: bool = True
    ) -> Dict:
        """
        使用指定参数运行tRNAscan-SE预测。

        参数:
            input_fasta: 输入FASTA文件路径
            output_prefix: 输出文件前缀
            log_file: 保存日志文件的路径
            kingdom: tRNA预测的界 (bac/arc/euk/mito, 默认: bac)
            genetic_code: 使用的遗传密码 (默认: 11为细菌)
            cove_cutoff: Cove分数截止值 (默认: 20)
            show_progress: 向stdout显示进度消息
            output_dir: 结果输出目录
            overwrite: 是否覆盖现有输出文件 (默认: True)

        Returns:
            Dictionary with:
            - 'success': Boolean indicating success
            - 'predicted_tRNAs': List of tRNA features
            - 'output_files': Generated output files
        """
        # Validate input file
        if not os.path.exists(input_fasta):
            raise FileNotFoundError(f"Input FASTA file not found: {input_fasta}")

        # Build command
        cmd = [self.executable]

        # Validate kingdom
        if kingdom not in self.valid_kingdoms:
            raise ValueError(f"Invalid kingdom: {kingdom}. Choose from {self.valid_kingdoms}")

        # Add options based on kingdom
        if kingdom == 'bac':
            cmd.append("-B")  # Bacterial model
        elif kingdom == 'arc':
            cmd.append("-A")  # Archaeal model
        elif kingdom == 'euk':
            cmd.append("-E")  # Eukaryotic model
        elif kingdom == 'mito':
            cmd.extend(["-M", "mammal"])  # Mitochondrial model (mammalian)

        # Handle genetic code
        # tRNAscan-SE doesn't directly use genetic code, but we can adjust parameters
        # based on the genetic code if needed in the future

        # Validate output_prefix format
        if output_prefix and any(c in output_prefix for c in ('/', '\\')):
            raise ValueError(f"Invalid output_prefix '{output_prefix}' - must not contain path separators")

        # Ensure output directory exists and is absolute
        output_dir = os.path.abspath(output_dir)
        os.makedirs(output_dir, exist_ok=True)

        # Set base name for output files
        base_name = output_prefix or os.path.splitext(os.path.basename(input_fasta))[0]

        # Set output file paths
        output_file = os.path.join(output_dir, f"{base_name}_tRNA.tsv")
        fasta_file = os.path.join(output_dir, f"{base_name}_tRNA.fasta")

        # Check if output files already exist
        if os.path.exists(output_file) or os.path.exists(fasta_file):
            if not overwrite:
                # Skip running tRNAscan-SE if files exist and overwrite is False
                logging.info(f"Output files already exist and overwrite=False. Skipping tRNAscan-SE.")
                result = {"success": True, "predicted_tRNAs": [], "output_files": []}

                # Check which files exist and add them to output_files
                if os.path.exists(output_file):
                    result["output_files"].append(output_file)
                    # Parse existing tRNA file
                    result["predicted_tRNAs"] = self._parse_tRNA_output(output_file)

                if os.path.exists(fasta_file):
                    result["output_files"].append(fasta_file)

                return result
            else:
                # Remove existing files if overwrite is True
                logging.info(f"Removing existing output files for overwrite.")
                if os.path.exists(output_file):
                    os.remove(output_file)
                if os.path.exists(fasta_file):
                    os.remove(fasta_file)

        # Set output file directly using -o option with absolute path
        cmd.extend(["-o", output_file])

        # Set output FASTA file using -a option with absolute path
        cmd.extend(["-a", fasta_file])

        # Add other options
        if log_file:
            cmd.extend(["-l", log_file])

        cmd.extend(["-X", str(cove_cutoff)])

        if show_progress:
            cmd.append("-d")

        # Directory creation and permission check already done above

        # Convert input_fasta to absolute path
        abs_input_fasta = os.path.abspath(input_fasta)
        cmd.append(abs_input_fasta)

        # Run command
        result = {"success": False, "predicted_tRNAs": [], "output_files": []}

        try:
            # Execute with redirected stdout/stderr
            stdout = subprocess.PIPE
            stderr = subprocess.PIPE

            cmd_str = ' '.join(cmd)
            logging.info(f"Running tRNAscan-SE command: {cmd_str}")
            logging.info(f"Working directory: current directory")

            process = subprocess.run(
                cmd,
                stdout=stdout,
                stderr=stderr,
                check=True,
                text=True
            )

            # Log the output for debugging
            if process.stdout:
                logging.debug(f"tRNAscan-SE stdout: {process.stdout}")

            if process.stderr:
                logging.debug(f"tRNAscan-SE stderr: {process.stderr}")

            # Collect output files
            expected_files = [
                f"{base_name}_tRNA.tsv",
                f"{base_name}_tRNA.fasta"
            ]

            # Check if output directory exists
            if not os.path.exists(output_dir):
                logging.error(f"Output directory does not exist: {output_dir}")
                result["error"] = f"Output directory does not exist: {output_dir}"
                return result

            # List files in output directory for debugging
            logging.debug(f"Files in output directory: {os.listdir(output_dir)}")

            result["output_files"] = [
                str(Path(output_dir) / f) for f in expected_files
                if os.path.exists(Path(output_dir) / f)
            ]

            # Parse results
            tRNA_file = Path(output_dir) / f"{base_name}_tRNA.tsv"
            if os.path.exists(tRNA_file):
                result["predicted_tRNAs"] = self._parse_tRNA_output(tRNA_file)
                result["success"] = True
            else:
                logging.warning(f"tRNA file not found: {tRNA_file}")
                result["error"] = f"tRNA file not found: {tRNA_file}"

        except subprocess.CalledProcessError as e:
            error_msg = f"tRNAscan-SE failed with exit code {e.returncode}"
            if e.stdout:
                error_msg += f"\nStdout: {e.stdout}"
            if e.stderr:
                error_msg += f"\nStderr: {e.stderr}"
            logging.error(error_msg)
            result["error"] = error_msg
        except FileNotFoundError:
            error_msg = "tRNAscan-SE executable not found. Is it installed and in PATH?"
            logging.error(error_msg)
            result["error"] = error_msg
        except Exception as e:
            error_msg = f"Unexpected error running tRNAscan-SE: {str(e)}"
            logging.error(error_msg)
            result["error"] = error_msg

        return result

    def _parse_tRNA_output(self, tRNA_file: str) -> List[Dict]:
        """Parse .tRNA output file into structured data."""
        tRNAs = []

        with open(tRNA_file) as f:
            # Skip header lines (first 3 lines)
            for _ in range(3):
                next(f, None)

            # Parse data lines
            for line in f:
                line = line.strip()
                if not line or line.startswith("--------"):
                    continue  # Skip empty or separator lines

                try:
                    parts = line.split()
                    if len(parts) >= 8:  # At least 8 columns in the output
                        # Handle the case where the last column is a note (e.g., 'pseudo')
                        note = parts[8] if len(parts) > 8 else None

                        tRNA = {
                            "sequence_name": parts[0],
                            "tRNA_number": int(parts[1]),
                            "start": int(parts[2]),
                            "end": int(parts[3]),
                            "tRNA_type": parts[4],
                            "anticodon": parts[5],
                            "intron_begin": int(parts[6]),
                            "intron_end": int(parts[7]),
                            "score": float(parts[8]) if len(parts) > 8 and parts[8] != "pseudo" else 0.0,
                            "note": note
                        }
                        tRNAs.append(tRNA)
                except (ValueError, IndexError) as e:
                    # Log the error but continue parsing
                    logging.warning(f"Error parsing tRNA line: {line}. Error: {str(e)}")
                    continue

        return tRNAs
