#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的温度特征提取器 - 结合DeepMu utils工具

这个模块整合了DeepMu项目的utils工具，提供了一个优化的温度特征提取器，
具有更好的错误处理、日志记录、数据预处理和特征选择功能。

主要改进：
1. 集成DeepMu的异常处理系统
2. 使用DeepMu的日志工具
3. 集成特征选择和数据变换工具
4. 添加数据验证和预处理功能
5. 支持多种输出格式和可视化
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple
import tempfile
import gzip

# 添加DeepMu路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
deepmu_path = os.path.join(parent_dir, 'deepmu')
if os.path.exists(deepmu_path) and deepmu_path not in sys.path:
    sys.path.insert(0, deepmu_path)

# 导入DeepMu utils工具
try:
    from deepmu.utils import (
        get_logger, DeepMuError, FeatureCalculationError, 
        PreprocessingError, InvalidSequenceError
    )
    from deepmu.utils.transforms import StandardScaler, MinMaxScaler
    from deepmu.utils.feature_selection import FeatureSelector
    from deepmu.utils.metrics import calculate_metrics
    from deepmu.utils.visualization import save_metrics_to_tsv, plot_metrics
    from deepmu.utils.preprocess import preprocess_genome
    DEEPMU_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入DeepMu utils工具: {e}")
    DEEPMU_AVAILABLE = False
    # 创建简单的替代品
    def get_logger(name="ex_feature"):
        return logging.getLogger(name)
    
    class DeepMuError(Exception):
        pass
    
    class FeatureCalculationError(DeepMuError):
        pass

# 导入本地模块
try:
    from .features.codon_temp import CodonTemperatureFeatures
    from .features.genomic_temp import GenomicTemperatureFeatures
    from .utils.config import TemperatureConfig
    from .utils.parallel import ParallelProcessor
    from .utils.validation import FeatureValidator
except ImportError:
    # 如果本地模块不存在，使用简化版本
    CodonTemperatureFeatures = None
    GenomicTemperatureFeatures = None
    TemperatureConfig = None
    ParallelProcessor = None
    FeatureValidator = None

# 导入简化的特征提取器
try:
    from .simple_genomic_features import SimpleGenomicFeatures, SimpleCodonFeatures
except ImportError:
    try:
        from simple_genomic_features import SimpleGenomicFeatures, SimpleCodonFeatures
    except ImportError:
        SimpleGenomicFeatures = None
        SimpleCodonFeatures = None

class OptimizedTemperatureExtractor:
    """
    优化的温度特征提取器
    
    结合了DeepMu的utils工具，提供更强大的特征提取、预处理、
    特征选择和数据验证功能。
    """
    
    def __init__(self, 
                 config_file: Optional[str] = None,
                 num_threads: int = 4,
                 enable_preprocessing: bool = True,
                 enable_feature_selection: bool = True,
                 enable_scaling: bool = True,
                 scaler_type: str = 'standard',
                 feature_selection_method: str = 'mutual_info',
                 max_features: int = 50,
                 output_dir: str = "optimized_features",
                 log_level: str = 'INFO'):
        """
        初始化优化的特征提取器
        
        参数:
            config_file: 配置文件路径
            num_threads: 线程数
            enable_preprocessing: 是否启用预处理
            enable_feature_selection: 是否启用特征选择
            enable_scaling: 是否启用数据缩放
            scaler_type: 缩放器类型 ('standard' 或 'minmax')
            feature_selection_method: 特征选择方法
            max_features: 最大特征数
            output_dir: 输出目录
            log_level: 日志级别
        """
        # 设置日志
        self.logger = get_logger("OptimizedTemperatureExtractor")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 存储配置
        self.num_threads = num_threads
        self.enable_preprocessing = enable_preprocessing
        self.enable_feature_selection = enable_feature_selection
        self.enable_scaling = enable_scaling
        self.scaler_type = scaler_type
        self.feature_selection_method = feature_selection_method
        self.max_features = max_features
        self.output_dir = Path(output_dir)
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self._init_components()
        
        self.logger.info("优化的温度特征提取器初始化完成")
    
    def _init_components(self):
        """初始化各个组件"""
        # 创建默认配置
        default_config = {
            'gc_window_size': 1000,
            'codon_table': 11,
            'min_gene_length': 300
        }

        # 初始化特征提取器 - 优先使用简化版本
        try:
            if SimpleGenomicFeatures:
                self.genomic_extractor = SimpleGenomicFeatures()
                self.logger.info("使用简化的基因组特征提取器")
            elif GenomicTemperatureFeatures:
                self.genomic_extractor = GenomicTemperatureFeatures(default_config)
                self.logger.info("使用完整的基因组特征提取器")
            else:
                self.genomic_extractor = None
                self.logger.warning("没有可用的基因组特征提取器")
        except Exception as e:
            self.logger.warning(f"基因组特征提取器初始化失败: {e}")
            self.genomic_extractor = None

        try:
            if SimpleCodonFeatures:
                self.codon_extractor = SimpleCodonFeatures()
                self.logger.info("使用简化的密码子特征提取器")
            elif CodonTemperatureFeatures:
                self.codon_extractor = CodonTemperatureFeatures(default_config)
                self.logger.info("使用完整的密码子特征提取器")
            else:
                self.codon_extractor = None
                self.logger.warning("没有可用的密码子特征提取器")
        except Exception as e:
            self.logger.warning(f"密码子特征提取器初始化失败: {e}")
            self.codon_extractor = None
        
        # 初始化数据变换器
        if DEEPMU_AVAILABLE and self.enable_scaling:
            if self.scaler_type == 'standard':
                self.scaler = StandardScaler()
            elif self.scaler_type == 'minmax':
                self.scaler = MinMaxScaler()
            else:
                self.scaler = None
                self.logger.warning(f"未知的缩放器类型: {self.scaler_type}")
        else:
            self.scaler = None
        
        # 初始化特征选择器
        if DEEPMU_AVAILABLE and self.enable_feature_selection:
            self.feature_selector = FeatureSelector(
                method=self.feature_selection_method,
                k=self.max_features
            )
        else:
            self.feature_selector = None
        
        # 初始化并行处理器
        self.parallel_processor = ParallelProcessor(self.num_threads) if ParallelProcessor else None
    
    def extract_single_genome(self, 
                            genome_id: str,
                            genome_file: str,
                            cds_file: Optional[str] = None,
                            protein_file: Optional[str] = None,
                            ko_file: Optional[str] = None,
                            temperature: Optional[float] = None) -> Dict[str, np.ndarray]:
        """
        提取单个基因组的温度特征
        
        参数:
            genome_id: 基因组ID
            genome_file: 基因组文件路径
            cds_file: CDS文件路径
            protein_file: 蛋白质文件路径
            ko_file: KO注释文件路径
            temperature: 已知温度（用于监督学习）
            
        返回:
            特征字典
        """
        try:
            self.logger.info(f"开始提取基因组 {genome_id} 的特征")
            
            # 验证输入文件
            if not os.path.exists(genome_file):
                raise FileNotFoundError(f"基因组文件不存在: {genome_file}")
            
            features = {}
            
            # 预处理（如果启用且DeepMu可用）
            if self.enable_preprocessing and DEEPMU_AVAILABLE:
                try:
                    processed_files = self._preprocess_genome(genome_file, genome_id)
                    if processed_files:
                        cds_file = processed_files.get('cds', cds_file)
                        protein_file = processed_files.get('protein', protein_file)
                        ko_file = processed_files.get('ko', ko_file)
                except Exception as e:
                    self.logger.warning(f"预处理失败，使用原始文件: {e}")
            
            # 提取基因组特征
            if self.genomic_extractor:
                try:
                    genomic_features = self.genomic_extractor.extract_features(genome_file)
                    features.update({f"genomic_{k}": v for k, v in genomic_features.items()})
                    self.logger.debug(f"提取了 {len(genomic_features)} 个基因组特征")
                except Exception as e:
                    self.logger.error(f"基因组特征提取失败: {e}")
                    raise FeatureCalculationError(f"基因组特征提取失败: {e}")
            
            # 提取密码子特征
            if self.codon_extractor and cds_file and os.path.exists(cds_file):
                try:
                    codon_features = self.codon_extractor.extract_features(cds_file)
                    features.update({f"codon_{k}": v for k, v in codon_features.items()})
                    self.logger.debug(f"提取了 {len(codon_features)} 个密码子特征")
                except Exception as e:
                    self.logger.warning(f"密码子特征提取失败: {e}")
            
            # 添加温度标签（如果提供）
            if temperature is not None:
                features['temperature'] = temperature
            
            # 添加基因组ID
            features['genome_id'] = genome_id
            
            self.logger.info(f"成功提取基因组 {genome_id} 的 {len(features)} 个特征")
            return features
            
        except Exception as e:
            self.logger.error(f"提取基因组 {genome_id} 特征时出错: {e}")
            raise FeatureCalculationError(f"特征提取失败: {e}")
    
    def _preprocess_genome(self, genome_file: str, genome_id: str) -> Optional[Dict[str, str]]:
        """
        使用DeepMu工具预处理基因组
        
        参数:
            genome_file: 基因组文件路径
            genome_id: 基因组ID
            
        返回:
            预处理后的文件路径字典
        """
        if not DEEPMU_AVAILABLE:
            return None
        
        try:
            # 创建临时目录用于预处理
            temp_dir = self.output_dir / "temp" / genome_id
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            # 使用DeepMu的预处理功能
            cds_file, ko_file, trna_file, rrna_file = preprocess_genome(
                genome_file=genome_file,
                output_dir=str(temp_dir),
                genetic_code=11,  # 细菌遗传密码
                identify_trnas=False,  # 暂时禁用tRNA识别
                identify_rrnas=False,  # 暂时禁用rRNA识别
                skip_existing=True
            )
            
            return {
                'cds': cds_file,
                'ko': ko_file,
                'trna': trna_file,
                'rrna': rrna_file
            }
            
        except Exception as e:
            self.logger.warning(f"预处理失败: {e}")
            return None

    def extract_batch(self,
                     metadata_file: str,
                     genome_col: str = 'genome_file',
                     cds_col: str = 'cds_file',
                     protein_col: str = 'protein_file',
                     ko_col: str = 'ko_file',
                     temperature_col: str = 'temperature',
                     genome_id_col: str = 'genome_id',
                     output_file: str = "batch_features.npz") -> str:
        """
        批量提取特征

        参数:
            metadata_file: 元数据文件路径
            genome_col: 基因组文件列名
            cds_col: CDS文件列名
            protein_col: 蛋白质文件列名
            ko_col: KO文件列名
            temperature_col: 温度列名
            genome_id_col: 基因组ID列名
            output_file: 输出文件名

        返回:
            输出文件路径
        """
        try:
            self.logger.info(f"开始批量特征提取: {metadata_file}")

            # 读取元数据
            if not os.path.exists(metadata_file):
                raise FileNotFoundError(f"元数据文件不存在: {metadata_file}")

            metadata = pd.read_csv(metadata_file, sep='\t')
            self.logger.info(f"读取了 {len(metadata)} 个基因组的元数据")

            # 批量提取特征
            all_features = []
            feature_names = None

            for idx, row in metadata.iterrows():
                try:
                    genome_id = row.get(genome_id_col, f"genome_{idx}")
                    genome_file = row.get(genome_col)
                    cds_file = row.get(cds_col)
                    protein_file = row.get(protein_col)
                    ko_file = row.get(ko_col)
                    temperature = row.get(temperature_col)

                    if pd.isna(genome_file) or not os.path.exists(genome_file):
                        self.logger.warning(f"跳过基因组 {genome_id}: 文件不存在")
                        continue

                    # 提取特征
                    features = self.extract_single_genome(
                        genome_id=genome_id,
                        genome_file=genome_file,
                        cds_file=cds_file if not pd.isna(cds_file) else None,
                        protein_file=protein_file if not pd.isna(protein_file) else None,
                        ko_file=ko_file if not pd.isna(ko_file) else None,
                        temperature=temperature if not pd.isna(temperature) else None
                    )

                    # 转换为数值特征向量
                    feature_vector, names = self._features_to_vector(features)
                    if feature_names is None:
                        feature_names = names

                    all_features.append(feature_vector)

                    self.logger.info(f"完成基因组 {genome_id} ({idx+1}/{len(metadata)})")

                except Exception as e:
                    self.logger.error(f"处理基因组 {genome_id} 时出错: {e}")
                    continue

            if not all_features:
                raise FeatureCalculationError("没有成功提取任何特征")

            # 转换为numpy数组
            X = np.array(all_features)
            self.logger.info(f"提取了 {X.shape[0]} 个样本，{X.shape[1]} 个特征")

            # 应用特征选择和缩放
            X_processed, selected_features = self._process_features(X, feature_names, metadata)

            # 保存结果
            output_path = self.output_dir / output_file
            self._save_features(X_processed, selected_features, metadata, output_path)

            self.logger.info(f"批量特征提取完成，保存至: {output_path}")
            return str(output_path)

        except Exception as e:
            self.logger.error(f"批量特征提取失败: {e}")
            raise FeatureCalculationError(f"批量特征提取失败: {e}")

    def _features_to_vector(self, features: Dict) -> Tuple[np.ndarray, List[str]]:
        """
        将特征字典转换为数值向量

        参数:
            features: 特征字典

        返回:
            特征向量和特征名称列表
        """
        vector = []
        names = []

        for key, value in features.items():
            if key in ['genome_id']:  # 跳过非数值特征
                continue

            if isinstance(value, (int, float, np.number)):
                vector.append(float(value))
                names.append(key)
            elif isinstance(value, np.ndarray):
                if value.size == 1:
                    vector.append(float(value.item()))
                    names.append(key)
                else:
                    # 多维特征展平
                    flat_values = value.flatten()
                    vector.extend(flat_values.astype(float))
                    names.extend([f"{key}_{i}" for i in range(len(flat_values))])

        return np.array(vector), names

    def _process_features(self, X: np.ndarray, feature_names: List[str],
                         metadata: pd.DataFrame) -> Tuple[np.ndarray, List[str]]:
        """
        处理特征：缩放和选择

        参数:
            X: 特征矩阵
            feature_names: 特征名称
            metadata: 元数据

        返回:
            处理后的特征矩阵和选择的特征名称
        """
        X_processed = X.copy()
        selected_features = feature_names.copy()

        # 数据缩放
        if self.scaler is not None:
            try:
                X_processed = self.scaler.fit_transform(X_processed)
                self.logger.info(f"应用了 {self.scaler_type} 缩放")
            except Exception as e:
                self.logger.warning(f"数据缩放失败: {e}")

        # 特征选择
        if self.feature_selector is not None and 'temperature' in metadata.columns:
            try:
                # 获取温度标签
                temperatures = metadata['temperature'].dropna().values
                if len(temperatures) > 0 and len(temperatures) == X_processed.shape[0]:
                    # 应用特征选择
                    X_selected = self.feature_selector.fit_transform(
                        X_processed, temperatures, feature_names
                    )
                    selected_features = self.feature_selector.get_selected_features()
                    X_processed = X_selected

                    self.logger.info(f"特征选择完成: {len(selected_features)} / {len(feature_names)} 特征被选中")
                else:
                    self.logger.warning("温度标签不完整，跳过特征选择")
            except Exception as e:
                self.logger.warning(f"特征选择失败: {e}")

        return X_processed, selected_features

    def _save_features(self, X: np.ndarray, feature_names: List[str],
                      metadata: pd.DataFrame, output_path: Path):
        """
        保存特征到文件

        参数:
            X: 特征矩阵
            feature_names: 特征名称
            metadata: 元数据
            output_path: 输出路径
        """
        try:
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存为npz格式
            np.savez_compressed(
                str(output_path),  # 转换为字符串路径
                features=X,
                feature_names=feature_names,
                metadata=metadata.to_dict('records')
            )

            # 同时保存为TSV格式便于查看
            tsv_path = output_path.with_suffix('.tsv')
            feature_df = pd.DataFrame(X, columns=feature_names)

            # 添加元数据列
            for col in ['genome_id', 'temperature']:
                if col in metadata.columns:
                    feature_df[col] = metadata[col].values[:len(X)]

            feature_df.to_csv(str(tsv_path), sep='\t', index=False)

            self.logger.info(f"特征已保存: {output_path} 和 {tsv_path}")

        except Exception as e:
            self.logger.error(f"保存特征失败: {e}")
            raise
