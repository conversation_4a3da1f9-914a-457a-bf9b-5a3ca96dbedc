"""
DeepMu的基于相关性的特征选择模块。

该模块提供基于特征与目标变量（生长速率或最适温度）相关性的特征选择功能。
"""

import numpy as np
import pandas as pd
import logging
from typing import List, Tuple, Union
import torch
from sklearn.feature_selection import SelectKBest, f_regression

# 设置日志
logger = logging.getLogger(__name__)

class CorrelationFeatureSelector:
    """
    基于与目标变量相关性的特征选择器。

    该类基于特征与目标变量的相关性（皮尔逊或斯皮尔曼）选择特征，
    允许为不同目标设置单独的特征集。
    """

    def __init__(self, method: str = 'pearson', k: int = 10):
        """
        初始化相关性特征选择器。

        参数:
            method: 相关性方法 ('pearson', 'spearman', 或 'f_regression')
            k: 要选择的特征数量
        """
        self.method = method
        self.k = k
        self.feature_names = None
        self.selected_indices = None
        self.feature_importances = None
        self.selected_feature_names = None

    def fit(self, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> 'CorrelationFeatureSelector':
        """
        将特征选择器拟合到数据。

        参数:
            X: 特征矩阵，形状为 (n_samples, n_features)
            y: 目标向量，形状为 (n_samples,)
            feature_names: 特征名称列表

        返回:
            自身
        """
        self.feature_names = feature_names

        # 处理边界情况
        if X.shape[1] <= self.k:
            logger.warning(f"特征数量 ({X.shape[1]}) 小于或等于 k ({self.k})。使用所有特征。")
            self.selected_indices = np.arange(X.shape[1])
            self.feature_importances = np.ones(X.shape[1])
            self.selected_feature_names = feature_names
            return self

        # 基于相关性计算特征重要性
        if self.method == 'pearson':
            # 计算皮尔逊相关系数
            correlations = np.array([np.corrcoef(X[:, i], y)[0, 1] for i in range(X.shape[1])])
            # 使用绝对相关性作为重要性
            self.feature_importances = np.abs(correlations)
        elif self.method == 'spearman':
            # 计算斯皮尔曼相关系数
            correlations = []
            for i in range(X.shape[1]):
                # 转换为排名
                x_ranks = pd.Series(X[:, i]).rank()
                y_ranks = pd.Series(y).rank()
                # 计算相关性
                corr = np.corrcoef(x_ranks, y_ranks)[0, 1]
                correlations.append(corr)
            correlations = np.array(correlations)
            # 使用绝对相关性作为重要性
            self.feature_importances = np.abs(correlations)
        elif self.method == 'f_regression':
            # 使用scikit-learn的f_regression
            selector = SelectKBest(f_regression, k=self.k)
            selector.fit(X, y)
            self.feature_importances = selector.scores_
        else:
            raise ValueError(f"未知方法: {self.method}")

        # 选择前k个特征
        self.selected_indices = np.argsort(self.feature_importances)[-self.k:]
        self.selected_feature_names = [feature_names[i] for i in self.selected_indices]

        logger.info(f"使用 {self.method} 相关性选择了 {len(self.selected_indices)} 个特征")

        return self

    def transform(self, X: np.ndarray) -> np.ndarray:
        """
        通过仅选择重要特征来转换数据。

        参数:
            X: 特征矩阵，形状为 (n_samples, n_features)

        返回:
            转换后的特征矩阵，形状为 (n_samples, k)
        """
        if self.selected_indices is None:
            raise ValueError("选择器尚未拟合")

        return X[:, self.selected_indices]

    def fit_transform(self, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> np.ndarray:
        """
        拟合选择器并转换数据。

        参数:
            X: 特征矩阵，形状为 (n_samples, n_features)
            y: 目标向量，形状为 (n_samples,)
            feature_names: 特征名称列表

        返回:
            转换后的特征矩阵，形状为 (n_samples, k)
        """
        self.fit(X, y, feature_names)
        return self.transform(X)

    def get_feature_names(self) -> List[str]:
        """
        获取所选特征的名称。

        返回:
            所选特征名称列表
        """
        if self.selected_feature_names is None:
            raise ValueError("选择器尚未拟合")

        return self.selected_feature_names

    def get_support(self, indices: bool = False) -> Union[np.ndarray, List[int]]:
        """
        获取所选特征的掩码或整数索引。

        参数:
            indices: 如果为True，返回整数索引，否则返回布尔掩码

        返回:
            所选特征的布尔掩码或整数索引
        """
        if self.selected_indices is None:
            raise ValueError("选择器尚未拟合")

        if indices:
            return self.selected_indices
        else:
            mask = np.zeros(len(self.feature_names), dtype=bool)
            mask[self.selected_indices] = True
            return mask


def select_features_for_targets(
    dataset,
    growth_rate_k: int = 10,
    optimal_temperature_k: int = 10,
    method: str = 'pearson',
    max_samples: int = 1000  # 为提高效率限制处理的样本数量
) -> Tuple[CorrelationFeatureSelector, CorrelationFeatureSelector]:
    """
    为生长速率和最适温度预测选择单独的特征。

    参数:
        dataset: 具有__getitem__方法的数据集对象
        growth_rate_k: 为生长速率预测选择的特征数量
        optimal_temperature_k: 为最适温度预测选择的特征数量
        method: 相关性方法 ('pearson', 'spearman', 或 'f_regression')
        max_samples: 为提高效率处理的最大样本数量

    返回:
        (growth_rate_selector, optimal_temperature_selector) 的元组
    """
    # 如果可用，从数据集获取特征名称
    if hasattr(dataset, 'get_feature_names'):
        feature_names = dataset.get_feature_names()
        logger.info(f"从数据集使用 {len(feature_names)} 个特征名称")
    else:
        feature_names = None

    # 收集所有特征和目标
    all_features = []
    growth_rates = []
    optimal_temperatures = []

    # 限制处理的样本数量
    num_samples = min(len(dataset), max_samples)
    logger.info(f"处理 {num_samples} 个样本进行特征选择")

    # 为提高效率批量处理样本
    batch_size = 10
    for i in range(0, num_samples, batch_size):
        batch_end = min(i + batch_size, num_samples)
        logger.info(f"处理样本 {i} 到 {batch_end-1}")

        for j in range(i, batch_end):
            try:
                features, targets = dataset[j]

                # 提取特征值
                if feature_names is None:
                    # 需要从第一个样本提取特征名称
                    if len(all_features) == 0:
                        feature_names = list(features.keys())
                        logger.info(f"从第一个样本提取了 {len(feature_names)} 个特征名称")

                # 创建特征向量
                feature_vector = []
                for key in feature_names:
                    if key in features:
                        value = features[key]
                        if isinstance(value, torch.Tensor):
                            # 将张量转换为numpy
                            value = value.detach().cpu().numpy()
                            if value.size > 1:
                                # 如果张量有多个元素，展平并添加每个元素
                                for k in range(value.size):
                                    feature_vector.append(value.flatten()[k])
                            else:
                                # 单值张量
                                feature_vector.append(value.item())
                        elif isinstance(value, (int, float, np.number)):
                            # 数值
                            feature_vector.append(float(value))
                        elif isinstance(value, np.ndarray) and value.dtype.kind in ['U', 'S']:
                            # 跳过字符串数组
                            feature_vector.append(0.0)
                        else:
                            # 尝试转换为浮点数，如果不可能，使用0.0
                            try:
                                feature_vector.append(float(value))
                            except (ValueError, TypeError):
                                feature_vector.append(0.0)
                    else:
                        # 缺失特征
                        feature_vector.append(0.0)

                # 将特征向量添加到列表
                all_features.append(np.array(feature_vector))

                # 获取目标值
                if 'growth_rate' in targets:
                    growth_rate = targets['growth_rate'].detach().cpu().numpy().item()
                    growth_rates.append(growth_rate)

                if 'optimal_temperature' in targets:
                    optimal_temperature = targets['optimal_temperature'].detach().cpu().numpy().item()
                    optimal_temperatures.append(optimal_temperature)
            except Exception as e:
                logger.warning(f"处理样本 {j} 时出错: {e}")

    # 转换为numpy数组
    if len(all_features) == 0:
        logger.error("未找到有效特征。请检查特征文件是否有错误。")
        return None, None

    X = np.array(all_features)
    growth_rate_y = np.array(growth_rates)
    optimal_temperature_y = np.array(optimal_temperatures)

    logger.info(f"收集了 {X.shape[0]} 个样本，包含 {X.shape[1]} 个特征")
    logger.info(f"找到 {len(growth_rates)} 个生长速率目标和 {len(optimal_temperatures)} 个最适温度目标")

    # 创建并拟合选择器
    growth_rate_selector = None
    optimal_temperature_selector = None

    if len(growth_rates) > 0:
        # 过滤具有生长速率目标的样本
        growth_rate_indices = np.arange(len(all_features))[np.arange(len(all_features)) < len(growth_rates)]
        growth_rate_X = X[growth_rate_indices]

        # 创建并拟合生长速率选择器
        growth_rate_selector = CorrelationFeatureSelector(method=method, k=growth_rate_k)
        growth_rate_selector.fit(growth_rate_X, growth_rate_y, feature_names)

        logger.info(f"为生长速率预测选择了 {len(growth_rate_selector.selected_indices)} 个特征")
        logger.info(f"生长速率的前5个特征: {growth_rate_selector.selected_feature_names[-5:]}")

    if len(optimal_temperatures) > 0:
        # 过滤具有最适温度目标的样本
        optimal_temperature_indices = np.arange(len(all_features))[np.arange(len(all_features)) < len(optimal_temperatures)]
        optimal_temperature_X = X[optimal_temperature_indices]

        # 创建并拟合最适温度选择器
        optimal_temperature_selector = CorrelationFeatureSelector(method=method, k=optimal_temperature_k)
        optimal_temperature_selector.fit(optimal_temperature_X, optimal_temperature_y, feature_names)

        logger.info(f"为最适温度预测选择了 {len(optimal_temperature_selector.selected_indices)} 个特征")
        logger.info(f"最适温度的前5个特征: {optimal_temperature_selector.selected_feature_names[-5:]}")

    return growth_rate_selector, optimal_temperature_selector
