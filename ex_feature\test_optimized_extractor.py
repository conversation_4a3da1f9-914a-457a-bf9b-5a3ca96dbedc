#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化的温度特征提取器

这个脚本用于测试优化的特征提取器是否正常工作，
包括模块导入、基本功能和错误处理。
"""

import os
import sys
import tempfile
import pandas as pd
from pathlib import Path

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        from optimized_extractor import OptimizedTemperatureExtractor
        print("✅ OptimizedTemperatureExtractor 导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_extractor_initialization():
    """测试提取器初始化"""
    print("\n=== 测试提取器初始化 ===")
    
    try:
        from optimized_extractor import OptimizedTemperatureExtractor
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            extractor = OptimizedTemperatureExtractor(
                num_threads=2,
                enable_preprocessing=False,  # 禁用预处理避免依赖问题
                enable_feature_selection=False,
                enable_scaling=False,
                output_dir=temp_dir,
                log_level='INFO'
            )
            
            print("✅ 提取器初始化成功")
            print(f"   输出目录: {extractor.output_dir}")
            print(f"   线程数: {extractor.num_threads}")
            print(f"   预处理: {extractor.enable_preprocessing}")
            print(f"   特征选择: {extractor.enable_feature_selection}")
            print(f"   数据缩放: {extractor.enable_scaling}")
            
            return True
            
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def create_test_genome_file(file_path: str):
    """创建测试基因组文件"""
    test_sequence = """
>test_genome
ATGCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCG
ATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGA
TCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGAT
CGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATC
GATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCGATCG
""".strip()
    
    with open(file_path, 'w') as f:
        f.write(test_sequence)

def test_single_genome_extraction():
    """测试单个基因组特征提取"""
    print("\n=== 测试单个基因组特征提取 ===")
    
    try:
        from optimized_extractor import OptimizedTemperatureExtractor
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            genome_file = os.path.join(temp_dir, "test_genome.fna")
            create_test_genome_file(genome_file)
            
            # 创建提取器
            extractor = OptimizedTemperatureExtractor(
                num_threads=1,
                enable_preprocessing=False,
                enable_feature_selection=False,
                enable_scaling=False,
                output_dir=temp_dir,
                log_level='WARNING'  # 减少日志输出
            )
            
            # 提取特征
            features = extractor.extract_single_genome(
                genome_id="test_genome",
                genome_file=genome_file,
                temperature=37.0
            )
            
            print("✅ 单个基因组特征提取成功")
            print(f"   基因组ID: test_genome")
            print(f"   特征数量: {len(features)}")
            print(f"   特征类型: {list(features.keys())[:5]}...")  # 显示前5个特征
            
            return True
            
    except Exception as e:
        print(f"❌ 单个基因组特征提取失败: {e}")
        return False

def test_batch_extraction():
    """测试批量特征提取"""
    print("\n=== 测试批量特征提取 ===")
    
    try:
        from optimized_extractor import OptimizedTemperatureExtractor
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试基因组文件
            genome_files = []
            for i in range(3):
                genome_file = os.path.join(temp_dir, f"genome_{i}.fna")
                create_test_genome_file(genome_file)
                genome_files.append(genome_file)
            
            # 创建元数据文件
            metadata_file = os.path.join(temp_dir, "metadata.tsv")
            metadata = pd.DataFrame({
                'genome_id': [f'genome_{i}' for i in range(3)],
                'genome_file': genome_files,
                'temperature': [25.0, 37.0, 55.0]
            })
            metadata.to_csv(metadata_file, sep='\t', index=False)
            
            # 创建提取器
            extractor = OptimizedTemperatureExtractor(
                num_threads=1,
                enable_preprocessing=False,
                enable_feature_selection=False,
                enable_scaling=False,
                output_dir=temp_dir,
                log_level='WARNING'
            )
            
            # 批量提取特征
            output_file = extractor.extract_batch(
                metadata_file=metadata_file,
                output_file="test_batch_features.npz"
            )
            
            print("✅ 批量特征提取成功")
            print(f"   元数据文件: {metadata_file}")
            print(f"   输出文件: {output_file}")
            print(f"   处理基因组数: 3")
            
            # 检查输出文件
            if os.path.exists(output_file):
                import numpy as np
                data = np.load(output_file, allow_pickle=True)
                print(f"   特征矩阵形状: {data['features'].shape}")
                print(f"   特征名称数量: {len(data['feature_names'])}")
            
            return True
            
    except Exception as e:
        print(f"❌ 批量特征提取失败: {e}")
        return False

def test_deepmu_integration():
    """测试DeepMu集成"""
    print("\n=== 测试DeepMu集成 ===")
    
    try:
        # 检查DeepMu utils是否可用
        deepmu_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'deepmu')
        if os.path.exists(deepmu_path):
            sys.path.insert(0, deepmu_path)
            
            try:
                from deepmu.utils import get_logger, DeepMuError
                print("✅ DeepMu utils导入成功")
                
                # 测试日志功能
                logger = get_logger("test")
                logger.info("测试日志消息")
                print("✅ DeepMu日志功能正常")
                
                return True
                
            except ImportError as e:
                print(f"⚠️  DeepMu utils导入失败: {e}")
                print("   优化提取器将使用简化功能")
                return False
        else:
            print("⚠️  DeepMu目录不存在")
            print("   优化提取器将使用简化功能")
            return False
            
    except Exception as e:
        print(f"❌ DeepMu集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 优化温度特征提取器测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("提取器初始化", test_extractor_initialization),
        ("单个基因组提取", test_single_genome_extraction),
        ("批量提取", test_batch_extraction),
        ("DeepMu集成", test_deepmu_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("🏁 测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20s}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！优化提取器工作正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
