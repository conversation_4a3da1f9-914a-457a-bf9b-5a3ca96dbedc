#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级基因组特征提取脚本

基于基因组序列和GFF注释文件提取高级特征：
1. 蛋白质特征 (氨基酸组成、理化性质、稳定性)
2. 代谢途径特征 (KEGG途径、酶系统)
3. 系统发育特征 (分类学信息、进化特征)
4. RNA特征 (tRNA/rRNA结构、稳定性)

结合已有的基础特征，生成完整的特征集用于温度预测。
"""

import os
import sys
import gzip
import pandas as pd
import numpy as np
import logging
from pathlib import Path
import argparse
from datetime import datetime
import tempfile

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入模块
try:
    from advanced_feature_extractor import AdvancedFeatureExtractor
    from simple_genomic_features import SimpleGenomicFeatures, SimpleCodonFeatures
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegratedFeatureExtractor:
    """集成的高级特征提取器"""
    
    def __init__(self, bacteria_dir: str, download_results_file: str, output_dir: str = "advanced_features"):
        """
        初始化提取器
        
        参数:
            bacteria_dir: Bacteria目录路径
            download_results_file: 下载结果TSV文件路径
            output_dir: 输出目录
        """
        self.bacteria_dir = Path(bacteria_dir)
        self.download_results_file = download_results_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化各种特征提取器
        self.advanced_extractor = AdvancedFeatureExtractor()
        self.genomic_extractor = SimpleGenomicFeatures()
        self.codon_extractor = SimpleCodonFeatures()
        
        # 读取下载结果
        self.download_results = self._load_download_results()
        
        logger.info(f"集成特征提取器初始化完成，找到 {len(self.download_results)} 个基因组记录")
    
    def _load_download_results(self) -> pd.DataFrame:
        """加载下载结果文件"""
        try:
            df = pd.read_csv(self.download_results_file, sep='\t')
            success_df = df[df['download_status'] == 'Success'].copy()
            logger.info(f"加载下载结果: {len(df)} 总记录, {len(success_df)} 成功下载")
            return success_df
        except Exception as e:
            logger.error(f"加载下载结果失败: {e}")
            return pd.DataFrame()
    
    def extract_all_advanced_features(self) -> pd.DataFrame:
        """提取所有基因组的高级特征"""
        logger.info("开始高级特征提取")
        
        all_features = []
        
        for idx, row in self.download_results.iterrows():
            try:
                taxid = row['species_taxonid']
                species_name = row['species_name']
                temperature = row['optimum_temperature_for_growth']
                download_path = row['download_path']
                
                logger.info(f"处理基因组 {taxid} ({species_name})")
                
                # 构建文件路径
                if download_path.startswith('Bacteria\\') or download_path.startswith('Bacteria/'):
                    relative_path = download_path[9:]
                else:
                    relative_path = download_path
                
                genome_dir = self.bacteria_dir / relative_path
                fasta_file = genome_dir / f"{taxid}_genomic.fna.gz"
                gff_file = genome_dir / f"{taxid}_genomic.gff.gz"
                
                # 检查文件是否存在
                if not fasta_file.exists() or not gff_file.exists():
                    logger.warning(f"文件不存在，跳过基因组 {taxid}")
                    continue
                
                # 提取特征
                features = self._extract_single_genome_advanced_features(
                    taxid=taxid,
                    species_name=species_name,
                    temperature=temperature,
                    fasta_file=str(fasta_file),
                    gff_file=str(gff_file),
                    row=row
                )
                
                if features:
                    all_features.append(features)
                    logger.info(f"成功提取 {len(features)} 个高级特征")
                
            except Exception as e:
                logger.error(f"处理基因组 {taxid} 失败: {e}")
                continue
        
        if all_features:
            features_df = pd.DataFrame(all_features)
            logger.info(f"高级特征提取完成: {len(features_df)} 个基因组, {len(features_df.columns)} 个特征")
            return features_df
        else:
            logger.error("未能提取任何高级特征")
            return pd.DataFrame()
    
    def _extract_single_genome_advanced_features(self, taxid: int, species_name: str, temperature: float,
                                               fasta_file: str, gff_file: str, row: pd.Series) -> dict:
        """提取单个基因组的高级特征"""
        try:
            features = {}
            
            # 基本信息
            features['taxid'] = taxid
            features['species_name'] = species_name
            features['temperature'] = temperature
            features['organism_type'] = row.get('organism_type.1', 'Bacteria')
            features['assembly_level'] = row.get('assembly_level', 'Unknown')
            features['quality_score'] = row.get('quality_score', 0)
            
            # 1. 基础基因组特征
            logger.debug("提取基础基因组特征")
            temp_fasta = self._decompress_file(fasta_file)
            if temp_fasta:
                genomic_features = self.genomic_extractor.extract_features(temp_fasta)
                for key, value in genomic_features.items():
                    features[f'genomic_{key}'] = value
                os.remove(temp_fasta)
            
            # 2. 读取基因组序列用于高级分析
            genome_sequence = self._read_genome_sequence(fasta_file)
            
            # 3. 提取蛋白质序列并分析
            logger.debug("提取蛋白质特征")
            protein_sequences = self._extract_protein_sequences(fasta_file, gff_file)
            protein_features = self.advanced_extractor.extract_protein_features(protein_sequences)
            for key, value in protein_features.items():
                features[f'protein_{key}'] = value
            
            # 4. 代谢途径特征
            logger.debug("提取代谢途径特征")
            pathway_features = self.advanced_extractor.extract_pathway_features(gff_file)
            for key, value in pathway_features.items():
                features[f'pathway_{key}'] = value
            
            # 5. RNA特征
            logger.debug("提取RNA特征")
            rna_features = self.advanced_extractor.extract_rna_features(gff_file, genome_sequence)
            for key, value in rna_features.items():
                features[f'rna_{key}'] = value
            
            # 6. 系统发育特征
            logger.debug("提取系统发育特征")
            phylo_features = self.advanced_extractor.extract_phylogenetic_features(species_name, taxid)
            for key, value in phylo_features.items():
                features[f'phylo_{key}'] = value
            
            # 7. 密码子特征
            logger.debug("提取密码子特征")
            cds_features = self._extract_cds_features(fasta_file, gff_file)
            for key, value in cds_features.items():
                features[f'cds_{key}'] = value
            
            return features
            
        except Exception as e:
            logger.error(f"提取基因组 {taxid} 高级特征失败: {e}")
            return None
    
    def _decompress_file(self, gz_file: str) -> str:
        """解压缩文件到临时文件"""
        try:
            temp_fd, temp_path = tempfile.mkstemp(suffix='.fasta')
            with gzip.open(gz_file, 'rt') as gz_f:
                with os.fdopen(temp_fd, 'w') as temp_f:
                    temp_f.write(gz_f.read())
            return temp_path
        except Exception as e:
            logger.error(f"解压文件失败 {gz_file}: {e}")
            return None
    
    def _read_genome_sequence(self, fasta_file: str) -> str:
        """读取基因组序列"""
        try:
            sequence = ""
            with gzip.open(fasta_file, 'rt') as f:
                for line in f:
                    if not line.startswith('>'):
                        sequence += line.strip().upper()
            return sequence
        except Exception as e:
            logger.error(f"读取基因组序列失败: {e}")
            return ""
    
    def _extract_protein_sequences(self, fasta_file: str, gff_file: str) -> list:
        """从基因组序列和GFF注释提取蛋白质序列"""
        try:
            # 读取基因组序列
            genome_sequence = self._read_genome_sequence(fasta_file)
            if not genome_sequence:
                return []
            
            protein_sequences = []
            
            # 从GFF文件提取CDS坐标
            with gzip.open(gff_file, 'rt') as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    
                    parts = line.strip().split('\t')
                    if len(parts) < 9:
                        continue
                    
                    if parts[2] == 'CDS':
                        start = int(parts[3]) - 1  # 转换为0-based
                        end = int(parts[4])
                        strand = parts[6]
                        
                        # 提取CDS序列
                        if start < len(genome_sequence) and end <= len(genome_sequence):
                            cds_seq = genome_sequence[start:end]
                            
                            if strand == '-':
                                cds_seq = self._reverse_complement(cds_seq)
                            
                            # 翻译为蛋白质序列
                            protein_seq = self._translate_dna(cds_seq)
                            if protein_seq and len(protein_seq) >= 20:  # 至少20个氨基酸
                                protein_sequences.append(protein_seq)
            
            logger.debug(f"提取了 {len(protein_sequences)} 个蛋白质序列")
            return protein_sequences
            
        except Exception as e:
            logger.error(f"提取蛋白质序列失败: {e}")
            return []
    
    def _translate_dna(self, dna_sequence: str) -> str:
        """将DNA序列翻译为蛋白质序列"""
        genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
            'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        protein = ""
        for i in range(0, len(dna_sequence) - 2, 3):
            codon = dna_sequence[i:i+3]
            if len(codon) == 3:
                aa = genetic_code.get(codon, 'X')
                if aa == '*':  # 终止密码子
                    break
                protein += aa
        
        return protein
    
    def _extract_cds_features(self, fasta_file: str, gff_file: str) -> dict:
        """提取CDS特征"""
        try:
            # 提取CDS序列
            cds_sequences = self._extract_cds_sequences(fasta_file, gff_file)
            
            if cds_sequences:
                # 创建临时CDS文件
                temp_cds_file = self._create_temp_cds_file(cds_sequences)
                
                if temp_cds_file:
                    # 使用密码子特征提取器
                    codon_features = self.codon_extractor.extract_features(temp_cds_file)
                    os.remove(temp_cds_file)
                    return codon_features
            
            return {}
            
        except Exception as e:
            logger.error(f"提取CDS特征失败: {e}")
            return {}
    
    def _extract_cds_sequences(self, fasta_file: str, gff_file: str) -> list:
        """提取CDS序列"""
        try:
            genome_sequence = self._read_genome_sequence(fasta_file)
            if not genome_sequence:
                return []
            
            cds_sequences = []
            
            with gzip.open(gff_file, 'rt') as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    
                    parts = line.strip().split('\t')
                    if len(parts) < 9:
                        continue
                    
                    if parts[2] == 'CDS':
                        start = int(parts[3]) - 1
                        end = int(parts[4])
                        strand = parts[6]
                        
                        if start < len(genome_sequence) and end <= len(genome_sequence):
                            cds_seq = genome_sequence[start:end]
                            
                            if strand == '-':
                                cds_seq = self._reverse_complement(cds_seq)
                            
                            if len(cds_seq) % 3 == 0 and len(cds_seq) >= 60:
                                cds_sequences.append(cds_seq)
            
            return cds_sequences
            
        except Exception as e:
            logger.error(f"提取CDS序列失败: {e}")
            return []
    
    def _create_temp_cds_file(self, cds_sequences: list) -> str:
        """创建临时CDS文件"""
        try:
            temp_fd, temp_path = tempfile.mkstemp(suffix='.fasta')
            with os.fdopen(temp_fd, 'w') as f:
                for i, seq in enumerate(cds_sequences):
                    f.write(f">CDS_{i+1}\n{seq}\n")
            return temp_path
        except Exception as e:
            logger.error(f"创建临时CDS文件失败: {e}")
            return None
    
    def _reverse_complement(self, sequence: str) -> str:
        """计算反向互补序列"""
        complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G', 'N': 'N'}
        return ''.join(complement.get(base, base) for base in reversed(sequence))

    def save_advanced_features(self, features_df: pd.DataFrame, filename: str = "advanced_genome_features.tsv"):
        """保存高级特征到TSV文件"""
        try:
            output_file = self.output_dir / filename
            features_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')

            logger.info(f"高级特征已保存到: {output_file}")
            logger.info(f"数据形状: {features_df.shape}")

            return str(output_file)

        except Exception as e:
            logger.error(f"保存高级特征失败: {e}")
            return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="高级基因组特征提取器")

    parser.add_argument('--bacteria-dir',
                       default='../temp_data/high_quality/resume_test/Bacteria',
                       help='Bacteria目录路径')
    parser.add_argument('--download-results',
                       default='../temp_data/high_quality/resume_test/download_results.tsv',
                       help='下载结果TSV文件路径')
    parser.add_argument('--output-dir',
                       default='advanced_features',
                       help='输出目录')
    parser.add_argument('--output-file',
                       default='advanced_genome_features.tsv',
                       help='输出文件名')
    parser.add_argument('--log-level',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO',
                       help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    try:
        # 创建集成特征提取器
        extractor = IntegratedFeatureExtractor(
            bacteria_dir=args.bacteria_dir,
            download_results_file=args.download_results,
            output_dir=args.output_dir
        )

        # 提取高级特征
        logger.info("开始高级特征提取...")
        features_df = extractor.extract_all_advanced_features()

        if features_df.empty:
            logger.error("未能提取任何高级特征")
            return 1

        # 保存结果
        output_file = extractor.save_advanced_features(features_df, args.output_file)

        if output_file:
            logger.info("✅ 高级特征提取完成!")
            logger.info(f"   基因组数量: {len(features_df)}")
            logger.info(f"   总特征数量: {len(features_df.columns) - 6}")
            logger.info(f"   输出文件: {output_file}")
            return 0
        else:
            logger.error("保存高级特征失败")
            return 1

    except Exception as e:
        logger.error(f"高级特征提取失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

    def save_advanced_features(self, features_df: pd.DataFrame, filename: str = "advanced_genome_features.tsv"):
        """保存高级特征到TSV文件"""
        try:
            output_file = self.output_dir / filename
            features_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')

            logger.info(f"高级特征已保存到: {output_file}")
            logger.info(f"数据形状: {features_df.shape}")

            # 生成特征统计报告
            self._generate_advanced_feature_report(features_df, output_file)

            return str(output_file)

        except Exception as e:
            logger.error(f"保存高级特征失败: {e}")
            return None

    def _generate_advanced_feature_report(self, features_df: pd.DataFrame, output_file: Path):
        """生成高级特征统计报告"""
        try:
            report_file = output_file.with_suffix('.md')

            # 计算特征类别统计
            feature_categories = {
                'genomic': len([col for col in features_df.columns if col.startswith('genomic_')]),
                'protein': len([col for col in features_df.columns if col.startswith('protein_')]),
                'pathway': len([col for col in features_df.columns if col.startswith('pathway_')]),
                'rna': len([col for col in features_df.columns if col.startswith('rna_')]),
                'phylo': len([col for col in features_df.columns if col.startswith('phylo_')]),
                'cds': len([col for col in features_df.columns if col.startswith('cds_')])
            }

            total_features = sum(feature_categories.values())

            report_content = f"""# 高级基因组特征提取报告

## 提取概览
- 提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {len(features_df)}
- 总特征数量: {total_features}
- 数据来源: temp_data/high_quality/resume_test/Bacteria

## 特征类别分布

### 1. 基因组基础特征 ({feature_categories['genomic']} 个)
- GC含量、核苷酸组成
- 序列复杂度、k-mer多样性
- 重复序列分析

### 2. 蛋白质特征 ({feature_categories['protein']} 个)
- 氨基酸组成和频率
- 理化性质 (分子量、疏水性、电荷)
- 蛋白质稳定性指标
- 温度敏感性分析

### 3. 代谢途径特征 ({feature_categories['pathway']} 个)
- 酶系统分析 (EC分类)
- 代谢多样性
- 温度相关代谢模块
- COG功能分类

### 4. RNA特征 ({feature_categories['rna']} 个)
- tRNA/rRNA数量和长度
- RNA GC含量
- RNA结构稳定性
- 修饰碱基潜力

### 5. 系统发育特征 ({feature_categories['phylo']} 个)
- 分类学层级信息
- 进化距离特征
- 系统发育保守性
- 生态位特征

### 6. 密码子特征 ({feature_categories['cds']} 个)
- 密码子使用频率
- 密码子偏好性
- GC含量在密码子位置

## 特征详细说明

### 蛋白质特征亮点
- **氨基酸组成**: 20种氨基酸的频率分布
- **理化性质**: 平均分子量、疏水性、电荷分布
- **稳定性指标**: 半胱氨酸、脯氨酸、甘氨酸含量
- **温度适应**: 嗜热/嗜冷氨基酸比例、二硫键潜力

### 代谢途径特征亮点
- **酶分类**: 6大类酶系统的分布
- **代谢多样性**: 酶种类、KEGG途径、COG分类多样性
- **应激响应**: 热激蛋白、冷激蛋白
- **核心代谢**: 能量代谢、氨基酸代谢比例

### RNA特征亮点
- **结构RNA**: tRNA和rRNA的数量、长度、GC含量
- **稳定性**: RNA二级结构稳定性评分
- **修饰**: 潜在修饰位点分析

### 系统发育特征亮点
- **分类信息**: 属多样性、分类学深度
- **进化位置**: 古老群体vs现代群体
- **生态适应**: 极端环境、病原性、海洋/土壤适应

## 数据质量评估

### 优势
1. **全面性**: 涵盖基因组、蛋白质、代谢、RNA、进化多个层面
2. **生物学相关性**: 所有特征都有明确的生物学意义
3. **温度适应性**: 专门设计了温度相关的特征
4. **可解释性**: 每个特征都可以从生物学角度解释

### 特征工程价值
1. **多层次分析**: 从DNA到蛋白质到代谢的完整链条
2. **功能注释**: 结合GFF注释提取功能相关特征
3. **进化视角**: 考虑系统发育和进化适应
4. **结构信息**: 包含RNA和蛋白质结构稳定性

## 应用建议

### 1. 温度预测模型
- 使用所有{total_features}个特征进行建模
- 重点关注蛋白质和代谢途径特征
- 结合系统发育信息提高预测精度

### 2. 特征选择策略
- 蛋白质特征: 氨基酸组成、稳定性指标
- 代谢特征: 酶多样性、应激响应
- RNA特征: 结构稳定性
- 进化特征: 生态适应性

### 3. 生物学解释
- 蛋白质稳定性与温度适应的关系
- 代谢途径与环境适应的关联
- RNA结构与温度稳定性的相关性
- 系统发育与温度偏好的进化模式

## 输出文件
- **特征数据**: {output_file.name}
- **分析报告**: {report_file.name}

---
*报告由高级基因组特征提取器自动生成*
"""

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)

            logger.info(f"高级特征报告已生成: {report_file}")

        except Exception as e:
            logger.error(f"生成高级特征报告失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="高级基因组特征提取器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
    # 基本用法
    python extract_advanced_features.py

    # 指定输出目录
    python extract_advanced_features.py --output-dir advanced_features

    # 指定数据路径
    python extract_advanced_features.py --bacteria-dir /path/to/Bacteria --download-results /path/to/results.tsv
        """
    )

    parser.add_argument('--bacteria-dir',
                       default='../temp_data/high_quality/resume_test/Bacteria',
                       help='Bacteria目录路径')
    parser.add_argument('--download-results',
                       default='../temp_data/high_quality/resume_test/download_results.tsv',
                       help='下载结果TSV文件路径')
    parser.add_argument('--output-dir',
                       default='advanced_features',
                       help='输出目录')
    parser.add_argument('--output-file',
                       default='advanced_genome_features.tsv',
                       help='输出文件名')
    parser.add_argument('--log-level',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO',
                       help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # 检查输入路径
    if not os.path.exists(args.bacteria_dir):
        logger.error(f"Bacteria目录不存在: {args.bacteria_dir}")
        return 1

    if not os.path.exists(args.download_results):
        logger.error(f"下载结果文件不存在: {args.download_results}")
        return 1

    try:
        # 创建集成特征提取器
        extractor = IntegratedFeatureExtractor(
            bacteria_dir=args.bacteria_dir,
            download_results_file=args.download_results,
            output_dir=args.output_dir
        )

        # 提取高级特征
        logger.info("开始高级特征提取...")
        features_df = extractor.extract_all_advanced_features()

        if features_df.empty:
            logger.error("未能提取任何高级特征")
            return 1

        # 保存结果
        output_file = extractor.save_advanced_features(features_df, args.output_file)

        if output_file:
            logger.info("✅ 高级特征提取完成!")
            logger.info(f"   基因组数量: {len(features_df)}")
            logger.info(f"   总特征数量: {len(features_df.columns) - 6}")
            logger.info(f"   输出文件: {output_file}")

            # 显示特征类别统计
            feature_categories = {
                'genomic': len([col for col in features_df.columns if col.startswith('genomic_')]),
                'protein': len([col for col in features_df.columns if col.startswith('protein_')]),
                'pathway': len([col for col in features_df.columns if col.startswith('pathway_')]),
                'rna': len([col for col in features_df.columns if col.startswith('rna_')]),
                'phylo': len([col for col in features_df.columns if col.startswith('phylo_')]),
                'cds': len([col for col in features_df.columns if col.startswith('cds_')])
            }

            logger.info("   特征类别分布:")
            for category, count in feature_categories.items():
                logger.info(f"     {category}: {count} 个特征")

            return 0
        else:
            logger.error("保存高级特征失败")
            return 1

    except Exception as e:
        logger.error(f"高级特征提取失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
