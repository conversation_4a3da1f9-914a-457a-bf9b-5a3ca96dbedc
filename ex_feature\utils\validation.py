"""
特征验证器

提供特征质量验证和数据完整性检查功能。

主要功能：
1. 特征值范围验证
2. 数据类型检查
3. 缺失值检测
4. 异常值识别
5. 特征分布分析
6. 质量评分
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from collections import defaultdict
import warnings

logger = logging.getLogger(__name__)

class FeatureValidator:
    """
    特征验证器
    
    验证提取的特征的质量和完整性。
    """
    
    def __init__(self, 
                 strict_mode: bool = False,
                 outlier_threshold: float = 3.0,
                 missing_threshold: float = 0.1):
        """
        初始化特征验证器
        
        参数:
            strict_mode: 是否启用严格模式
            outlier_threshold: 异常值检测阈值（标准差倍数）
            missing_threshold: 缺失值比例阈值
        """
        self.strict_mode = strict_mode
        self.outlier_threshold = outlier_threshold
        self.missing_threshold = missing_threshold
        
        # 预定义的特征范围
        self.feature_ranges = {
            # 密码子特征
            'codon_diversity': (0, 10),
            'gc_content': (0, 1),
            'thermostability_ratio': (0, 100),
            
            # 基因组特征
            'genome_size': (100000, 20000000),  # 100kb - 20Mb
            'gc_skew': (-1, 1),
            'sequence_entropy': (0, 2),
            
            # 蛋白质特征
            'avg_molecular_weight': (10000, 200000),
            'avg_isoelectric_point': (3, 12),
            'instability_index': (0, 100),
            
            # 通用范围
            'frequency': (0, 1),
            'ratio': (0, 10),
            'percentage': (0, 100)
        }
        
        logger.info("特征验证器初始化完成")
    
    def validate_features(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证特征字典
        
        参数:
            features: 特征字典
            
        返回:
            验证结果字典
        """
        validation_result = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'statistics': {},
            'quality_score': 0.0,
            'feature_issues': {}
        }
        
        if not features:
            validation_result['valid'] = False
            validation_result['errors'].append("特征字典为空")
            return validation_result
        
        # 过滤掉元数据字段
        feature_data = {k: v for k, v in features.items() if not k.startswith('_')}
        
        if not feature_data:
            validation_result['valid'] = False
            validation_result['errors'].append("没有有效的特征数据")
            return validation_result
        
        # 执行各种验证
        validation_result.update(self._validate_data_types(feature_data))
        validation_result.update(self._validate_value_ranges(feature_data))
        validation_result.update(self._detect_missing_values(feature_data))
        validation_result.update(self._detect_outliers(feature_data))
        validation_result.update(self._analyze_feature_distribution(feature_data))
        
        # 计算质量评分
        validation_result['quality_score'] = self._calculate_quality_score(validation_result)
        
        # 生成统计信息
        validation_result['statistics'] = self._generate_statistics(feature_data)
        
        logger.debug(f"特征验证完成: 质量评分 {validation_result['quality_score']:.3f}")
        
        return validation_result
    
    def _validate_data_types(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """验证数据类型"""
        result = {'type_issues': []}
        
        for key, value in features.items():
            if value is None:
                result['type_issues'].append(f"{key}: 值为None")
            elif isinstance(value, str):
                # 字符串值可能表示分类特征
                if self.strict_mode:
                    result['type_issues'].append(f"{key}: 字符串值在数值特征中")
            elif isinstance(value, (int, float, np.number)):
                # 检查是否为有效数值
                if np.isnan(value):
                    result['type_issues'].append(f"{key}: NaN值")
                elif np.isinf(value):
                    result['type_issues'].append(f"{key}: 无穷值")
            else:
                result['type_issues'].append(f"{key}: 未知数据类型 {type(value)}")
        
        return result
    
    def _validate_value_ranges(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """验证数值范围"""
        result = {'range_issues': []}
        
        for key, value in features.items():
            if not isinstance(value, (int, float, np.number)) or np.isnan(value) or np.isinf(value):
                continue
            
            # 检查预定义范围
            range_found = False
            for pattern, (min_val, max_val) in self.feature_ranges.items():
                if pattern in key.lower():
                    if not (min_val <= value <= max_val):
                        result['range_issues'].append(
                            f"{key}: 值 {value} 超出预期范围 [{min_val}, {max_val}]"
                        )
                    range_found = True
                    break
            
            # 通用范围检查
            if not range_found:
                if value < -1e6 or value > 1e6:
                    result['range_issues'].append(f"{key}: 值 {value} 可能过大或过小")
        
        return result
    
    def _detect_missing_values(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """检测缺失值"""
        result = {'missing_issues': []}
        
        missing_count = 0
        total_count = len(features)
        
        for key, value in features.items():
            if value is None or (isinstance(value, float) and np.isnan(value)):
                missing_count += 1
                result['missing_issues'].append(f"{key}: 缺失值")
        
        missing_ratio = missing_count / total_count if total_count > 0 else 0
        
        if missing_ratio > self.missing_threshold:
            result['missing_issues'].append(
                f"缺失值比例过高: {missing_ratio:.3f} > {self.missing_threshold}"
            )
        
        result['missing_ratio'] = missing_ratio
        
        return result
    
    def _detect_outliers(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """检测异常值"""
        result = {'outlier_issues': []}
        
        # 收集数值特征
        numeric_values = []
        for key, value in features.items():
            if isinstance(value, (int, float, np.number)) and not (np.isnan(value) or np.isinf(value)):
                numeric_values.append(value)
        
        if len(numeric_values) < 3:
            return result
        
        # 计算统计量
        mean_val = np.mean(numeric_values)
        std_val = np.std(numeric_values)
        
        if std_val == 0:
            return result
        
        # 检测异常值
        for key, value in features.items():
            if isinstance(value, (int, float, np.number)) and not (np.isnan(value) or np.isinf(value)):
                z_score = abs(value - mean_val) / std_val
                
                if z_score > self.outlier_threshold:
                    result['outlier_issues'].append(
                        f"{key}: 可能的异常值 {value} (Z-score: {z_score:.2f})"
                    )
        
        return result
    
    def _analyze_feature_distribution(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """分析特征分布"""
        result = {'distribution_issues': []}
        
        # 收集数值特征
        numeric_features = {}
        for key, value in features.items():
            if isinstance(value, (int, float, np.number)) and not (np.isnan(value) or np.isinf(value)):
                numeric_features[key] = value
        
        if len(numeric_features) < 2:
            return result
        
        values = list(numeric_features.values())
        
        # 检查值的多样性
        unique_values = len(set(values))
        total_values = len(values)
        
        if unique_values == 1:
            result['distribution_issues'].append("所有特征值相同，缺乏多样性")
        elif unique_values / total_values < 0.1:
            result['distribution_issues'].append("特征值多样性较低")
        
        # 检查零值比例
        zero_count = sum(1 for v in values if v == 0)
        zero_ratio = zero_count / total_values
        
        if zero_ratio > 0.5:
            result['distribution_issues'].append(f"零值比例过高: {zero_ratio:.3f}")
        
        result['zero_ratio'] = zero_ratio
        result['diversity_ratio'] = unique_values / total_values
        
        return result
    
    def _calculate_quality_score(self, validation_result: Dict[str, Any]) -> float:
        """计算质量评分（0-1）"""
        score = 1.0
        
        # 错误严重扣分
        error_count = len(validation_result.get('errors', []))
        score -= error_count * 0.2
        
        # 类型问题扣分
        type_issues = len(validation_result.get('type_issues', []))
        score -= type_issues * 0.1
        
        # 范围问题扣分
        range_issues = len(validation_result.get('range_issues', []))
        score -= range_issues * 0.05
        
        # 缺失值扣分
        missing_ratio = validation_result.get('missing_ratio', 0)
        score -= missing_ratio * 0.3
        
        # 异常值扣分
        outlier_issues = len(validation_result.get('outlier_issues', []))
        score -= outlier_issues * 0.02
        
        # 分布问题扣分
        distribution_issues = len(validation_result.get('distribution_issues', []))
        score -= distribution_issues * 0.1
        
        # 多样性奖励
        diversity_ratio = validation_result.get('diversity_ratio', 0)
        score += diversity_ratio * 0.1
        
        return max(0.0, min(1.0, score))
    
    def _generate_statistics(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """生成特征统计信息"""
        stats = {
            'total_features': len(features),
            'numeric_features': 0,
            'string_features': 0,
            'missing_features': 0,
            'zero_features': 0
        }
        
        numeric_values = []
        
        for key, value in features.items():
            if value is None or (isinstance(value, float) and np.isnan(value)):
                stats['missing_features'] += 1
            elif isinstance(value, str):
                stats['string_features'] += 1
            elif isinstance(value, (int, float, np.number)):
                stats['numeric_features'] += 1
                if not (np.isnan(value) or np.isinf(value)):
                    numeric_values.append(value)
                    if value == 0:
                        stats['zero_features'] += 1
        
        # 数值特征统计
        if numeric_values:
            stats['min_value'] = min(numeric_values)
            stats['max_value'] = max(numeric_values)
            stats['mean_value'] = np.mean(numeric_values)
            stats['std_value'] = np.std(numeric_values)
            stats['median_value'] = np.median(numeric_values)
        
        return stats
    
    def validate_batch(self, feature_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量验证特征
        
        参数:
            feature_list: 特征字典列表
            
        返回:
            批量验证结果
        """
        batch_result = {
            'total_samples': len(feature_list),
            'valid_samples': 0,
            'invalid_samples': 0,
            'average_quality_score': 0.0,
            'common_issues': defaultdict(int),
            'sample_results': []
        }
        
        quality_scores = []
        
        for i, features in enumerate(feature_list):
            result = self.validate_features(features)
            batch_result['sample_results'].append(result)
            
            if result['valid']:
                batch_result['valid_samples'] += 1
            else:
                batch_result['invalid_samples'] += 1
            
            quality_scores.append(result['quality_score'])
            
            # 统计常见问题
            for issue_type in ['type_issues', 'range_issues', 'missing_issues', 
                             'outlier_issues', 'distribution_issues']:
                issues = result.get(issue_type, [])
                for issue in issues:
                    batch_result['common_issues'][issue] += 1
        
        if quality_scores:
            batch_result['average_quality_score'] = np.mean(quality_scores)
            batch_result['min_quality_score'] = min(quality_scores)
            batch_result['max_quality_score'] = max(quality_scores)
        
        return batch_result
