#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的基因组特征提取器

不依赖复杂配置，提供基本的基因组特征提取功能。
"""

import os
import numpy as np
from typing import Dict, List
from collections import Counter

class SimpleGenomicFeatures:
    """简化的基因组特征提取器"""
    
    def __init__(self):
        """初始化"""
        self.window_size = 1000
    
    def extract_features(self, genome_file: str) -> Dict[str, float]:
        """
        提取基因组特征
        
        参数:
            genome_file: 基因组文件路径
            
        返回:
            特征字典
        """
        try:
            # 读取序列
            sequence = self._read_fasta(genome_file)
            if not sequence:
                return {}
            
            features = {}
            
            # 基本统计特征
            features.update(self._extract_basic_stats(sequence))
            
            # GC含量特征
            features.update(self._extract_gc_features(sequence))
            
            # 核苷酸组成特征
            features.update(self._extract_nucleotide_composition(sequence))
            
            # 二核苷酸特征
            features.update(self._extract_dinucleotide_features(sequence))
            
            # 序列复杂度特征
            features.update(self._extract_complexity_features(sequence))
            
            return features
            
        except Exception as e:
            print(f"提取基因组特征失败: {e}")
            return {}
    
    def _read_fasta(self, file_path: str) -> str:
        """读取FASTA文件"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # 提取序列（去除FASTA头）
            lines = content.strip().split('\n')
            sequence = ''.join(line for line in lines if not line.startswith('>'))
            return sequence.upper()
            
        except Exception as e:
            print(f"读取FASTA文件失败: {e}")
            return ""
    
    def _extract_basic_stats(self, sequence: str) -> Dict[str, float]:
        """提取基本统计特征"""
        features = {}
        
        features['genome_size'] = len(sequence)
        features['n_content'] = sequence.count('N') / len(sequence) if len(sequence) > 0 else 0
        
        return features
    
    def _extract_gc_features(self, sequence: str) -> Dict[str, float]:
        """提取GC含量特征"""
        features = {}
        
        if len(sequence) == 0:
            return {'gc_content': 0, 'at_content': 0}
        
        # 整体GC含量
        gc_count = sequence.count('G') + sequence.count('C')
        at_count = sequence.count('A') + sequence.count('T')
        total_bases = gc_count + at_count
        
        if total_bases > 0:
            features['gc_content'] = gc_count / total_bases
            features['at_content'] = at_count / total_bases
        else:
            features['gc_content'] = 0
            features['at_content'] = 0
        
        # 滑动窗口GC含量分析
        gc_values = []
        for i in range(0, len(sequence) - self.window_size + 1, self.window_size // 2):
            window = sequence[i:i + self.window_size]
            if len(window) == self.window_size:
                w_gc = window.count('G') + window.count('C')
                w_at = window.count('A') + window.count('T')
                w_total = w_gc + w_at
                if w_total > 0:
                    gc_values.append(w_gc / w_total)
        
        if gc_values:
            features['gc_mean'] = np.mean(gc_values)
            features['gc_std'] = np.std(gc_values)
            features['gc_min'] = min(gc_values)
            features['gc_max'] = max(gc_values)
            features['gc_range'] = features['gc_max'] - features['gc_min']
        else:
            features['gc_mean'] = features['gc_content']
            features['gc_std'] = 0
            features['gc_min'] = features['gc_content']
            features['gc_max'] = features['gc_content']
            features['gc_range'] = 0
        
        # GC偏斜
        g_count = sequence.count('G')
        c_count = sequence.count('C')
        if gc_count > 0:
            features['gc_skew'] = (g_count - c_count) / gc_count
        else:
            features['gc_skew'] = 0
        
        return features
    
    def _extract_nucleotide_composition(self, sequence: str) -> Dict[str, float]:
        """提取核苷酸组成特征"""
        features = {}
        
        total_length = len(sequence)
        if total_length == 0:
            return {'a_freq': 0, 't_freq': 0, 'g_freq': 0, 'c_freq': 0}
        
        # 单核苷酸频率
        for base in 'ATGC':
            features[f'{base.lower()}_freq'] = sequence.count(base) / total_length
        
        return features
    
    def _extract_dinucleotide_features(self, sequence: str) -> Dict[str, float]:
        """提取二核苷酸特征"""
        features = {}
        
        # 二核苷酸频率
        dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TA', 'TT', 'TG', 'TC',
                        'GA', 'GT', 'GG', 'GC', 'CA', 'CT', 'CG', 'CC']
        
        for dinuc in dinucleotides:
            count = 0
            for i in range(len(sequence) - 1):
                if sequence[i:i+2] == dinuc:
                    count += 1
            features[f'dinuc_{dinuc.lower()}'] = count / (len(sequence) - 1) if len(sequence) > 1 else 0
        
        # CpG含量
        cpg_count = 0
        for i in range(len(sequence) - 1):
            if sequence[i:i+2] == 'CG':
                cpg_count += 1
        features['cpg_content'] = cpg_count / (len(sequence) - 1) if len(sequence) > 1 else 0
        
        return features
    
    def _extract_complexity_features(self, sequence: str) -> Dict[str, float]:
        """提取序列复杂度特征"""
        features = {}
        
        # 语言复杂度（基于k-mer多样性）
        for k in [2, 3, 4]:
            kmers = []
            for i in range(len(sequence) - k + 1):
                kmer = sequence[i:i+k]
                if 'N' not in kmer:
                    kmers.append(kmer)
            
            if kmers:
                unique_kmers = len(set(kmers))
                total_kmers = len(kmers)
                max_possible = 4 ** k
                
                features[f'kmer{k}_diversity'] = unique_kmers / max_possible
                features[f'kmer{k}_complexity'] = unique_kmers / total_kmers if total_kmers > 0 else 0
            else:
                features[f'kmer{k}_diversity'] = 0
                features[f'kmer{k}_complexity'] = 0
        
        # 序列熵
        if len(sequence) > 0:
            base_counts = Counter(sequence)
            total = sum(base_counts.values())
            entropy = 0
            for count in base_counts.values():
                if count > 0:
                    p = count / total
                    entropy -= p * np.log2(p)
            features['sequence_entropy'] = entropy
        else:
            features['sequence_entropy'] = 0
        
        # 重复序列检测
        repeat_patterns = ['A' * 10, 'T' * 10, 'G' * 10, 'C' * 10,
                          'AT' * 5, 'GC' * 5, 'AG' * 5, 'CT' * 5]
        
        total_repeats = 0
        for pattern in repeat_patterns:
            count = sequence.count(pattern)
            total_repeats += count * len(pattern)
        
        features['simple_repeat_content'] = total_repeats / len(sequence) if len(sequence) > 0 else 0
        
        return features

class SimpleCodonFeatures:
    """简化的密码子特征提取器"""
    
    def __init__(self):
        """初始化"""
        pass
    
    def extract_features(self, cds_file: str) -> Dict[str, float]:
        """
        提取密码子特征
        
        参数:
            cds_file: CDS文件路径
            
        返回:
            特征字典
        """
        try:
            # 读取CDS序列
            sequences = self._read_fasta_multiple(cds_file)
            if not sequences:
                return {}
            
            features = {}
            
            # 合并所有CDS序列
            all_cds = ''.join(sequences)
            
            # 密码子使用频率
            features.update(self._extract_codon_usage(all_cds))
            
            # 密码子偏好性
            features.update(self._extract_codon_bias(all_cds))
            
            return features
            
        except Exception as e:
            print(f"提取密码子特征失败: {e}")
            return {}
    
    def _read_fasta_multiple(self, file_path: str) -> List[str]:
        """读取多序列FASTA文件"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            sequences = []
            current_seq = ""
            
            for line in content.strip().split('\n'):
                if line.startswith('>'):
                    if current_seq:
                        sequences.append(current_seq.upper())
                        current_seq = ""
                else:
                    current_seq += line
            
            if current_seq:
                sequences.append(current_seq.upper())
            
            # 过滤长度为3的倍数的序列
            valid_sequences = [seq for seq in sequences if len(seq) % 3 == 0]
            
            return valid_sequences
            
        except Exception as e:
            print(f"读取CDS文件失败: {e}")
            return []
    
    def _extract_codon_usage(self, cds_sequence: str) -> Dict[str, float]:
        """提取密码子使用频率"""
        features = {}
        
        # 统计密码子
        codon_counts = Counter()
        for i in range(0, len(cds_sequence), 3):
            codon = cds_sequence[i:i+3]
            if len(codon) == 3 and all(base in 'ATGC' for base in codon):
                codon_counts[codon] += 1
        
        total_codons = sum(codon_counts.values())
        
        if total_codons == 0:
            return features
        
        # 计算频率
        for codon, count in codon_counts.items():
            features[f'codon_{codon.lower()}_freq'] = count / total_codons
        
        return features
    
    def _extract_codon_bias(self, cds_sequence: str) -> Dict[str, float]:
        """提取密码子偏好性特征"""
        features = {}
        
        # GC含量在密码子位置
        gc1_count = gc2_count = gc3_count = 0
        total_positions = 0
        
        for i in range(0, len(cds_sequence), 3):
            codon = cds_sequence[i:i+3]
            if len(codon) == 3:
                if codon[0] in 'GC': gc1_count += 1
                if codon[1] in 'GC': gc2_count += 1
                if codon[2] in 'GC': gc3_count += 1
                total_positions += 1
        
        if total_positions > 0:
            features['gc1'] = gc1_count / total_positions
            features['gc2'] = gc2_count / total_positions
            features['gc3'] = gc3_count / total_positions
        else:
            features['gc1'] = 0
            features['gc2'] = 0
            features['gc3'] = 0
        
        return features
