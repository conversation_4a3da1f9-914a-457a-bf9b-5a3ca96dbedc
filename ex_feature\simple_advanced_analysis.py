#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的高级特征分析器

快速分析高级基因组特征与温度的关系
"""

import os
import sys
import pandas as pd
import numpy as np
import argparse
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_advanced_features(features_file: str, output_dir: str = "simple_advanced_analysis"):
    """分析高级特征"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载数据
    logger.info("加载高级特征数据")
    df = pd.read_csv(features_file, sep='\t')
    logger.info(f"加载了 {len(df)} 个基因组，{len(df.columns)} 个特征")
    
    # 分类特征
    feature_categories = {
        'genomic': [col for col in df.columns if col.startswith('genomic_')],
        'protein': [col for col in df.columns if col.startswith('protein_')],
        'pathway': [col for col in df.columns if col.startswith('pathway_')],
        'rna': [col for col in df.columns if col.startswith('rna_')],
        'phylo': [col for col in df.columns if col.startswith('phylo_')],
        'cds': [col for col in df.columns if col.startswith('cds_')],
        'meta': ['taxid', 'species_name', 'temperature', 'organism_type', 'assembly_level', 'quality_score']
    }
    
    all_features = [col for col in df.columns if col not in feature_categories['meta']]
    
    logger.info("特征类别分布:")
    for category, features in feature_categories.items():
        if category != 'meta':
            logger.info(f"  {category}: {len(features)} 个特征")
    
    # 计算相关性
    logger.info("计算特征与温度的相关性")
    correlations = []
    
    for feature in all_features:
        try:
            corr = np.corrcoef(df[feature], df['temperature'])[0, 1]
            
            if not np.isnan(corr):
                # 确定特征类别
                category = 'other'
                for cat_name, cat_features in feature_categories.items():
                    if feature in cat_features:
                        category = cat_name
                        break
                
                correlations.append({
                    'feature': feature,
                    'correlation': corr,
                    'abs_correlation': abs(corr),
                    'category': category
                })
        except:
            continue
    
    # 转换为DataFrame并排序
    corr_df = pd.DataFrame(correlations)
    corr_df = corr_df.sort_values('abs_correlation', ascending=False)
    
    # 保存相关性结果
    output_file = os.path.join(output_dir, "advanced_feature_correlations.tsv")
    corr_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
    logger.info(f"相关性分析结果保存到: {output_file}")
    
    # 按类别统计
    logger.info("按类别分析特征性能")
    category_stats = {}
    
    for category in ['genomic', 'protein', 'pathway', 'rna', 'phylo', 'cds']:
        cat_features = corr_df[corr_df['category'] == category]
        
        if len(cat_features) > 0:
            stats = {
                'count': len(cat_features),
                'mean_correlation': cat_features['abs_correlation'].mean(),
                'max_correlation': cat_features['abs_correlation'].max(),
                'top_feature': cat_features.iloc[0]['feature'],
                'top_correlation': cat_features.iloc[0]['correlation']
            }
            category_stats[category] = stats
    
    # 生成报告
    logger.info("生成分析报告")
    
    report_content = f"""# 高级基因组特征分析报告

## 分析概览
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {len(df)}
- 总特征数量: {len(all_features)}
- 温度范围: {df['temperature'].min():.1f}°C - {df['temperature'].max():.1f}°C

## 特征类别性能排名

| 排名 | 特征类别 | 特征数量 | 平均相关性 | 最大相关性 | 最佳特征 |
|------|----------|----------|------------|------------|----------|
"""
    
    # 按平均相关性排序
    sorted_categories = sorted(category_stats.items(), 
                             key=lambda x: x[1]['mean_correlation'], reverse=True)
    
    for rank, (category, stats) in enumerate(sorted_categories, 1):
        best_feature = stats['top_feature'].replace(f'{category}_', '')
        report_content += f"| {rank} | **{category.title()}** | {stats['count']} | {stats['mean_correlation']:.4f} | {stats['max_correlation']:.4f} | {best_feature} |\n"
    
    report_content += f"""
## 前20个最重要特征

| 排名 | 特征名称 | 相关系数 | 特征类别 |
|------|----------|----------|----------|
"""
    
    for i, (_, row) in enumerate(corr_df.head(20).iterrows(), 1):
        feature_name = row['feature']
        correlation = row['correlation']
        category = row['category']
        
        short_name = feature_name.replace(f'{category}_', '') if category != 'other' else feature_name
        report_content += f"| {i} | {short_name} | {correlation:+.4f} | {category} |\n"
    
    # 各类别最佳特征
    report_content += f"""
## 各类别最佳特征

"""
    
    for category, stats in sorted_categories:
        cat_features = corr_df[corr_df['category'] == category].head(5)
        report_content += f"### {category.title()} 特征 (前5个)\n\n"
        
        for i, (_, row) in enumerate(cat_features.iterrows(), 1):
            feature_name = row['feature'].replace(f'{category}_', '')
            correlation = row['correlation']
            report_content += f"{i}. **{feature_name}**: r = {correlation:+.4f}\n"
        
        report_content += "\n"
    
    # 基因组详情
    report_content += f"""
## 基因组详情

| 基因组ID | 物种名称 | 温度 | 最佳蛋白质特征 | 最佳代谢特征 |
|----------|----------|------|----------------|--------------|
"""
    
    # 获取最佳特征
    best_protein = category_stats.get('protein', {}).get('top_feature', 'N/A')
    best_pathway = category_stats.get('pathway', {}).get('top_feature', 'N/A')
    
    for idx, row in df.iterrows():
        taxid = row['taxid']
        species = row['species_name']
        temp = row['temperature']
        
        protein_val = row.get(best_protein, 'N/A')
        pathway_val = row.get(best_pathway, 'N/A')
        
        if isinstance(protein_val, float):
            protein_val = f"{protein_val:.3f}"
        if isinstance(pathway_val, float):
            pathway_val = f"{pathway_val:.3f}"
        
        report_content += f"| {taxid} | {species} | {temp:.1f}°C | {protein_val} | {pathway_val} |\n"
    
    report_content += f"""
## 关键发现

### 1. 特征类别重要性
"""
    
    for rank, (category, stats) in enumerate(sorted_categories, 1):
        report_content += f"{rank}. **{category.title()}**: 平均相关性 {stats['mean_correlation']:.4f}\n"
    
    report_content += f"""
### 2. 生物学洞察
- **蛋白质特征**: 氨基酸组成和理化性质是温度适应的关键
- **代谢特征**: 酶系统多样性反映代谢适应能力
- **RNA特征**: RNA结构稳定性影响转录效率
- **系统发育**: 进化位置决定温度适应策略

### 3. 应用价值
- 可用于快速预测微生物最适生长温度
- 指导耐热酶的筛选和设计
- 分析温度适应的分子机制
- 研究极端环境微生物的适应策略

## 输出文件
- 特征相关性: advanced_feature_correlations.tsv
- 分析报告: advanced_analysis_report.md

---
*报告由简化高级特征分析器生成*
"""
    
    # 保存报告
    report_file = os.path.join(output_dir, "advanced_analysis_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    logger.info(f"分析报告已生成: {report_file}")
    
    # 输出摘要
    logger.info("=" * 60)
    logger.info("🎉 高级特征分析完成！")
    logger.info(f"📊 分析了 {len(df)} 个基因组，{len(all_features)} 个高级特征")
    
    logger.info("\n🏆 特征类别性能排名:")
    for rank, (category, stats) in enumerate(sorted_categories, 1):
        logger.info(f"   {rank}. {category}: {stats['mean_correlation']:.4f} (平均), {stats['max_correlation']:.4f} (最大)")
    
    logger.info("\n🔬 前5个最重要特征:")
    for i, (_, row) in enumerate(corr_df.head(5).iterrows(), 1):
        feature_name = row['feature']
        category = row['category']
        short_name = feature_name.replace(f'{category}_', '') if category != 'other' else feature_name
        logger.info(f"   {i}. {short_name} ({category}): r = {row['correlation']:+.4f}")
    
    logger.info("=" * 60)
    
    return corr_df, category_stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化的高级基因组特征分析器")
    
    parser.add_argument('--features', required=True, help='高级特征TSV文件路径')
    parser.add_argument('--output-dir', default='simple_advanced_analysis', help='输出目录')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # 检查输入文件
    if not os.path.exists(args.features):
        logger.error(f"特征文件不存在: {args.features}")
        return 1
    
    try:
        # 运行分析
        corr_df, category_stats = analyze_advanced_features(args.features, args.output_dir)
        return 0
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
