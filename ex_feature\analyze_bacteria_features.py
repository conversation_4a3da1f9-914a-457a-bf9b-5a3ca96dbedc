#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
细菌基因组温度特征分析器

分析从Bacteria目录提取的基因组特征与温度的关系，
生成相关性分析、可视化图表和预测模型。
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
import logging
from datetime import datetime
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score, LeaveOneOut
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BacteriaFeatureAnalyzer:
    """细菌基因组特征分析器"""
    
    def __init__(self, features_file: str, output_dir: str = "bacteria_analysis"):
        """
        初始化分析器
        
        参数:
            features_file: 特征TSV文件路径
            output_dir: 输出目录
        """
        self.features_file = features_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载数据
        self.df = self._load_data()
        
        # 分离特征和目标
        self.feature_columns = [col for col in self.df.columns 
                               if col not in ['taxid', 'species_name', 'temperature', 'organism_type', 'assembly_level', 'quality_score']]
        
        logger.info(f"加载了 {len(self.df)} 个基因组，{len(self.feature_columns)} 个特征")
    
    def _load_data(self) -> pd.DataFrame:
        """加载特征数据"""
        try:
            df = pd.read_csv(self.features_file, sep='\t')
            logger.info(f"成功加载特征数据: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"加载特征数据失败: {e}")
            raise
    
    def analyze_correlations(self) -> pd.DataFrame:
        """分析特征与温度的相关性"""
        logger.info("分析特征与温度的相关性")
        
        correlations = []
        
        for feature in self.feature_columns:
            try:
                # 计算皮尔逊相关系数
                corr = np.corrcoef(self.df[feature], self.df['temperature'])[0, 1]
                
                if not np.isnan(corr):
                    # 确定特征类别
                    if feature.startswith('genomic_'):
                        category = '基因组特征'
                    elif feature.startswith('cds_'):
                        category = '密码子特征'
                    elif any(x in feature for x in ['gene_count', 'cds_count', 'trna_count', 'rrna_count']):
                        category = '基因注释特征'
                    else:
                        category = '其他特征'
                    
                    correlations.append({
                        'feature': feature,
                        'correlation': corr,
                        'abs_correlation': abs(corr),
                        'category': category
                    })
            except Exception as e:
                logger.warning(f"计算特征 {feature} 相关性失败: {e}")
        
        # 转换为DataFrame并排序
        corr_df = pd.DataFrame(correlations)
        corr_df = corr_df.sort_values('abs_correlation', ascending=False)
        
        # 保存相关性结果
        output_file = self.output_dir / "feature_correlations.tsv"
        corr_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
        
        logger.info(f"相关性分析完成，结果保存到: {output_file}")
        
        return corr_df
    
    def generate_visualizations(self, corr_df: pd.DataFrame):
        """生成可视化图表"""
        logger.info("生成可视化图表")
        
        # 设置图表样式
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 1. 温度分布和基本统计
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 温度分布直方图
        axes[0, 0].hist(self.df['temperature'], bins=8, alpha=0.7, edgecolor='black', color='skyblue')
        axes[0, 0].set_xlabel('温度 (°C)')
        axes[0, 0].set_ylabel('基因组数量')
        axes[0, 0].set_title('温度分布')
        axes[0, 0].grid(alpha=0.3)
        
        # GC含量与温度散点图
        axes[0, 1].scatter(self.df['genomic_gc_content'], self.df['temperature'], 
                          alpha=0.8, s=100, color='coral')
        axes[0, 1].set_xlabel('GC含量')
        axes[0, 1].set_ylabel('温度 (°C)')
        axes[0, 1].set_title('GC含量 vs 温度')
        
        # 添加趋势线
        z = np.polyfit(self.df['genomic_gc_content'], self.df['temperature'], 1)
        p = np.poly1d(z)
        axes[0, 1].plot(self.df['genomic_gc_content'], p(self.df['genomic_gc_content']), 
                       "r--", alpha=0.8, linewidth=2)
        
        # 计算相关系数
        gc_corr = np.corrcoef(self.df['genomic_gc_content'], self.df['temperature'])[0, 1]
        axes[0, 1].text(0.05, 0.95, f'r = {gc_corr:.3f}', transform=axes[0, 1].transAxes,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        # 基因组大小与温度
        axes[1, 0].scatter(self.df['genomic_genome_size']/1e6, self.df['temperature'], 
                          alpha=0.8, s=100, color='lightgreen')
        axes[1, 0].set_xlabel('基因组大小 (Mb)')
        axes[1, 0].set_ylabel('温度 (°C)')
        axes[1, 0].set_title('基因组大小 vs 温度')
        
        # 特征相关性条形图（前10个）
        top_features = corr_df.head(10)
        y_pos = np.arange(len(top_features))
        
        colors = ['red' if x < 0 else 'blue' for x in top_features['correlation']]
        bars = axes[1, 1].barh(y_pos, top_features['correlation'], color=colors, alpha=0.7)
        axes[1, 1].set_yticks(y_pos)
        axes[1, 1].set_yticklabels([f.replace('genomic_', '').replace('cds_', '') 
                                   for f in top_features['feature']], fontsize=8)
        axes[1, 1].set_xlabel('与温度的相关系数')
        axes[1, 1].set_title('前10个最相关特征')
        axes[1, 1].grid(axis='x', alpha=0.3)
        
        # 添加数值标签
        for i, (bar, corr) in enumerate(zip(bars, top_features['correlation'])):
            axes[1, 1].text(corr + (0.01 if corr > 0 else -0.01), i, f'{corr:.3f}', 
                           va='center', ha='left' if corr > 0 else 'right', fontsize=8)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'bacteria_feature_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 详细的特征分析图
        self._generate_detailed_plots(corr_df)
        
        logger.info("可视化图表生成完成")
    
    def _generate_detailed_plots(self, corr_df: pd.DataFrame):
        """生成详细的特征分析图"""
        # 选择最相关的6个特征
        top_6_features = corr_df.head(6)['feature'].tolist()
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, feature in enumerate(top_6_features):
            ax = axes[i]
            
            # 散点图
            ax.scatter(self.df[feature], self.df['temperature'], alpha=0.8, s=100)
            
            # 趋势线
            z = np.polyfit(self.df[feature], self.df['temperature'], 1)
            p = np.poly1d(z)
            ax.plot(self.df[feature], p(self.df[feature]), "r--", alpha=0.8, linewidth=2)
            
            # 标签和标题
            feature_name = feature.replace('genomic_', '').replace('cds_', '')
            ax.set_xlabel(feature_name)
            ax.set_ylabel('温度 (°C)')
            
            # 相关系数
            corr = corr_df[corr_df['feature'] == feature]['correlation'].iloc[0]
            ax.set_title(f'{feature_name}\nr = {corr:.3f}')
            
            # 添加基因组标签
            for idx, row in self.df.iterrows():
                ax.annotate(f"{row['taxid']}", 
                           (row[feature], row['temperature']),
                           xytext=(5, 5), textcoords='offset points',
                           fontsize=8, alpha=0.7)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'top_features_detailed.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def build_prediction_models(self, corr_df: pd.DataFrame):
        """构建温度预测模型"""
        logger.info("构建温度预测模型")
        
        # 选择前15个最相关的特征
        top_features = corr_df.head(15)['feature'].tolist()
        
        X = self.df[top_features].values
        y = self.df['temperature'].values
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        models = {
            '线性回归': LinearRegression(),
            '随机森林': RandomForestRegressor(n_estimators=100, random_state=42)
        }
        
        results = {}
        
        for model_name, model in models.items():
            logger.info(f"训练 {model_name} 模型")
            
            # 留一法交叉验证（因为样本数较少）
            loo = LeaveOneOut()
            
            # 交叉验证评分
            cv_scores = cross_val_score(model, X_scaled, y, cv=loo, scoring='neg_mean_squared_error')
            rmse_scores = np.sqrt(-cv_scores)
            
            # 拟合完整模型
            model.fit(X_scaled, y)
            y_pred = model.predict(X_scaled)
            
            # 计算指标
            rmse = np.sqrt(mean_squared_error(y, y_pred))
            mae = mean_absolute_error(y, y_pred)
            r2 = r2_score(y, y_pred)
            
            results[model_name] = {
                'model': model,
                'rmse': rmse,
                'mae': mae,
                'r2': r2,
                'cv_rmse_mean': rmse_scores.mean(),
                'cv_rmse_std': rmse_scores.std(),
                'predictions': y_pred,
                'features': top_features
            }
            
            logger.info(f"{model_name} - RMSE: {rmse:.2f}°C, MAE: {mae:.2f}°C, R²: {r2:.3f}")
        
        # 保存模型结果
        self._save_model_results(results)
        
        return results
    
    def _save_model_results(self, results: dict):
        """保存模型结果"""
        # 创建预测结果DataFrame
        predictions_df = self.df[['taxid', 'species_name', 'temperature']].copy()
        
        for model_name, result in results.items():
            predictions_df[f'{model_name}_预测'] = result['predictions']
            predictions_df[f'{model_name}_误差'] = predictions_df['temperature'] - result['predictions']
        
        # 保存预测结果
        output_file = self.output_dir / "temperature_predictions.tsv"
        predictions_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
        
        logger.info(f"预测结果保存到: {output_file}")
    
    def generate_comprehensive_report(self, corr_df: pd.DataFrame, model_results: dict):
        """生成综合分析报告"""
        logger.info("生成综合分析报告")
        
        # 计算基本统计
        temp_stats = self.df['temperature'].describe()
        gc_corr = corr_df[corr_df['feature'] == 'genomic_gc_content']['correlation'].iloc[0]
        
        # 按特征类别统计
        category_stats = corr_df.groupby('category')['abs_correlation'].agg(['count', 'mean', 'max']).round(4)
        
        report_content = f"""# 细菌基因组温度特征综合分析报告

## 分析概览
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {len(self.df)}
- 特征数量: {len(self.feature_columns)}
- 温度范围: {temp_stats['min']:.1f}°C - {temp_stats['max']:.1f}°C
- 平均温度: {temp_stats['mean']:.1f}°C

## 关键发现

### 1. 温度-GC含量关系验证
- **GC含量与温度相关系数**: {gc_corr:.4f}
- **生物学意义**: {'强正相关，验证了高温菌具有更高GC含量的理论' if gc_corr > 0.5 else '相关性较弱，可能受其他因素影响'}

### 2. 最重要的温度预测特征

以下是与温度相关性最强的前10个特征：

| 排名 | 特征名称 | 相关系数 | 特征类别 | 生物学意义 |
|------|----------|----------|----------|------------|
"""
        
        for i, (_, row) in enumerate(corr_df.head(10).iterrows(), 1):
            feature_name = row['feature'].replace('genomic_', '').replace('cds_', '')
            correlation = row['correlation']
            category = row['category']
            
            # 生物学意义解释
            if 'gc' in feature_name.lower():
                meaning = "GC含量相关，影响DNA热稳定性"
            elif 'dinuc' in feature_name.lower():
                meaning = "二核苷酸模式，影响DNA结构稳定性"
            elif 'codon' in feature_name.lower():
                meaning = "密码子使用偏好，反映翻译效率"
            elif 'gene' in feature_name.lower():
                meaning = "基因组织特征，反映基因组紧密度"
            else:
                meaning = "序列组成特征"
            
            report_content += f"| {i} | {feature_name} | {correlation:+.4f} | {category} | {meaning} |\n"
        
        report_content += f"""
### 3. 按特征类别的重要性分析

| 特征类别 | 特征数量 | 平均相关性 | 最大相关性 |
|----------|----------|------------|------------|
"""
        
        for category, stats in category_stats.iterrows():
            report_content += f"| {category} | {stats['count']} | {stats['mean']:.4f} | {stats['max']:.4f} |\n"
        
        # 添加模型结果
        if model_results:
            report_content += f"""
## 温度预测模型结果

### 模型性能比较

| 模型 | RMSE (°C) | MAE (°C) | R² | 交叉验证RMSE |
|------|-----------|----------|----|-----------| 
"""
            
            for model_name, result in model_results.items():
                report_content += f"| {model_name} | {result['rmse']:.2f} | {result['mae']:.2f} | {result['r2']:.3f} | {result['cv_rmse_mean']:.2f}±{result['cv_rmse_std']:.2f} |\n"
            
            # 找出最佳模型
            best_model = min(model_results.items(), key=lambda x: x[1]['cv_rmse_mean'])
            
            report_content += f"""
### 最佳模型: {best_model[0]}
- **交叉验证RMSE**: {best_model[1]['cv_rmse_mean']:.2f}±{best_model[1]['cv_rmse_std']:.2f}°C
- **拟合R²**: {best_model[1]['r2']:.3f}
- **使用特征数**: {len(best_model[1]['features'])}

### 预测结果详情

| 基因组ID | 物种名称 | 实际温度 | 预测温度 | 误差 |
|----------|----------|----------|----------|------|
"""
            
            for idx, row in self.df.iterrows():
                taxid = row['taxid']
                species = row['species_name']
                actual = row['temperature']
                predicted = best_model[1]['predictions'][idx]
                error = actual - predicted
                
                report_content += f"| {taxid} | {species} | {actual:.1f}°C | {predicted:.1f}°C | {error:+.1f}°C |\n"
        
        report_content += f"""
## 生物学解释

### 温度适应机制
基于分析结果，细菌的温度适应主要通过以下机制实现：

1. **DNA稳定性调节**
   - 高温菌倾向于具有更高的GC含量
   - 特定的二核苷酸组合增强DNA热稳定性

2. **蛋白质稳定性**
   - 密码子使用偏好影响蛋白质折叠
   - 特定氨基酸组合提高热稳定性

3. **基因组组织**
   - 基因密度和排列影响转录效率
   - 基因组大小与代谢效率相关

### 进化意义
- 温度适应是多基因性状，涉及基因组多个层面
- GC含量是最重要的适应性特征之一
- 密码子使用偏好反映了翻译水平的适应

## 应用建议

### 1. 温度预测
- 使用前15个最相关特征可获得较好的预测精度
- 建议结合多种模型进行集成预测
- 对于新的细菌基因组，可快速预测其最适生长温度

### 2. 生物技术应用
- 筛选耐热酶时可参考GC含量和密码子偏好
- 设计合成生物学系统时考虑温度适应性特征
- 优化工业菌株的温度稳定性

### 3. 进化研究
- 分析温度适应的分子机制
- 研究极端环境微生物的适应策略
- 预测气候变化对微生物群落的影响

## 数据质量评估

### 优势
- 特征提取全面，涵盖基因组、基因和密码子多个层面
- 结合了序列信息和注释信息
- 生物学解释性强

### 局限性
- 样本数量相对较少（{len(self.df)}个基因组）
- 温度范围相对集中（{temp_stats['min']:.1f}-{temp_stats['max']:.1f}°C）
- 缺乏极端温度的样本

### 改进建议
- 增加更多样本，特别是极端温度的微生物
- 加入更多功能性特征（如代谢路径）
- 考虑系统发育关系的影响

## 输出文件
- **特征相关性**: feature_correlations.tsv
- **预测结果**: temperature_predictions.tsv
- **可视化图表**: bacteria_feature_analysis.png, top_features_detailed.png
- **分析报告**: comprehensive_analysis_report.md

---
*报告由细菌基因组特征分析器自动生成*
"""
        
        # 保存报告
        report_file = self.output_dir / "comprehensive_analysis_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"综合分析报告已生成: {report_file}")
    
    def run_complete_analysis(self):
        """运行完整的分析流程"""
        logger.info("开始完整的特征分析流程")
        
        # 1. 相关性分析
        corr_df = self.analyze_correlations()
        
        # 2. 生成可视化
        self.generate_visualizations(corr_df)
        
        # 3. 构建预测模型
        model_results = self.build_prediction_models(corr_df)
        
        # 4. 生成综合报告
        self.generate_comprehensive_report(corr_df, model_results)
        
        logger.info("✅ 完整分析流程完成!")
        
        return {
            'correlations': corr_df,
            'models': model_results,
            'output_dir': self.output_dir
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="细菌基因组温度特征分析器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
    # 基本用法
    python analyze_bacteria_features.py --features bacteria_temp_features/bacteria_genome_features.tsv

    # 指定输出目录
    python analyze_bacteria_features.py --features features.tsv --output-dir analysis_results
        """
    )

    parser.add_argument('--features', required=True, help='特征TSV文件路径')
    parser.add_argument('--output-dir', default='bacteria_analysis', help='输出目录')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # 检查输入文件
    if not os.path.exists(args.features):
        logger.error(f"特征文件不存在: {args.features}")
        return 1

    try:
        # 创建分析器
        analyzer = BacteriaFeatureAnalyzer(
            features_file=args.features,
            output_dir=args.output_dir
        )

        # 运行完整分析
        results = analyzer.run_complete_analysis()

        # 输出结果摘要
        logger.info("=" * 60)
        logger.info("🎉 分析完成！主要结果:")
        logger.info(f"   📊 分析了 {len(analyzer.df)} 个细菌基因组")
        logger.info(f"   🔍 评估了 {len(analyzer.feature_columns)} 个特征")
        logger.info(f"   📈 生成了相关性分析和预测模型")
        logger.info(f"   📁 结果保存在: {results['output_dir']}")

        # 显示最重要的特征
        top_5_features = results['correlations'].head(5)
        logger.info("\n🏆 前5个最重要的温度预测特征:")
        for i, (_, row) in enumerate(top_5_features.iterrows(), 1):
            feature_name = row['feature'].replace('genomic_', '').replace('cds_', '')
            logger.info(f"   {i}. {feature_name}: r = {row['correlation']:+.4f}")

        # 显示模型性能
        if 'models' in results and results['models']:
            logger.info("\n🤖 预测模型性能:")
            for model_name, result in results['models'].items():
                logger.info(f"   {model_name}: RMSE = {result['cv_rmse_mean']:.2f}°C, R² = {result['r2']:.3f}")

        logger.info("=" * 60)

        return 0

    except Exception as e:
        logger.error(f"分析失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
