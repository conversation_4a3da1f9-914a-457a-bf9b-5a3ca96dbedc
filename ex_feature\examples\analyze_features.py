#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析提取的温度特征

这个脚本分析提取的基因组特征与温度的关系，
生成相关性分析和可视化图表。
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

def analyze_features(features_file: str, output_dir: str = "analysis"):
    """分析特征与温度的关系"""
    
    print("=== 温度特征分析 ===")
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 读取特征数据
    try:
        df = pd.read_csv(features_file, sep='\t')
        print(f"读取特征数据: {len(df)} 个样本, {len(df.columns)} 个特征")
    except Exception as e:
        print(f"读取特征文件失败: {e}")
        return
    
    # 基本统计
    print(f"\n基本统计:")
    print(f"温度范围: {df['temperature'].min():.1f}°C - {df['temperature'].max():.1f}°C")
    print(f"平均温度: {df['temperature'].mean():.1f}°C")
    
    # 计算特征与温度的相关性
    feature_cols = [col for col in df.columns if col not in ['temperature', 'genome_id']]
    correlations = []
    
    for col in feature_cols:
        try:
            corr = np.corrcoef(df[col], df['temperature'])[0, 1]
            if not np.isnan(corr):
                correlations.append({
                    'feature': col,
                    'correlation': corr,
                    'abs_correlation': abs(corr)
                })
        except:
            continue
    
    # 排序并显示最相关的特征
    correlations_df = pd.DataFrame(correlations)
    correlations_df = correlations_df.sort_values('abs_correlation', ascending=False)
    
    print(f"\n最相关的前10个特征:")
    print("特征名称                                相关系数")
    print("-" * 60)
    for i, (_, row) in enumerate(correlations_df.head(10).iterrows(), 1):
        print(f"{i:2d}. {row['feature']:35s} {row['correlation']:+8.4f}")
    
    # 保存相关性分析结果
    correlations_df.to_csv(output_path / "feature_correlations.tsv", sep='\t', index=False)
    print(f"\n相关性分析结果已保存: {output_path / 'feature_correlations.tsv'}")
    
    # 生成可视化图表
    generate_plots(df, correlations_df, output_path)
    
    # 生成分析报告
    generate_report(df, correlations_df, output_path)

def generate_plots(df: pd.DataFrame, correlations_df: pd.DataFrame, output_path: Path):
    """生成可视化图表"""
    
    print("\n生成可视化图表...")
    
    # 设置图表样式
    plt.style.use('default')
    sns.set_palette("husl")
    
    # 1. 温度分布图
    plt.figure(figsize=(10, 6))
    plt.subplot(2, 2, 1)
    plt.hist(df['temperature'], bins=10, alpha=0.7, edgecolor='black')
    plt.xlabel('温度 (°C)')
    plt.ylabel('频数')
    plt.title('温度分布')
    
    # 2. GC含量与温度的关系
    plt.subplot(2, 2, 2)
    plt.scatter(df['genomic_gc_content'], df['temperature'], alpha=0.7)
    plt.xlabel('GC含量')
    plt.ylabel('温度 (°C)')
    plt.title('GC含量 vs 温度')
    
    # 添加趋势线
    z = np.polyfit(df['genomic_gc_content'], df['temperature'], 1)
    p = np.poly1d(z)
    plt.plot(df['genomic_gc_content'], p(df['genomic_gc_content']), "r--", alpha=0.8)
    
    # 3. 序列熵与温度的关系
    plt.subplot(2, 2, 3)
    plt.scatter(df['genomic_sequence_entropy'], df['temperature'], alpha=0.7, color='green')
    plt.xlabel('序列熵')
    plt.ylabel('温度 (°C)')
    plt.title('序列熵 vs 温度')
    
    # 4. 特征相关性热图（前10个特征）
    plt.subplot(2, 2, 4)
    top_features = correlations_df.head(10)['feature'].tolist()
    corr_matrix = df[top_features + ['temperature']].corr()
    
    # 只显示与温度的相关性
    temp_corr = corr_matrix['temperature'].drop('temperature')
    
    # 创建简化的热图
    plt.barh(range(len(temp_corr)), temp_corr.values)
    plt.yticks(range(len(temp_corr)), [f.replace('genomic_', '') for f in temp_corr.index], fontsize=8)
    plt.xlabel('与温度的相关系数')
    plt.title('特征-温度相关性')
    plt.grid(axis='x', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_path / 'feature_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 生成详细的相关性图
    plt.figure(figsize=(12, 8))
    
    # 选择最相关的6个特征进行详细分析
    top_6_features = correlations_df.head(6)['feature'].tolist()
    
    for i, feature in enumerate(top_6_features, 1):
        plt.subplot(2, 3, i)
        plt.scatter(df[feature], df['temperature'], alpha=0.7)
        plt.xlabel(feature.replace('genomic_', ''))
        plt.ylabel('温度 (°C)')
        
        # 添加相关系数
        corr = correlations_df[correlations_df['feature'] == feature]['correlation'].iloc[0]
        plt.title(f'r = {corr:.3f}')
        
        # 添加趋势线
        z = np.polyfit(df[feature], df['temperature'], 1)
        p = np.poly1d(z)
        plt.plot(df[feature], p(df[feature]), "r--", alpha=0.8)
    
    plt.tight_layout()
    plt.savefig(output_path / 'top_features_correlation.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"图表已保存:")
    print(f"  - {output_path / 'feature_analysis.png'}")
    print(f"  - {output_path / 'top_features_correlation.png'}")

def generate_report(df: pd.DataFrame, correlations_df: pd.DataFrame, output_path: Path):
    """生成分析报告"""
    
    print("\n生成分析报告...")
    
    # 计算一些关键统计
    gc_corr = correlations_df[correlations_df['feature'] == 'genomic_gc_content']['correlation'].iloc[0]
    entropy_corr = correlations_df[correlations_df['feature'] == 'genomic_sequence_entropy']['correlation'].iloc[0]
    
    report_content = f"""# 基因组温度特征分析报告

## 分析概览
- 分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
- 样本数量: {len(df)}
- 特征数量: {len(correlations_df)}
- 温度范围: {df['temperature'].min():.1f}°C - {df['temperature'].max():.1f}°C

## 关键发现

### 1. GC含量与温度的关系
- **相关系数**: {gc_corr:.4f}
- **生物学意义**: {'正相关表明高温菌倾向于具有更高的GC含量，这有助于DNA的热稳定性' if gc_corr > 0 else '负相关可能表明其他因素影响温度适应性'}

### 2. 序列复杂度与温度
- **序列熵相关系数**: {entropy_corr:.4f}
- **解释**: {'高温环境可能选择更简单、更稳定的序列模式' if entropy_corr < 0 else '高温环境可能需要更复杂的序列来维持功能'}

### 3. 最重要的温度预测特征

以下是与温度相关性最强的前10个特征：

| 排名 | 特征名称 | 相关系数 | 生物学意义 |
|------|----------|----------|------------|
"""
    
    for i, (_, row) in enumerate(correlations_df.head(10).iterrows(), 1):
        feature_name = row['feature'].replace('genomic_', '')
        correlation = row['correlation']
        
        # 简单的生物学解释
        if 'gc' in feature_name.lower():
            meaning = "GC含量相关，影响DNA热稳定性"
        elif 'entropy' in feature_name.lower():
            meaning = "序列复杂度，反映基因组组织"
        elif 'dinuc' in feature_name.lower():
            meaning = "二核苷酸模式，影响DNA结构"
        elif 'kmer' in feature_name.lower():
            meaning = "序列模式多样性"
        else:
            meaning = "基因组结构特征"
        
        report_content += f"| {i} | {feature_name} | {correlation:+.4f} | {meaning} |\n"
    
    report_content += f"""
## 温度分类分析

根据温度范围，样本可分为以下类别：
"""
    
    # 温度分类统计
    temp_categories = []
    for _, row in df.iterrows():
        temp = row['temperature']
        if temp <= 15:
            temp_categories.append("低温菌 (≤15°C)")
        elif temp <= 45:
            temp_categories.append("中温菌 (16-45°C)")
        elif temp <= 80:
            temp_categories.append("高温菌 (46-80°C)")
        else:
            temp_categories.append("超高温菌 (>80°C)")
    
    category_counts = pd.Series(temp_categories).value_counts()
    
    for category, count in category_counts.items():
        report_content += f"- **{category}**: {count} 个样本\n"
    
    report_content += f"""
## 特征工程建议

基于分析结果，以下特征对温度预测最有价值：

1. **GC含量特征**: 包括整体GC含量、GC分布和GC偏斜
2. **序列复杂度**: 序列熵和k-mer多样性
3. **二核苷酸模式**: 特定的二核苷酸组合频率
4. **基因组结构**: 基因组大小和组织特征

## 模型建议

1. **线性模型**: 可以尝试多元线性回归作为基线
2. **非线性模型**: 随机森林或梯度提升可能捕获复杂关系
3. **特征选择**: 使用前{min(20, len(correlations_df))}个最相关特征
4. **交叉验证**: 使用留一法或k折交叉验证评估性能

## 输出文件

- `feature_correlations.tsv`: 完整的特征相关性分析
- `feature_analysis.png`: 基本分析图表
- `top_features_correlation.png`: 重要特征详细分析
- `analysis_report.md`: 本分析报告

---
*报告由优化的温度特征分析器自动生成*
"""
    
    # 保存报告
    report_file = output_path / "analysis_report.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"分析报告已保存: {report_file}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="分析提取的温度特征")
    parser.add_argument('--features', required=True, help='特征TSV文件路径')
    parser.add_argument('--output-dir', default='analysis', help='输出目录')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.features):
        print(f"错误: 特征文件不存在: {args.features}")
        return 1
    
    try:
        analyze_features(args.features, args.output_dir)
        print("\n✅ 特征分析完成!")
        return 0
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
