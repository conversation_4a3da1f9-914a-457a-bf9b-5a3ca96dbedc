# 测试数据集

这个数据集包含 9 个模拟的微生物基因组，用于测试温度特征提取功能。

## 文件说明

### 基因组文件
- `psychrophile_1.fna`: psychrophile (温度: 5.0°C, GC含量: 35.0%)
- `psychrophile_2.fna`: psychrophile (温度: 10.0°C, GC含量: 38.0%)
- `mesophile_1.fna`: mesophile (温度: 25.0°C, GC含量: 45.0%)
- `mesophile_2.fna`: mesophile (温度: 30.0°C, GC含量: 48.0%)
- `mesophile_3.fna`: mesophile (温度: 37.0°C, GC含量: 52.0%)
- `thermophile_1.fna`: thermophile (温度: 55.0°C, GC含量: 62.0%)
- `thermophile_2.fna`: thermophile (温度: 65.0°C, GC含量: 68.0%)
- `hyperthermophile_1.fna`: hyperthermophile (温度: 80.0°C, GC含量: 72.0%)
- `hyperthermophile_2.fna`: hyperthermophile (温度: 95.0°C, GC含量: 75.0%)

### 元数据文件
- `genomes.tsv`: 包含所有基因组的元数据信息

## 使用方法

### 批量特征提取
```bash
python ../simple_extract_features.py batch --metadata genomes.tsv --output-dir features/ --threads 4
```

### 单个基因组特征提取
```bash
python ../simple_extract_features.py single --genome psychrophile_1.fna --output psychrophile_1_features.npz
```

## 数据特点

- **低温菌 (Psychrophiles)**: 温度 ≤ 15°C, 低GC含量
- **中温菌 (Mesophiles)**: 温度 20-45°C, 中等GC含量  
- **高温菌 (Thermophiles)**: 温度 45-80°C, 高GC含量
- **超高温菌 (Hyperthermophiles)**: 温度 ≥ 80°C, 很高GC含量

这种设计模拟了真实微生物中温度与GC含量的相关性。
