#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级基因组特征提取器

基于基因组序列和GFF注释文件提取高级特征：
1. 蛋白质特征 (氨基酸组成、理化性质、稳定性)
2. 代谢途径特征 (KEGG途径、酶系统)
3. 系统发育特征 (分类学信息、进化特征)
4. RNA特征 (tRNA/rRNA结构、稳定性)
"""

import os
import sys
import gzip
import json
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from collections import Counter, defaultdict
import tempfile
import argparse
from datetime import datetime
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedFeatureExtractor:
    """高级基因组特征提取器"""
    
    def __init__(self):
        """初始化提取器"""
        # 氨基酸理化性质数据
        self.aa_properties = self._init_aa_properties()
        
        # 密码子表
        self.genetic_code = self._init_genetic_code()
        
        # KEGG酶分类
        self.enzyme_classes = self._init_enzyme_classes()
        
        logger.info("高级特征提取器初始化完成")
    
    def _init_aa_properties(self) -> Dict[str, Dict[str, float]]:
        """初始化氨基酸理化性质"""
        return {
            'A': {'mw': 89.09, 'pI': 6.00, 'hydrophobicity': 1.8, 'charge': 0, 'polar': 0, 'aromatic': 0, 'aliphatic': 1},
            'R': {'mw': 174.20, 'pI': 10.76, 'hydrophobicity': -4.5, 'charge': 1, 'polar': 1, 'aromatic': 0, 'aliphatic': 0},
            'N': {'mw': 132.12, 'pI': 5.41, 'hydrophobicity': -3.5, 'charge': 0, 'polar': 1, 'aromatic': 0, 'aliphatic': 0},
            'D': {'mw': 133.10, 'pI': 2.77, 'hydrophobicity': -3.5, 'charge': -1, 'polar': 1, 'aromatic': 0, 'aliphatic': 0},
            'C': {'mw': 121.15, 'pI': 5.07, 'hydrophobicity': 2.5, 'charge': 0, 'polar': 0, 'aromatic': 0, 'aliphatic': 0},
            'Q': {'mw': 146.15, 'pI': 5.65, 'hydrophobicity': -3.5, 'charge': 0, 'polar': 1, 'aromatic': 0, 'aliphatic': 0},
            'E': {'mw': 147.13, 'pI': 4.25, 'hydrophobicity': -3.5, 'charge': -1, 'polar': 1, 'aromatic': 0, 'aliphatic': 0},
            'G': {'mw': 75.07, 'pI': 5.97, 'hydrophobicity': -0.4, 'charge': 0, 'polar': 0, 'aromatic': 0, 'aliphatic': 0},
            'H': {'mw': 155.16, 'pI': 7.59, 'hydrophobicity': -3.2, 'charge': 0.1, 'polar': 1, 'aromatic': 1, 'aliphatic': 0},
            'I': {'mw': 131.17, 'pI': 6.02, 'hydrophobicity': 4.5, 'charge': 0, 'polar': 0, 'aromatic': 0, 'aliphatic': 1},
            'L': {'mw': 131.17, 'pI': 5.98, 'hydrophobicity': 3.8, 'charge': 0, 'polar': 0, 'aromatic': 0, 'aliphatic': 1},
            'K': {'mw': 146.19, 'pI': 9.74, 'hydrophobicity': -3.9, 'charge': 1, 'polar': 1, 'aromatic': 0, 'aliphatic': 0},
            'M': {'mw': 149.21, 'pI': 5.74, 'hydrophobicity': 1.9, 'charge': 0, 'polar': 0, 'aromatic': 0, 'aliphatic': 0},
            'F': {'mw': 165.19, 'pI': 5.48, 'hydrophobicity': 2.8, 'charge': 0, 'polar': 0, 'aromatic': 1, 'aliphatic': 0},
            'P': {'mw': 115.13, 'pI': 6.30, 'hydrophobicity': -1.6, 'charge': 0, 'polar': 0, 'aromatic': 0, 'aliphatic': 0},
            'S': {'mw': 105.09, 'pI': 5.68, 'hydrophobicity': -0.8, 'charge': 0, 'polar': 1, 'aromatic': 0, 'aliphatic': 0},
            'T': {'mw': 119.12, 'pI': 5.60, 'hydrophobicity': -0.7, 'charge': 0, 'polar': 1, 'aromatic': 0, 'aliphatic': 0},
            'W': {'mw': 204.23, 'pI': 5.89, 'hydrophobicity': -0.9, 'charge': 0, 'polar': 0, 'aromatic': 1, 'aliphatic': 0},
            'Y': {'mw': 181.19, 'pI': 5.66, 'hydrophobicity': -1.3, 'charge': 0, 'polar': 1, 'aromatic': 1, 'aliphatic': 0},
            'V': {'mw': 117.15, 'pI': 5.96, 'hydrophobicity': 4.2, 'charge': 0, 'polar': 0, 'aromatic': 0, 'aliphatic': 1}
        }
    
    def _init_genetic_code(self) -> Dict[str, str]:
        """初始化遗传密码表"""
        return {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
            'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
    
    def _init_enzyme_classes(self) -> Dict[str, str]:
        """初始化酶分类"""
        return {
            'EC:1': '氧化还原酶',
            'EC:2': '转移酶',
            'EC:3': '水解酶',
            'EC:4': '裂解酶',
            'EC:5': '异构酶',
            'EC:6': '连接酶'
        }
    
    def extract_protein_features(self, protein_sequences: List[str]) -> Dict[str, float]:
        """
        提取蛋白质特征
        
        参数:
            protein_sequences: 蛋白质序列列表
            
        返回:
            蛋白质特征字典
        """
        features = {}
        
        if not protein_sequences:
            return self._get_empty_protein_features()
        
        # 合并所有蛋白质序列
        all_proteins = ''.join(protein_sequences)
        
        # 1. 氨基酸组成
        aa_counts = Counter(all_proteins)
        total_aa = len(all_proteins)
        
        for aa in self.aa_properties.keys():
            features[f'aa_freq_{aa}'] = aa_counts.get(aa, 0) / total_aa if total_aa > 0 else 0
        
        # 2. 理化性质统计
        if total_aa > 0:
            # 分子量
            total_mw = sum(self.aa_properties[aa]['mw'] * aa_counts.get(aa, 0) for aa in self.aa_properties.keys())
            features['avg_protein_mw'] = total_mw / total_aa
            
            # 等电点 (简化计算)
            charged_aa = sum(aa_counts.get(aa, 0) for aa in ['R', 'K', 'D', 'E', 'H'])
            features['charged_aa_ratio'] = charged_aa / total_aa
            
            # 疏水性
            hydrophobic_score = sum(self.aa_properties[aa]['hydrophobicity'] * aa_counts.get(aa, 0) 
                                  for aa in self.aa_properties.keys())
            features['avg_hydrophobicity'] = hydrophobic_score / total_aa
            
            # 极性氨基酸比例
            polar_aa = sum(aa_counts.get(aa, 0) for aa in self.aa_properties.keys() 
                          if self.aa_properties[aa]['polar'] == 1)
            features['polar_aa_ratio'] = polar_aa / total_aa
            
            # 芳香族氨基酸比例
            aromatic_aa = sum(aa_counts.get(aa, 0) for aa in self.aa_properties.keys() 
                            if self.aa_properties[aa]['aromatic'] == 1)
            features['aromatic_aa_ratio'] = aromatic_aa / total_aa
            
            # 脂肪族氨基酸比例
            aliphatic_aa = sum(aa_counts.get(aa, 0) for aa in self.aa_properties.keys() 
                             if self.aa_properties[aa]['aliphatic'] == 1)
            features['aliphatic_aa_ratio'] = aliphatic_aa / total_aa
        
        # 3. 蛋白质稳定性指标
        features.update(self._calculate_protein_stability(protein_sequences))
        
        # 4. 温度敏感性分析
        features.update(self._analyze_temperature_sensitivity(protein_sequences))
        
        return features
    
    def _get_empty_protein_features(self) -> Dict[str, float]:
        """返回空的蛋白质特征"""
        features = {}
        
        # 氨基酸频率
        for aa in self.aa_properties.keys():
            features[f'aa_freq_{aa}'] = 0.0
        
        # 理化性质
        features.update({
            'avg_protein_mw': 0.0,
            'charged_aa_ratio': 0.0,
            'avg_hydrophobicity': 0.0,
            'polar_aa_ratio': 0.0,
            'aromatic_aa_ratio': 0.0,
            'aliphatic_aa_ratio': 0.0,
            'avg_protein_length': 0.0,
            'protein_length_std': 0.0,
            'cysteine_content': 0.0,
            'proline_content': 0.0,
            'glycine_content': 0.0,
            'thermophilic_aa_ratio': 0.0,
            'psychrophilic_aa_ratio': 0.0,
            'disulfide_potential': 0.0
        })
        
        return features
    
    def _calculate_protein_stability(self, protein_sequences: List[str]) -> Dict[str, float]:
        """计算蛋白质稳定性指标"""
        features = {}
        
        if not protein_sequences:
            return {
                'avg_protein_length': 0.0,
                'protein_length_std': 0.0,
                'cysteine_content': 0.0,
                'proline_content': 0.0,
                'glycine_content': 0.0
            }
        
        # 蛋白质长度统计
        lengths = [len(seq) for seq in protein_sequences]
        features['avg_protein_length'] = np.mean(lengths)
        features['protein_length_std'] = np.std(lengths)
        
        # 合并序列分析
        all_proteins = ''.join(protein_sequences)
        total_aa = len(all_proteins)
        
        if total_aa > 0:
            # 半胱氨酸含量 (二硫键形成)
            features['cysteine_content'] = all_proteins.count('C') / total_aa
            
            # 脯氨酸含量 (结构刚性)
            features['proline_content'] = all_proteins.count('P') / total_aa
            
            # 甘氨酸含量 (结构柔性)
            features['glycine_content'] = all_proteins.count('G') / total_aa
        
        return features
    
    def _analyze_temperature_sensitivity(self, protein_sequences: List[str]) -> Dict[str, float]:
        """分析温度敏感性"""
        features = {}
        
        if not protein_sequences:
            return {
                'thermophilic_aa_ratio': 0.0,
                'psychrophilic_aa_ratio': 0.0,
                'disulfide_potential': 0.0
            }
        
        all_proteins = ''.join(protein_sequences)
        total_aa = len(all_proteins)
        
        if total_aa > 0:
            # 嗜热菌偏好氨基酸 (高GC含量密码子对应)
            thermophilic_aa = all_proteins.count('G') + all_proteins.count('P') + all_proteins.count('R')
            features['thermophilic_aa_ratio'] = thermophilic_aa / total_aa
            
            # 嗜冷菌偏好氨基酸 (低GC含量密码子对应)
            psychrophilic_aa = all_proteins.count('K') + all_proteins.count('N') + all_proteins.count('T')
            features['psychrophilic_aa_ratio'] = psychrophilic_aa / total_aa
            
            # 二硫键形成潜力
            cys_count = all_proteins.count('C')
            features['disulfide_potential'] = (cys_count * (cys_count - 1)) / (2 * total_aa) if total_aa > 0 else 0
        
        return features

    def extract_pathway_features(self, gff_file: str) -> Dict[str, float]:
        """
        提取代谢途径特征

        参数:
            gff_file: GFF注释文件路径

        返回:
            代谢途径特征字典
        """
        features = {}

        try:
            # 解析GFF文件中的功能注释
            enzyme_counts = defaultdict(int)
            kegg_pathways = defaultdict(int)
            cog_categories = defaultdict(int)

            with gzip.open(gff_file, 'rt') as f:
                for line in f:
                    if line.startswith('#'):
                        continue

                    parts = line.strip().split('\t')
                    if len(parts) < 9:
                        continue

                    attributes = parts[8]

                    # 提取EC号
                    ec_matches = re.findall(r'EC:(\d+\.\d+\.\d+\.\d+)', attributes)
                    for ec in ec_matches:
                        ec_class = f"EC:{ec.split('.')[0]}"
                        enzyme_counts[ec_class] += 1

                    # 提取KEGG信息
                    kegg_matches = re.findall(r'KEGG:([^;,\s]+)', attributes)
                    for kegg in kegg_matches:
                        kegg_pathways[kegg] += 1

                    # 提取COG分类
                    cog_matches = re.findall(r'COG:([A-Z]+)', attributes)
                    for cog in cog_matches:
                        for category in cog:
                            cog_categories[category] += 1

            # 1. 酶系统分析
            total_enzymes = sum(enzyme_counts.values())
            for ec_class, name in self.enzyme_classes.items():
                features[f'enzyme_{ec_class.replace(":", "_")}_count'] = enzyme_counts.get(ec_class, 0)
                features[f'enzyme_{ec_class.replace(":", "_")}_ratio'] = (
                    enzyme_counts.get(ec_class, 0) / total_enzymes if total_enzymes > 0 else 0
                )

            # 2. 代谢多样性
            features['total_enzymes'] = total_enzymes
            features['enzyme_diversity'] = len(enzyme_counts)
            features['kegg_pathway_diversity'] = len(kegg_pathways)
            features['cog_category_diversity'] = len(cog_categories)

            # 3. 温度相关代谢模块
            features.update(self._analyze_temperature_pathways(kegg_pathways, cog_categories))

        except Exception as e:
            logger.error(f"提取代谢途径特征失败: {e}")
            # 返回空特征
            for ec_class in self.enzyme_classes.keys():
                features[f'enzyme_{ec_class.replace(":", "_")}_count'] = 0
                features[f'enzyme_{ec_class.replace(":", "_")}_ratio'] = 0

            features.update({
                'total_enzymes': 0,
                'enzyme_diversity': 0,
                'kegg_pathway_diversity': 0,
                'cog_category_diversity': 0,
                'heat_shock_proteins': 0,
                'cold_shock_proteins': 0,
                'stress_response_ratio': 0,
                'energy_metabolism_ratio': 0,
                'amino_acid_metabolism_ratio': 0
            })

        return features

    def _analyze_temperature_pathways(self, kegg_pathways: Dict, cog_categories: Dict) -> Dict[str, float]:
        """分析温度相关代谢途径"""
        features = {}

        # 热激蛋白相关
        heat_shock_keywords = ['hsp', 'dnak', 'groel', 'groES', 'clpx', 'clpP']
        heat_shock_count = 0

        # 冷激蛋白相关
        cold_shock_keywords = ['csp', 'cold', 'rbfa', 'pnp']
        cold_shock_count = 0

        # 统计关键词出现次数
        for pathway in kegg_pathways.keys():
            pathway_lower = pathway.lower()
            if any(keyword in pathway_lower for keyword in heat_shock_keywords):
                heat_shock_count += kegg_pathways[pathway]
            if any(keyword in pathway_lower for keyword in cold_shock_keywords):
                cold_shock_count += kegg_pathways[pathway]

        total_pathways = sum(kegg_pathways.values())

        features['heat_shock_proteins'] = heat_shock_count
        features['cold_shock_proteins'] = cold_shock_count
        features['stress_response_ratio'] = (heat_shock_count + cold_shock_count) / total_pathways if total_pathways > 0 else 0

        # COG功能分类分析
        total_cogs = sum(cog_categories.values())

        # 能量代谢 (COG C)
        features['energy_metabolism_ratio'] = cog_categories.get('C', 0) / total_cogs if total_cogs > 0 else 0

        # 氨基酸代谢 (COG E)
        features['amino_acid_metabolism_ratio'] = cog_categories.get('E', 0) / total_cogs if total_cogs > 0 else 0

        return features

    def extract_rna_features(self, gff_file: str, genome_sequence: str) -> Dict[str, float]:
        """
        提取RNA特征

        参数:
            gff_file: GFF注释文件路径
            genome_sequence: 基因组序列

        返回:
            RNA特征字典
        """
        features = {}

        try:
            trna_sequences = []
            rrna_sequences = []

            # 从GFF文件提取RNA信息
            with gzip.open(gff_file, 'rt') as f:
                for line in f:
                    if line.startswith('#'):
                        continue

                    parts = line.strip().split('\t')
                    if len(parts) < 9:
                        continue

                    feature_type = parts[2]
                    start = int(parts[3]) - 1  # 转换为0-based
                    end = int(parts[4])
                    strand = parts[6]

                    if feature_type in ['tRNA', 'rRNA']:
                        # 提取RNA序列
                        if start < len(genome_sequence) and end <= len(genome_sequence):
                            rna_seq = genome_sequence[start:end]
                            if strand == '-':
                                rna_seq = self._reverse_complement(rna_seq)

                            if feature_type == 'tRNA':
                                trna_sequences.append(rna_seq)
                            elif feature_type == 'rRNA':
                                rrna_sequences.append(rna_seq)

            # 1. tRNA特征
            features.update(self._analyze_trna_features(trna_sequences))

            # 2. rRNA特征
            features.update(self._analyze_rrna_features(rrna_sequences))

            # 3. RNA结构稳定性
            features.update(self._analyze_rna_stability(trna_sequences + rrna_sequences))

        except Exception as e:
            logger.error(f"提取RNA特征失败: {e}")
            # 返回空特征
            features.update({
                'trna_count': 0,
                'rrna_count': 0,
                'trna_avg_length': 0,
                'rrna_avg_length': 0,
                'trna_gc_content': 0,
                'rrna_gc_content': 0,
                'rna_stability_score': 0,
                'modified_bases_potential': 0
            })

        return features

    def _analyze_trna_features(self, trna_sequences: List[str]) -> Dict[str, float]:
        """分析tRNA特征"""
        features = {}

        features['trna_count'] = len(trna_sequences)

        if trna_sequences:
            # tRNA长度统计
            lengths = [len(seq) for seq in trna_sequences]
            features['trna_avg_length'] = np.mean(lengths)

            # tRNA GC含量
            all_trna = ''.join(trna_sequences)
            gc_count = all_trna.count('G') + all_trna.count('C')
            features['trna_gc_content'] = gc_count / len(all_trna) if len(all_trna) > 0 else 0
        else:
            features['trna_avg_length'] = 0
            features['trna_gc_content'] = 0

        return features

    def _analyze_rrna_features(self, rrna_sequences: List[str]) -> Dict[str, float]:
        """分析rRNA特征"""
        features = {}

        features['rrna_count'] = len(rrna_sequences)

        if rrna_sequences:
            # rRNA长度统计
            lengths = [len(seq) for seq in rrna_sequences]
            features['rrna_avg_length'] = np.mean(lengths)

            # rRNA GC含量
            all_rrna = ''.join(rrna_sequences)
            gc_count = all_rrna.count('G') + all_rrna.count('C')
            features['rrna_gc_content'] = gc_count / len(all_rrna) if len(all_rrna) > 0 else 0
        else:
            features['rrna_avg_length'] = 0
            features['rrna_gc_content'] = 0

        return features

    def _analyze_rna_stability(self, rna_sequences: List[str]) -> Dict[str, float]:
        """分析RNA结构稳定性"""
        features = {}

        if not rna_sequences:
            features['rna_stability_score'] = 0
            features['modified_bases_potential'] = 0
            return features

        # 简化的RNA稳定性评分
        all_rna = ''.join(rna_sequences)

        # GC含量贡献稳定性
        gc_content = (all_rna.count('G') + all_rna.count('C')) / len(all_rna) if len(all_rna) > 0 else 0

        # 茎环结构潜力 (简化计算)
        stem_potential = 0
        for i in range(len(all_rna) - 3):
            tetranuc = all_rna[i:i+4]
            # 检查回文序列 (简化的茎环检测)
            complement_map = str.maketrans('ATGC', 'TACG')
            if tetranuc == tetranuc[::-1].translate(complement_map):
                stem_potential += 1

        features['rna_stability_score'] = gc_content * 0.7 + (stem_potential / len(all_rna)) * 0.3 if len(all_rna) > 0 else 0

        # 修饰碱基潜力 (基于序列模式)
        modified_sites = all_rna.count('T') + all_rna.count('A')  # 简化估计
        features['modified_bases_potential'] = modified_sites / len(all_rna) if len(all_rna) > 0 else 0

        return features

    def extract_phylogenetic_features(self, species_name: str, taxid: int) -> Dict[str, float]:
        """
        提取系统发育特征

        参数:
            species_name: 物种名称
            taxid: 分类ID

        返回:
            系统发育特征字典
        """
        features = {}

        try:
            # 1. 分类学层级信息 (基于物种名称推断)
            features.update(self._analyze_taxonomic_hierarchy(species_name))

            # 2. 进化距离特征 (简化计算)
            features.update(self._calculate_evolutionary_distance(species_name))

            # 3. 系统发育保守性 (基于分类群特征)
            features.update(self._analyze_phylogenetic_conservation(species_name))

        except Exception as e:
            logger.error(f"提取系统发育特征失败: {e}")
            # 返回空特征
            features.update({
                'genus_diversity_score': 0,
                'family_conservation_score': 0,
                'evolutionary_distance_score': 0,
                'taxonomic_depth': 0,
                'is_extremophile': 0,
                'is_pathogen': 0,
                'is_marine': 0,
                'is_soil': 0
            })

        return features

    def _analyze_taxonomic_hierarchy(self, species_name: str) -> Dict[str, float]:
        """分析分类学层级"""
        features = {}

        # 简化的分类学分析
        name_parts = species_name.lower().split()

        # 属多样性评分 (基于属名长度和复杂性)
        if len(name_parts) >= 1:
            genus = name_parts[0]
            features['genus_diversity_score'] = len(genus) / 20.0  # 标准化到0-1
        else:
            features['genus_diversity_score'] = 0

        # 分类学深度 (基于名称复杂性)
        features['taxonomic_depth'] = len(name_parts)

        return features

    def _calculate_evolutionary_distance(self, species_name: str) -> Dict[str, float]:
        """计算进化距离特征"""
        features = {}

        # 基于物种名称的简化进化距离估计
        name_lower = species_name.lower()

        # 进化距离评分 (基于已知的进化关系)
        ancient_groups = ['archaeo', 'methano', 'halo', 'thermo', 'pyro', 'sulfo']
        modern_groups = ['escherichia', 'bacillus', 'streptococcus', 'lactobacillus']

        if any(group in name_lower for group in ancient_groups):
            features['evolutionary_distance_score'] = 0.8  # 古老群体
        elif any(group in name_lower for group in modern_groups):
            features['evolutionary_distance_score'] = 0.2  # 现代群体
        else:
            features['evolutionary_distance_score'] = 0.5  # 中等

        return features

    def _analyze_phylogenetic_conservation(self, species_name: str) -> Dict[str, float]:
        """分析系统发育保守性"""
        features = {}

        name_lower = species_name.lower()

        # 保守性评分 (基于分类群特征)
        conserved_groups = ['bacillus', 'clostridium', 'streptomyces']
        if any(group in name_lower for group in conserved_groups):
            features['family_conservation_score'] = 0.8
        else:
            features['family_conservation_score'] = 0.5

        # 生态位特征
        features['is_extremophile'] = 1.0 if any(x in name_lower for x in ['thermo', 'psychro', 'halo', 'acido', 'alkali']) else 0.0
        features['is_pathogen'] = 1.0 if any(x in name_lower for x in ['pathogen', 'virulent', 'disease']) else 0.0
        features['is_marine'] = 1.0 if any(x in name_lower for x in ['marine', 'ocean', 'sea']) else 0.0
        features['is_soil'] = 1.0 if any(x in name_lower for x in ['soil', 'terrestrial', 'rhizo']) else 0.0

        return features

    def _reverse_complement(self, sequence: str) -> str:
        """计算反向互补序列"""
        complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G', 'N': 'N'}
        return ''.join(complement.get(base, base) for base in reversed(sequence))
