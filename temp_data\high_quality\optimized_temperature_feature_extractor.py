#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的基因组序列最适生长温度预测特征提取器

基于DeepMu项目和paper目录中的先进方法，整合多种温度适应性特征：
1. 基因组温度特征 (genomic_temp.py)
2. 密码子温度特征 (codon_temp.py)  
3. 热稳定性相关特征
4. 温度适应性氨基酸特征
5. GC含量分布和偏斜特征
6. 序列复杂度和重复序列特征
7. 氢键强度和堆叠能特征

参考文献：
- DeepMu: Enhanced microbial growth rate and temperature predictor
- Building a tRNA thermometer to estimate microbial growth temperature
"""

import os
import argparse
import pandas as pd
import numpy as np
import logging
import gzip
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from collections import Counter, defaultdict
from datetime import datetime

# 尝试导入BioPython
try:
    from Bio import SeqIO
    from Bio.SeqUtils import GC
    from Bio.SeqUtils.ProtParam import ProteinAnalysis
    from Bio.Data import CodonTable
    BIOPYTHON_AVAILABLE = True
except ImportError:
    BIOPYTHON_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedTemperatureFeatureExtractor:
    """优化的温度特征提取器"""
    
    def __init__(self, genetic_code=11, window_size=1000):
        """初始化特征提取器
        
        Args:
            genetic_code: 遗传密码表编号 (默认11为细菌)
            window_size: GC含量计算窗口大小
        """
        self.genetic_code = genetic_code
        self.window_size = window_size
        
        # 初始化密码子表
        if BIOPYTHON_AVAILABLE:
            self.codon_table = CodonTable.unambiguous_dna_by_id[genetic_code]
        else:
            self.codon_table = None
        
        # 初始化温度相关的氨基酸和密码子
        self._init_temperature_features()
        
        logger.info("优化的温度特征提取器初始化完成")
    
    def _init_temperature_features(self):
        """初始化温度相关特征"""
        
        # 热稳定性相关氨基酸（倾向于在高温环境中使用）
        self.thermostable_aa = {
            'G': 'Glycine',      # 甘氨酸 - 增加蛋白质柔性
            'P': 'Proline',      # 脯氨酸 - 增加结构刚性
            'A': 'Alanine',      # 丙氨酸 - 小侧链，稳定
            'V': 'Valine',       # 缬氨酸 - 疏水性，稳定
            'I': 'Isoleucine',   # 异亮氨酸 - 疏水性
            'L': 'Leucine'       # 亮氨酸 - 疏水性
        }
        
        # 温度敏感氨基酸（在低温环境中更常见）
        self.thermolabile_aa = {
            'Q': 'Glutamine',    # 谷氨酰胺 - 极性，氢键
            'N': 'Asparagine',   # 天冬酰胺 - 极性，氢键
            'S': 'Serine',       # 丝氨酸 - 极性，小
            'T': 'Threonine',    # 苏氨酸 - 极性
            'C': 'Cysteine',     # 半胱氨酸 - 二硫键
            'M': 'Methionine'    # 甲硫氨酸 - 含硫
        }
        
        # 氨基酸理化性质
        self.aa_properties = {
            'A': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 89.1},
            'C': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 121.0},
            'D': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1, 'mw': 133.1},
            'E': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1, 'mw': 147.1},
            'F': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 165.2},
            'G': {'hydrophobic': False, 'polar': False, 'charged': False, 'charge': 0, 'mw': 75.1},
            'H': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'mw': 155.2},
            'I': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 131.2},
            'K': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'mw': 146.2},
            'L': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 131.2},
            'M': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 149.2},
            'N': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 132.1},
            'P': {'hydrophobic': False, 'polar': False, 'charged': False, 'charge': 0, 'mw': 115.1},
            'Q': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 146.2},
            'R': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'mw': 174.2},
            'S': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 105.1},
            'T': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 119.1},
            'V': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 117.1},
            'W': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 204.2},
            'Y': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 181.2}
        }
        
        # 堆叠能权重（基于相邻碱基对的热力学稳定性）
        self.stacking_weights = {
            'AA': -1.0, 'AT': -0.88, 'AG': -1.28, 'AC': -1.44,
            'TA': -0.58, 'TT': -1.0, 'TG': -1.45, 'TC': -1.28,
            'GA': -1.29, 'GT': -1.44, 'GG': -1.84, 'GC': -2.17,
            'CA': -1.45, 'CT': -1.29, 'CG': -2.36, 'CC': -1.84
        }
    
    def extract_features_from_genome(self, genome_file: str, cds_file: Optional[str] = None, 
                                   protein_file: Optional[str] = None) -> Dict[str, float]:
        """从基因组文件提取优化的温度预测特征
        
        Args:
            genome_file: 基因组序列文件路径
            cds_file: CDS序列文件路径 (可选)
            protein_file: 蛋白质序列文件路径 (可选)
            
        Returns:
            特征字典
        """
        features = {}
        
        logger.info(f"开始提取优化的基因组特征: {genome_file}")
        
        # 1. 基因组基本特征和结构特征
        genome_features = self._extract_genomic_features(genome_file)
        features.update(genome_features)
        
        # 2. 密码子使用特征 (如果有CDS文件)
        if cds_file and os.path.exists(cds_file):
            codon_features = self._extract_codon_features(cds_file)
            features.update(codon_features)
        
        # 3. 氨基酸组成特征 (如果有蛋白质文件)
        if protein_file and os.path.exists(protein_file):
            aa_features = self._extract_amino_acid_features(protein_file)
            features.update(aa_features)
        
        # 4. 蛋白质等电点特征 (如果有蛋白质文件)
        if protein_file and os.path.exists(protein_file):
            pi_features = self._extract_protein_pi_features(protein_file)
            features.update(pi_features)
        
        logger.info(f"特征提取完成，共提取 {len(features)} 个特征")
        return features
    
    def _load_sequences(self, file_path: str, file_type: str = "fasta") -> List[str]:
        """加载序列文件"""
        sequences = []
        
        try:
            # 处理压缩文件
            if file_path.endswith('.gz'):
                opener = gzip.open
                mode = 'rt'
            else:
                opener = open
                mode = 'r'
            
            if BIOPYTHON_AVAILABLE:
                with opener(file_path, mode) as f:
                    for record in SeqIO.parse(f, file_type):
                        seq_str = str(record.seq).upper()
                        # 过滤掉包含过多N的序列
                        if file_type == "fasta":
                            n_content = seq_str.count('N') / len(seq_str) if len(seq_str) > 0 else 1
                            if n_content < 0.1:  # N含量小于10%
                                sequences.append(seq_str)
                        else:
                            sequences.append(seq_str)
            else:
                # 简单的FASTA解析
                with opener(file_path, mode) as f:
                    current_seq = ""
                    for line in f:
                        line = line.strip()
                        if line.startswith('>'):
                            if current_seq:
                                seq_str = current_seq.upper()
                                if file_type == "fasta":
                                    n_content = seq_str.count('N') / len(seq_str) if len(seq_str) > 0 else 1
                                    if n_content < 0.1:
                                        sequences.append(seq_str)
                                else:
                                    sequences.append(seq_str)
                                current_seq = ""
                        else:
                            current_seq += line
                    if current_seq:
                        seq_str = current_seq.upper()
                        if file_type == "fasta":
                            n_content = seq_str.count('N') / len(seq_str) if len(seq_str) > 0 else 1
                            if n_content < 0.1:
                                sequences.append(seq_str)
                        else:
                            sequences.append(seq_str)
        
        except Exception as e:
            logger.error(f"读取序列文件失败 {file_path}: {e}")
        
        return sequences

    def _extract_genomic_features(self, genome_file: str) -> Dict[str, float]:
        """提取基因组特征（基于genomic_temp.py优化）"""
        features = {}

        try:
            # 读取基因组序列
            sequences = self._load_sequences(genome_file)
            if not sequences:
                logger.warning(f"未找到有效的基因组序列: {genome_file}")
                return self._get_empty_genomic_features()

            # 合并所有序列
            full_genome = ''.join(sequences)

            if len(full_genome) == 0:
                logger.warning("基因组序列为空")
                return self._get_empty_genomic_features()

            # 1. 基本基因组统计
            features.update(self._extract_basic_genome_stats(sequences, full_genome))

            # 2. GC含量相关特征
            features.update(self._extract_gc_features(full_genome))

            # 3. 核苷酸组成特征
            features.update(self._extract_nucleotide_composition(full_genome))

            # 4. 序列复杂度特征
            features.update(self._extract_sequence_complexity(full_genome))

            # 5. 重复序列特征
            features.update(self._extract_repeat_features(full_genome))

            # 6. 热稳定性相关特征
            features.update(self._extract_thermostability_features(full_genome))

            # 7. 基因组结构特征
            features.update(self._extract_structural_features(sequences))

            logger.info(f"基因组特征提取完成: {len(features)} 个特征")

        except Exception as e:
            logger.error(f"基因组特征提取失败: {e}")
            return self._get_empty_genomic_features()

        return features

    def _get_empty_genomic_features(self) -> Dict[str, float]:
        """返回空的基因组特征"""
        return {
            'genome_size': 0, 'contig_count': 0, 'gc_content': 0, 'at_content': 0,
            'gc_skew': 0, 'at_skew': 0, 'sequence_entropy': 0, 'thermal_stability_index': 0
        }

    def _extract_basic_genome_stats(self, sequences: List[str], full_genome: str) -> Dict[str, float]:
        """提取基本基因组统计特征"""
        features = {}

        # 基因组大小
        features['genome_size'] = len(full_genome)

        # contig数量
        features['contig_count'] = len(sequences)

        # 平均contig长度
        if sequences:
            contig_lengths = [len(seq) for seq in sequences]
            features['avg_contig_length'] = np.mean(contig_lengths)
            features['max_contig_length'] = max(contig_lengths)
            features['min_contig_length'] = min(contig_lengths)
            features['contig_length_std'] = np.std(contig_lengths)

            # N50统计
            features['n50'] = self._calculate_n50(contig_lengths)
        else:
            features['avg_contig_length'] = 0
            features['max_contig_length'] = 0
            features['min_contig_length'] = 0
            features['contig_length_std'] = 0
            features['n50'] = 0

        # N含量
        n_count = full_genome.count('N')
        features['n_content'] = n_count / len(full_genome) if len(full_genome) > 0 else 0

        return features

    def _calculate_n50(self, contig_lengths: List[int]) -> int:
        """计算N50值"""
        sorted_lengths = sorted(contig_lengths, reverse=True)
        total_length = sum(sorted_lengths)
        target_length = total_length / 2

        cumulative_length = 0
        for length in sorted_lengths:
            cumulative_length += length
            if cumulative_length >= target_length:
                return length

        return 0

    def _extract_gc_features(self, genome: str) -> Dict[str, float]:
        """提取GC含量相关特征"""
        features = {}

        # 整体GC含量
        if BIOPYTHON_AVAILABLE:
            features['gc_content'] = GC(genome) / 100.0
        else:
            gc_count = genome.count('G') + genome.count('C')
            features['gc_content'] = gc_count / len(genome) if len(genome) > 0 else 0

        features['at_content'] = 1.0 - features['gc_content']

        # 滑动窗口GC含量分析
        gc_values = []
        for i in range(0, len(genome) - self.window_size + 1, self.window_size // 2):
            window = genome[i:i + self.window_size]
            if len(window) == self.window_size:
                if BIOPYTHON_AVAILABLE:
                    gc_val = GC(window) / 100.0
                else:
                    gc_count = window.count('G') + window.count('C')
                    gc_val = gc_count / len(window)
                gc_values.append(gc_val)

        if gc_values:
            features['gc_mean'] = np.mean(gc_values)
            features['gc_std'] = np.std(gc_values)
            features['gc_min'] = min(gc_values)
            features['gc_max'] = max(gc_values)
            features['gc_range'] = features['gc_max'] - features['gc_min']
            features['gc_cv'] = features['gc_std'] / features['gc_mean'] if features['gc_mean'] > 0 else 0
        else:
            features['gc_mean'] = features['gc_content']
            features['gc_std'] = 0
            features['gc_min'] = features['gc_content']
            features['gc_max'] = features['gc_content']
            features['gc_range'] = 0
            features['gc_cv'] = 0

        # GC偏斜分析
        features.update(self._calculate_gc_skew(genome))

        return features

    def _calculate_gc_skew(self, genome: str) -> Dict[str, float]:
        """计算GC偏斜"""
        features = {}

        g_count = genome.count('G')
        c_count = genome.count('C')
        a_count = genome.count('A')
        t_count = genome.count('T')

        # GC偏斜 = (G-C)/(G+C)
        gc_total = g_count + c_count
        if gc_total > 0:
            features['gc_skew'] = (g_count - c_count) / gc_total
        else:
            features['gc_skew'] = 0

        # AT偏斜 = (A-T)/(A+T)
        at_total = a_count + t_count
        if at_total > 0:
            features['at_skew'] = (a_count - t_count) / at_total
        else:
            features['at_skew'] = 0

        # 滑动窗口GC偏斜分析
        gc_skew_values = []
        for i in range(0, len(genome) - self.window_size + 1, self.window_size):
            window = genome[i:i + self.window_size]
            if len(window) == self.window_size:
                w_g = window.count('G')
                w_c = window.count('C')
                w_gc_total = w_g + w_c
                if w_gc_total > 0:
                    gc_skew_values.append((w_g - w_c) / w_gc_total)

        if gc_skew_values:
            features['gc_skew_std'] = np.std(gc_skew_values)
            features['gc_skew_range'] = max(gc_skew_values) - min(gc_skew_values)
        else:
            features['gc_skew_std'] = 0
            features['gc_skew_range'] = 0

        return features

    def _extract_nucleotide_composition(self, genome: str) -> Dict[str, float]:
        """提取核苷酸组成特征"""
        features = {}

        total_length = len(genome)
        if total_length == 0:
            return {'a_content': 0, 't_content': 0, 'g_content': 0, 'c_content': 0}

        # 单核苷酸频率
        features['a_content'] = genome.count('A') / total_length
        features['t_content'] = genome.count('T') / total_length
        features['g_content'] = genome.count('G') / total_length
        features['c_content'] = genome.count('C') / total_length

        # 二核苷酸频率
        dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TA', 'TT', 'TG', 'TC',
                        'GA', 'GT', 'GG', 'GC', 'CA', 'CT', 'CG', 'CC']

        for dinuc in dinucleotides:
            count = 0
            for i in range(len(genome) - 1):
                if genome[i:i+2] == dinuc:
                    count += 1
            features[f'dinuc_{dinuc.lower()}'] = count / (total_length - 1) if total_length > 1 else 0

        # CpG含量（在原核生物中也有意义）
        cpg_count = 0
        for i in range(len(genome) - 1):
            if genome[i:i+2] == 'CG':
                cpg_count += 1
        features['cpg_content'] = cpg_count / (total_length - 1) if total_length > 1 else 0

        return features

    def _extract_sequence_complexity(self, genome: str) -> Dict[str, float]:
        """提取序列复杂度特征"""
        features = {}

        # 语言复杂度（基于k-mer多样性）
        for k in [2, 3, 4]:
            kmers = []
            for i in range(len(genome) - k + 1):
                kmer = genome[i:i+k]
                if 'N' not in kmer:
                    kmers.append(kmer)

            if kmers:
                unique_kmers = len(set(kmers))
                total_kmers = len(kmers)
                max_possible = 4 ** k

                features[f'kmer{k}_diversity'] = unique_kmers / max_possible
                features[f'kmer{k}_complexity'] = unique_kmers / total_kmers if total_kmers > 0 else 0
            else:
                features[f'kmer{k}_diversity'] = 0
                features[f'kmer{k}_complexity'] = 0

        # 序列熵
        if len(genome) > 0:
            base_counts = Counter(genome)
            total = sum(base_counts.values())
            entropy = 0
            for count in base_counts.values():
                if count > 0:
                    p = count / total
                    entropy -= p * np.log2(p)
            features['sequence_entropy'] = entropy
        else:
            features['sequence_entropy'] = 0

        return features

    def _extract_repeat_features(self, genome: str) -> Dict[str, float]:
        """提取重复序列特征"""
        features = {}

        # 简单重复序列检测
        repeat_patterns = ['A' * 10, 'T' * 10, 'G' * 10, 'C' * 10,
                          'AT' * 5, 'GC' * 5, 'AG' * 5, 'CT' * 5]

        total_repeats = 0
        for pattern in repeat_patterns:
            count = genome.count(pattern)
            total_repeats += count * len(pattern)

        features['simple_repeat_content'] = total_repeats / len(genome) if len(genome) > 0 else 0

        # 回文序列检测（简化版）
        palindrome_count = 0
        for length in [6, 8, 10]:
            for i in range(len(genome) - length + 1):
                seq = genome[i:i+length]
                if seq == seq[::-1].translate(str.maketrans('ATCG', 'TAGC')):
                    palindrome_count += 1

        features['palindrome_density'] = palindrome_count / (len(genome) / 1000) if len(genome) > 0 else 0

        return features

    def _extract_thermostability_features(self, genome: str) -> Dict[str, float]:
        """提取热稳定性相关特征"""
        features = {}

        # 氢键强度相关特征
        gc_bonds = genome.count('G') + genome.count('C')  # 3个氢键
        at_bonds = genome.count('A') + genome.count('T')  # 2个氢键

        total_bases = len(genome)
        if total_bases > 0:
            # 平均氢键强度
            avg_h_bonds = (gc_bonds * 3 + at_bonds * 2) / total_bases
            features['avg_hydrogen_bonds'] = avg_h_bonds

            # 热稳定性指数
            features['thermal_stability_index'] = gc_bonds / total_bases
        else:
            features['avg_hydrogen_bonds'] = 0
            features['thermal_stability_index'] = 0

        # 堆叠能相关特征（基于相邻碱基对）
        stacking_energy = 0
        for i in range(len(genome) - 1):
            dinuc = genome[i:i+2]
            if dinuc in self.stacking_weights:
                stacking_energy += self.stacking_weights[dinuc]

        features['avg_stacking_energy'] = stacking_energy / (len(genome) - 1) if len(genome) > 1 else 0

        return features

    def _extract_structural_features(self, sequences: List[str]) -> Dict[str, float]:
        """提取基因组结构特征"""
        features = {}

        if not sequences:
            return {'genome_fragmentation': 0, 'largest_contig_ratio': 0}

        contig_lengths = [len(seq) for seq in sequences]
        total_length = sum(contig_lengths)

        # 基因组碎片化程度
        features['genome_fragmentation'] = len(sequences) / (total_length / 1000000) if total_length > 0 else 0

        # 最大contig占比
        features['largest_contig_ratio'] = max(contig_lengths) / total_length if total_length > 0 else 0

        return features

    def _extract_codon_features(self, cds_file: str) -> Dict[str, float]:
        """提取密码子使用特征（基于codon_temp.py优化）"""
        features = {}

        if not BIOPYTHON_AVAILABLE or not self.codon_table:
            logger.warning("BioPython不可用，跳过密码子特征提取")
            return features

        try:
            # 读取CDS序列
            cds_sequences = self._load_sequences(cds_file)

            if not cds_sequences:
                logger.warning(f"未找到有效的CDS序列: {cds_file}")
                return features

            # 过滤长度为3的倍数的序列
            valid_cds = [seq for seq in cds_sequences if len(seq) % 3 == 0]

            if not valid_cds:
                logger.warning("未找到长度为3的倍数的CDS序列")
                return features

            # 合并所有CDS序列
            all_cds = ''.join(valid_cds)

            # 统计密码子
            codon_counts = defaultdict(int)
            aa_counts = defaultdict(int)

            for i in range(0, len(all_cds), 3):
                codon = all_cds[i:i+3]
                if len(codon) == 3 and all(base in 'ATGC' for base in codon):
                    codon_counts[codon] += 1
                    if codon in self.codon_table.forward_table:
                        aa = self.codon_table.forward_table[codon]
                        aa_counts[aa] += 1

            total_codons = sum(codon_counts.values())

            if total_codons == 0:
                logger.warning("未找到有效的密码子")
                return features

            # 1. 基本密码子频率
            for codon in self.codon_table.forward_table:
                features[f'codon_{codon.lower()}_freq'] = codon_counts[codon] / total_codons

            # 2. 计算有效密码子数 (ENC)
            features['enc'] = self._calculate_enc(codon_counts, aa_counts)

            # 3. 密码子使用偏好 (CUB)
            features['cub'] = self._calculate_cub(codon_counts, aa_counts)

            # 4. 温度适应性密码子特征
            features.update(self._extract_temperature_codon_features(codon_counts, aa_counts, total_codons))

            # 5. GC含量在密码子位置
            features.update(self._extract_codon_position_gc(all_cds))

            logger.info(f"密码子特征提取完成: {len([k for k in features.keys() if 'codon' in k or k in ['enc', 'cub']])} 个特征")

        except Exception as e:
            logger.error(f"密码子特征提取失败: {e}")

        return features

    def _calculate_enc(self, codon_counts: Dict[str, int], aa_counts: Dict[str, int]) -> float:
        """计算有效密码子数 (Effective Number of Codons)"""
        try:
            # 按氨基酸分组密码子
            aa_codon_groups = defaultdict(list)
            for codon, aa in self.codon_table.forward_table.items():
                aa_codon_groups[aa].append(codon)

            # 计算每个氨基酸的Nc值
            nc_values = []
            for aa, codons in aa_codon_groups.items():
                if aa_counts[aa] > 0:
                    # 计算该氨基酸的密码子使用方差
                    frequencies = [codon_counts[codon] / aa_counts[aa] for codon in codons]
                    if len(frequencies) > 1:
                        variance = sum(f * f for f in frequencies)
                        nc = 1 / variance if variance > 0 else len(codons)
                        nc_values.append(nc)

            # 计算平均Nc值
            return np.mean(nc_values) if nc_values else 20.0

        except Exception as e:
            logger.error(f"ENC计算失败: {e}")
            return 20.0

    def _calculate_cub(self, codon_counts: Dict[str, int], aa_counts: Dict[str, int]) -> float:
        """计算密码子使用偏好 (Codon Usage Bias)"""
        try:
            # 按氨基酸分组密码子
            aa_codon_groups = defaultdict(list)
            for codon, aa in self.codon_table.forward_table.items():
                aa_codon_groups[aa].append(codon)

            bias_scores = []
            for aa, codons in aa_codon_groups.items():
                if aa_counts[aa] > 0 and len(codons) > 1:
                    # 计算该氨基酸的密码子频率
                    frequencies = [codon_counts[codon] / aa_counts[aa] for codon in codons]
                    # 计算偏好度 (最大频率 - 平均频率)
                    max_freq = max(frequencies)
                    avg_freq = 1.0 / len(codons)
                    bias = max_freq - avg_freq
                    bias_scores.append(bias)

            return np.mean(bias_scores) if bias_scores else 0.0

        except Exception as e:
            logger.error(f"CUB计算失败: {e}")
            return 0.0

    def _extract_temperature_codon_features(self, codon_counts: Dict[str, int],
                                          aa_counts: Dict[str, int], total_codons: int) -> Dict[str, float]:
        """提取温度适应性密码子特征"""
        features = {}

        # 热稳定性氨基酸的密码子使用
        thermostable_codons = 0
        thermolabile_codons = 0

        for codon, count in codon_counts.items():
            if codon in self.codon_table.forward_table:
                aa = self.codon_table.forward_table[codon]
                if aa in self.thermostable_aa:
                    thermostable_codons += count
                elif aa in self.thermolabile_aa:
                    thermolabile_codons += count

        features['thermostable_codon_ratio'] = thermostable_codons / total_codons if total_codons > 0 else 0
        features['thermolabile_codon_ratio'] = thermolabile_codons / total_codons if total_codons > 0 else 0

        # GC含量相关的密码子
        gc_rich_codons = 0
        gc_poor_codons = 0

        for codon, count in codon_counts.items():
            gc_content = (codon.count('G') + codon.count('C')) / 3
            if gc_content >= 2/3:  # GC含量 >= 66.7%
                gc_rich_codons += count
            elif gc_content <= 1/3:  # GC含量 <= 33.3%
                gc_poor_codons += count

        features['gc_rich_codon_ratio'] = gc_rich_codons / total_codons if total_codons > 0 else 0
        features['gc_poor_codon_ratio'] = gc_poor_codons / total_codons if total_codons > 0 else 0

        # 密码子稳定性指数
        total_stability = 0
        for codon, count in codon_counts.items():
            stability = sum(3 if base in 'GC' else 2 for base in codon)
            total_stability += stability * count

        features['codon_stability_index'] = total_stability / (total_codons * 3) if total_codons > 0 else 0

        return features

    def _extract_codon_position_gc(self, cds_sequence: str) -> Dict[str, float]:
        """提取密码子位置的GC含量"""
        features = {}

        gc1_count = gc2_count = gc3_count = 0
        total_positions = 0

        for i in range(0, len(cds_sequence), 3):
            codon = cds_sequence[i:i+3]
            if len(codon) == 3:
                if codon[0] in 'GC': gc1_count += 1
                if codon[1] in 'GC': gc2_count += 1
                if codon[2] in 'GC': gc3_count += 1
                total_positions += 1

        features['gc1'] = gc1_count / total_positions if total_positions > 0 else 0
        features['gc2'] = gc2_count / total_positions if total_positions > 0 else 0
        features['gc3'] = gc3_count / total_positions if total_positions > 0 else 0

        return features

    def _extract_amino_acid_features(self, protein_file: str) -> Dict[str, float]:
        """提取氨基酸组成特征"""
        features = {}

        try:
            # 读取蛋白质序列
            protein_sequences = self._load_sequences(protein_file)

            if not protein_sequences:
                logger.warning(f"未找到蛋白质序列: {protein_file}")
                return features

            # 清理序列（移除终止密码子等）
            clean_sequences = []
            for seq in protein_sequences:
                clean_seq = seq.replace('*', '').replace('X', '')
                if clean_seq:
                    clean_sequences.append(clean_seq)

            if not clean_sequences:
                logger.warning("未找到有效的蛋白质序列")
                return features

            # 合并所有蛋白质序列
            all_proteins = ''.join(clean_sequences)
            total_aa = len(all_proteins)

            if total_aa == 0:
                return features

            # 氨基酸频率
            aa_counts = Counter(all_proteins)
            for aa in 'ACDEFGHIKLMNPQRSTVWY':
                features[f'aa_{aa.lower()}_freq'] = aa_counts[aa] / total_aa

            # 温度适应性氨基酸特征
            thermostable_count = sum(aa_counts[aa] for aa in self.thermostable_aa if aa in aa_counts)
            thermolabile_count = sum(aa_counts[aa] for aa in self.thermolabile_aa if aa in aa_counts)

            features['aa_thermostable_ratio'] = thermostable_count / total_aa
            features['aa_thermolabile_ratio'] = thermolabile_count / total_aa

            # 氨基酸理化性质统计
            hydrophobic_count = polar_count = charged_count = 0
            positive_count = negative_count = 0
            total_mw = 0

            for aa in all_proteins:
                if aa in self.aa_properties:
                    props = self.aa_properties[aa]
                    if props['hydrophobic']:
                        hydrophobic_count += 1
                    if props['polar']:
                        polar_count += 1
                    if props['charged']:
                        charged_count += 1
                        if props['charge'] > 0:
                            positive_count += 1
                        elif props['charge'] < 0:
                            negative_count += 1
                    total_mw += props['mw']

            features['aa_hydrophobic_ratio'] = hydrophobic_count / total_aa
            features['aa_polar_ratio'] = polar_count / total_aa
            features['aa_charged_ratio'] = charged_count / total_aa
            features['aa_positive_ratio'] = positive_count / total_aa
            features['aa_negative_ratio'] = negative_count / total_aa
            features['aa_avg_molecular_weight'] = total_mw / total_aa

            # 氨基酸多样性 (Shannon熵)
            aa_freqs = [aa_counts[aa] / total_aa for aa in 'ACDEFGHIKLMNPQRSTVWY' if aa_counts[aa] > 0]
            if aa_freqs:
                shannon_entropy = -sum(f * np.log2(f) for f in aa_freqs if f > 0)
                features['aa_shannon_entropy'] = shannon_entropy
            else:
                features['aa_shannon_entropy'] = 0.0

            # 特殊氨基酸比例
            features['aa_glycine_ratio'] = aa_counts['G'] / total_aa  # 柔性
            features['aa_proline_ratio'] = aa_counts['P'] / total_aa  # 刚性
            features['aa_cysteine_ratio'] = aa_counts['C'] / total_aa  # 二硫键

            logger.info(f"氨基酸特征提取完成: {len([k for k in features.keys() if 'aa_' in k])} 个特征")

        except Exception as e:
            logger.error(f"氨基酸特征提取失败: {e}")

        return features

    def _extract_protein_pi_features(self, protein_file: str) -> Dict[str, float]:
        """提取蛋白质等电点特征"""
        features = {}

        if not BIOPYTHON_AVAILABLE:
            logger.warning("BioPython不可用，跳过蛋白质pI特征提取")
            return features

        try:
            pi_values = []
            instability_values = []

            # 读取蛋白质序列
            protein_sequences = self._load_sequences(protein_file)

            for seq in protein_sequences:
                clean_seq = seq.replace('*', '').replace('X', '')
                if clean_seq and all(aa in 'ACDEFGHIKLMNPQRSTVWY' for aa in clean_seq):
                    try:
                        analysis = ProteinAnalysis(clean_seq)

                        # 等电点
                        pi = analysis.isoelectric_point()
                        if 3.0 <= pi <= 13.0:  # 合理的pI范围
                            pi_values.append(pi)

                        # 不稳定性指数
                        instability = analysis.instability_index()
                        if 0 <= instability <= 100:  # 合理的不稳定性指数范围
                            instability_values.append(instability)

                    except Exception:
                        continue

            if pi_values:
                features['protein_pi_mean'] = np.mean(pi_values)
                features['protein_pi_std'] = np.std(pi_values)
                features['protein_pi_median'] = np.median(pi_values)
                features['protein_pi_min'] = np.min(pi_values)
                features['protein_pi_max'] = np.max(pi_values)

                # 酸性和碱性蛋白质比例
                acidic_count = sum(1 for pi in pi_values if pi < 7.0)
                basic_count = sum(1 for pi in pi_values if pi > 7.0)
                total_proteins = len(pi_values)

                features['protein_acidic_ratio'] = acidic_count / total_proteins
                features['protein_basic_ratio'] = basic_count / total_proteins
                features['protein_neutral_ratio'] = (total_proteins - acidic_count - basic_count) / total_proteins

                logger.info(f"蛋白质pI特征提取完成: 分析了 {len(pi_values)} 个蛋白质")

            if instability_values:
                features['protein_instability_mean'] = np.mean(instability_values)
                features['protein_instability_std'] = np.std(instability_values)

                # 稳定和不稳定蛋白质比例
                stable_count = sum(1 for inst in instability_values if inst < 40)
                unstable_count = sum(1 for inst in instability_values if inst >= 40)
                total_analyzed = len(instability_values)

                features['protein_stable_ratio'] = stable_count / total_analyzed
                features['protein_unstable_ratio'] = unstable_count / total_analyzed

        except Exception as e:
            logger.error(f"蛋白质pI特征提取失败: {e}")

        return features

class OptimizedBatchFeatureExtractor:
    """优化的批量特征提取器"""

    def __init__(self, output_dir: str = "optimized_genome_features"):
        """初始化批量特征提取器"""
        self.output_dir = output_dir
        self.extractor = OptimizedTemperatureFeatureExtractor()
        os.makedirs(output_dir, exist_ok=True)

    def extract_from_download_results(self, download_results_file: str,
                                    genome_base_dir: str) -> pd.DataFrame:
        """从下载结果文件批量提取特征"""
        logger.info(f"开始优化的批量特征提取")
        logger.info(f"下载结果文件: {download_results_file}")
        logger.info(f"基因组目录: {genome_base_dir}")

        # 读取下载结果
        df = pd.read_csv(download_results_file, sep='\t', encoding='utf-8')
        success_df = df[df['download_status'] == 'Success'].copy()

        logger.info(f"找到 {len(success_df)} 个成功下载的基因组")

        # 提取特征
        all_features = []

        for idx, row in success_df.iterrows():
            try:
                taxid = row['species_taxonid']
                download_path = row['download_path']

                logger.info(f"处理 taxid {taxid} ({idx+1}/{len(success_df)})")

                # 构建文件路径
                genome_dir = os.path.join(genome_base_dir, download_path)
                fasta_file = os.path.join(genome_dir, f"{taxid}_genomic.fna.gz")

                # 检查文件是否存在
                if not os.path.exists(fasta_file):
                    logger.warning(f"基因组文件不存在: {fasta_file}")
                    continue

                # 解压文件 (如果需要)
                temp_fasta = self._prepare_file(fasta_file)

                if temp_fasta:
                    # 提取特征
                    features = self.extractor.extract_features_from_genome(temp_fasta)

                    # 添加基本信息
                    features['taxid'] = taxid
                    features['species_name'] = row.get('species_name', '')
                    features['organism_type'] = row.get('organism_type.1', '')
                    features['optimum_temperature'] = row.get('optimum_temperature_for_growth', 0)

                    all_features.append(features)

                    # 清理临时文件
                    if temp_fasta != fasta_file:
                        os.remove(temp_fasta)

            except Exception as e:
                logger.error(f"处理 taxid {taxid} 失败: {e}")
                continue

        # 转换为DataFrame
        if all_features:
            features_df = pd.DataFrame(all_features)

            # 保存结果
            output_file = os.path.join(self.output_dir, 'optimized_genome_temperature_features.tsv')
            features_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')

            logger.info(f"优化特征提取完成: {len(features_df)} 个基因组")
            logger.info(f"特征数量: {len(features_df.columns) - 4}")  # 减去基本信息列
            logger.info(f"结果保存至: {output_file}")

            # 生成特征报告
            self.generate_feature_report(features_df)

            return features_df
        else:
            logger.error("未能提取任何特征")
            return pd.DataFrame()

    def _prepare_file(self, file_path: str) -> Optional[str]:
        """准备文件 (解压缩如果需要)"""
        try:
            if file_path.endswith('.gz'):
                # 创建临时文件
                temp_fd, temp_path = tempfile.mkstemp(suffix='.fasta')

                with gzip.open(file_path, 'rt') as gz_file:
                    with os.fdopen(temp_fd, 'w') as temp_file:
                        temp_file.write(gz_file.read())

                return temp_path
            else:
                return file_path

        except Exception as e:
            logger.error(f"准备文件失败: {e}")
            return None

    def generate_feature_report(self, features_df: pd.DataFrame):
        """生成优化的特征分析报告"""
        if features_df.empty:
            logger.warning("特征DataFrame为空，无法生成报告")
            return

        logger.info("生成优化的特征分析报告")

        # 基本统计
        num_genomes = len(features_df)
        num_features = len(features_df.columns) - 4  # 减去基本信息列

        # 按生物类型统计
        organism_counts = features_df['organism_type'].value_counts()

        # 温度范围统计
        temp_stats = features_df['optimum_temperature'].describe()

        report_content = f"""# 优化的基因组温度预测特征提取报告

## 提取概览
- 提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {num_genomes:,}
- 特征数量: {num_features}
- 特征提取器版本: 优化版 v2.0

## 按生物类型分布
"""

        for org_type, count in organism_counts.items():
            report_content += f"- **{org_type}**: {count} 个基因组\n"

        report_content += f"""
## 温度分布统计
- 平均温度: {temp_stats['mean']:.1f}°C
- 温度范围: {temp_stats['min']:.1f}°C - {temp_stats['max']:.1f}°C
- 标准差: {temp_stats['std']:.1f}°C

## 优化的特征类别

### 1. 基因组基本特征 (增强版)
- 基因组大小、contig统计
- N50、基因组碎片化程度
- 最大contig占比

### 2. GC含量特征 (详细分析)
- 整体GC含量、滑动窗口GC分析
- GC含量变异系数、GC偏斜分析
- 局部GC含量分布特征

### 3. 核苷酸组成特征 (扩展版)
- 单核苷酸和二核苷酸频率
- CpG含量分析
- 核苷酸偏斜特征

### 4. 序列复杂度特征 (新增)
- k-mer多样性 (k=2,3,4)
- 序列熵分析
- 语言复杂度指数

### 5. 重复序列特征 (新增)
- 简单重复序列含量
- 回文序列密度
- 重复模式分析

### 6. 热稳定性特征 (核心)
- 氢键强度分析
- 堆叠能计算
- 热稳定性指数

### 7. 密码子使用特征 (温度适应性)
- 有效密码子数 (ENC)
- 密码子使用偏好 (CUB)
- 热稳定性密码子比例
- GC富集/贫乏密码子分析

### 8. 氨基酸组成特征 (温度相关)
- 热稳定性/敏感性氨基酸比例
- 理化性质统计
- 特殊氨基酸分析

### 9. 蛋白质特征 (生物物理)
- 等电点分布分析
- 蛋白质稳定性指数
- 酸碱性比例

## 关键特征统计
"""

        # 选择关键特征进行统计
        key_features = [
            'gc_content', 'thermal_stability_index', 'avg_hydrogen_bonds',
            'sequence_entropy', 'enc', 'aa_thermostable_ratio',
            'protein_pi_mean', 'genome_size'
        ]

        for feature in key_features:
            if feature in features_df.columns:
                stats = features_df[feature].describe()
                report_content += f"\n### {feature}\n"
                report_content += f"- 平均值: {stats['mean']:.6f}\n"
                report_content += f"- 标准差: {stats['std']:.6f}\n"
                report_content += f"- 范围: {stats['min']:.6f} - {stats['max']:.6f}\n"

        report_content += f"""
## 温度预测相关性分析

基于生物学原理，以下特征预期与温度有较强相关性：

1. **GC含量相关**: gc_content, thermal_stability_index
2. **氢键强度**: avg_hydrogen_bonds
3. **氨基酸组成**: aa_thermostable_ratio, aa_thermolabile_ratio
4. **密码子使用**: thermostable_codon_ratio, gc_rich_codon_ratio
5. **蛋白质稳定性**: protein_stable_ratio, protein_instability_mean

## 使用建议

1. **特征选择**: 建议使用相关性分析和特征重要性评估筛选最佳特征
2. **数据预处理**: 建议对特征进行标准化或归一化处理
3. **模型选择**: 可尝试线性回归、随机森林、梯度提升等算法
4. **交叉验证**: 使用k折交叉验证评估模型性能
5. **特征工程**: 可考虑特征组合和多项式特征

## 输出文件
- 特征数据: optimized_genome_temperature_features.tsv
- 分析报告: optimized_feature_extraction_report.md

## 技术改进
相比基础版本，本优化版本增加了：
- {num_features - 26} 个新特征
- 温度适应性特征分析
- 序列复杂度和重复序列分析
- 蛋白质稳定性特征
- 更详细的GC含量分析
"""

        # 保存报告
        report_file = os.path.join(self.output_dir, 'optimized_feature_extraction_report.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"优化特征分析报告已保存: {report_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='优化的基因组序列最适生长温度预测特征提取器')
    parser.add_argument('--download-results', required=True,
                       help='下载结果TSV文件路径')
    parser.add_argument('--genome-dir', required=True,
                       help='基因组文件基础目录')
    parser.add_argument('--output-dir', default='optimized_genome_features',
                       help='输出目录 (默认: optimized_genome_features)')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.download_results):
        logger.error(f"下载结果文件不存在: {args.download_results}")
        return

    if not os.path.exists(args.genome_dir):
        logger.error(f"基因组目录不存在: {args.genome_dir}")
        return

    # 创建批量提取器
    batch_extractor = OptimizedBatchFeatureExtractor(args.output_dir)

    # 提取特征
    features_df = batch_extractor.extract_from_download_results(
        args.download_results,
        args.genome_dir
    )

    if not features_df.empty:
        logger.info("✅ 优化特征提取任务完成！")
        logger.info(f"提取了 {len(features_df.columns) - 4} 个特征")
        logger.info(f"处理了 {len(features_df)} 个基因组")
    else:
        logger.error("❌ 优化特征提取任务失败！")

if __name__ == "__main__":
    main()
