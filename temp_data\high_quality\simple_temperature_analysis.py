#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的基因组温度特征分析

不依赖机器学习库，进行基本的统计分析和相关性分析
"""

import pandas as pd
import numpy as np
import os

class SimpleTemperatureAnalyzer:
    """简单温度分析器"""
    
    def __init__(self, features_file):
        """初始化分析器"""
        self.features_file = features_file
        self.df = None
        
    def load_data(self):
        """加载数据"""
        print("=== 加载特征数据 ===")
        
        self.df = pd.read_csv(self.features_file, sep='\t')
        print(f"数据形状: {self.df.shape}")
        print(f"基因组数量: {len(self.df)}")
        
        # 分离特征和目标变量
        self.feature_columns = [col for col in self.df.columns 
                               if col not in ['taxid', 'species_name', 'organism_type', 'optimum_temperature']]
        
        print(f"特征数量: {len(self.feature_columns)}")
        print(f"温度范围: {self.df['optimum_temperature'].min():.1f}°C - {self.df['optimum_temperature'].max():.1f}°C")
        
    def analyze_correlations(self):
        """分析特征与温度的相关性"""
        print("\n=== 特征-温度相关性分析 ===")
        
        correlations = []
        
        for feature in self.feature_columns:
            # 计算皮尔逊相关系数
            corr = np.corrcoef(self.df[feature], self.df['optimum_temperature'])[0, 1]
            
            if not np.isnan(corr):
                correlations.append({
                    'feature': feature,
                    'correlation': corr,
                    'abs_correlation': abs(corr)
                })
        
        # 按绝对相关系数排序
        correlations_df = pd.DataFrame(correlations)
        correlations_df = correlations_df.sort_values('abs_correlation', ascending=False)
        
        print("前15个与温度相关性最强的特征:")
        for i, (_, row) in enumerate(correlations_df.head(15).iterrows(), 1):
            print(f"  {i:2d}. {row['feature']:25s}: {row['correlation']:+7.4f}")
        
        return correlations_df
    
    def analyze_feature_statistics(self):
        """分析特征统计信息"""
        print("\n=== 特征统计分析 ===")
        
        # 计算基本统计信息
        stats_df = self.df[self.feature_columns].describe()
        
        print("关键特征统计:")
        key_features = ['gc_content', 'genome_size', 'at_content', 'gc_skew', 'at_skew']
        
        for feature in key_features:
            if feature in stats_df.columns:
                stats = stats_df[feature]
                print(f"\n{feature}:")
                print(f"  平均值: {stats['mean']:.6f}")
                print(f"  标准差: {stats['std']:.6f}")
                print(f"  范围: {stats['min']:.6f} - {stats['max']:.6f}")
        
        return stats_df
    
    def analyze_temperature_groups(self):
        """按温度分组分析"""
        print("\n=== 温度分组分析 ===")
        
        # 定义温度组
        temp_groups = []
        for _, row in self.df.iterrows():
            temp = row['optimum_temperature']
            if temp <= 20:
                group = "低温 (≤20°C)"
            elif temp <= 30:
                group = "中温 (21-30°C)"
            else:
                group = "高温 (>30°C)"
            temp_groups.append(group)
        
        self.df['temp_group'] = temp_groups
        
        # 按组统计
        group_stats = self.df.groupby('temp_group').agg({
            'optimum_temperature': ['count', 'mean', 'std'],
            'gc_content': 'mean',
            'genome_size': 'mean',
            'at_content': 'mean'
        }).round(4)
        
        print("温度分组统计:")
        print(group_stats)
        
        return group_stats
    
    def simple_linear_prediction(self, correlations_df):
        """使用最相关的特征进行简单线性预测"""
        print("\n=== 简单线性预测 ===")
        
        # 选择相关性最强的特征
        best_feature = correlations_df.iloc[0]['feature']
        best_corr = correlations_df.iloc[0]['correlation']
        
        print(f"使用最相关特征进行预测: {best_feature} (相关系数: {best_corr:.4f})")
        
        # 简单线性回归 y = ax + b
        x = self.df[best_feature].values
        y = self.df['optimum_temperature'].values
        
        # 计算回归系数
        x_mean = np.mean(x)
        y_mean = np.mean(y)
        
        numerator = np.sum((x - x_mean) * (y - y_mean))
        denominator = np.sum((x - x_mean) ** 2)
        
        if denominator != 0:
            slope = numerator / denominator
            intercept = y_mean - slope * x_mean
            
            # 预测
            y_pred = slope * x + intercept
            
            # 计算误差
            mse = np.mean((y - y_pred) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(y - y_pred))
            
            print(f"线性回归方程: 温度 = {slope:.4f} × {best_feature} + {intercept:.4f}")
            print(f"预测性能:")
            print(f"  RMSE: {rmse:.2f}°C")
            print(f"  MAE:  {mae:.2f}°C")
            
            # 显示预测结果
            print(f"\n预测结果:")
            for i, (actual, predicted) in enumerate(zip(y, y_pred)):
                taxid = self.df.iloc[i]['taxid']
                species = self.df.iloc[i]['species_name']
                error = actual - predicted
                print(f"  taxid {taxid:2d} ({species:12s}): 实际 {actual:4.1f}°C, "
                      f"预测 {predicted:4.1f}°C, 误差 {error:+5.1f}°C")
            
            return {
                'feature': best_feature,
                'slope': slope,
                'intercept': intercept,
                'rmse': rmse,
                'mae': mae,
                'predictions': y_pred
            }
        else:
            print("无法进行线性回归预测")
            return None
    
    def generate_analysis_report(self, correlations_df, stats_df, group_stats, prediction_result):
        """生成分析报告"""
        print("\n=== 生成分析报告 ===")
        
        report_content = f"""# 基因组温度特征分析报告

## 数据概览
- 分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {len(self.df)}
- 特征数量: {len(self.feature_columns)}
- 温度范围: {self.df['optimum_temperature'].min():.1f}°C - {self.df['optimum_temperature'].max():.1f}°C

## 温度分布
- 平均温度: {self.df['optimum_temperature'].mean():.1f}°C
- 温度标准差: {self.df['optimum_temperature'].std():.1f}°C

## 最相关特征 (前10个)
"""
        
        for i, (_, row) in enumerate(correlations_df.head(10).iterrows(), 1):
            report_content += f"{i}. **{row['feature']}**: {row['correlation']:+.4f}\n"
        
        report_content += f"""
## 关键特征统计
"""
        
        key_features = ['gc_content', 'genome_size', 'at_content', 'gc_skew', 'at_skew']
        for feature in key_features:
            if feature in stats_df.columns:
                stats = stats_df[feature]
                report_content += f"\n### {feature}\n"
                report_content += f"- 平均值: {stats['mean']:.6f}\n"
                report_content += f"- 标准差: {stats['std']:.6f}\n"
                report_content += f"- 范围: {stats['min']:.6f} - {stats['max']:.6f}\n"
        
        if prediction_result:
            report_content += f"""
## 简单线性预测结果
- 最佳预测特征: {prediction_result['feature']}
- 回归方程: 温度 = {prediction_result['slope']:.4f} × {prediction_result['feature']} + {prediction_result['intercept']:.4f}
- RMSE: {prediction_result['rmse']:.2f}°C
- MAE: {prediction_result['mae']:.2f}°C

### 预测详情
"""
            for i, row in self.df.iterrows():
                actual = row['optimum_temperature']
                predicted = prediction_result['predictions'][i]
                error = actual - predicted
                report_content += f"- **taxid {row['taxid']}** ({row['species_name']}): "
                report_content += f"实际 {actual:.1f}°C, 预测 {predicted:.1f}°C, 误差 {error:+.1f}°C\n"
        
        report_content += f"""
## 生物学意义
1. **GC含量与温度**: GC含量通常与热稳定性相关，高温菌往往具有较高的GC含量
2. **基因组大小**: 不同温度环境的微生物可能具有不同的基因组大小特征
3. **核苷酸偏斜**: AT/GC偏斜可能反映了温度适应的进化压力
4. **二核苷酸频率**: 特定的二核苷酸组合可能与温度适应性相关

## 改进建议
1. 增加更多基因组样本，特别是极端温度环境的微生物
2. 添加更多特征，如密码子使用偏好、氨基酸组成、tRNA特征等
3. 考虑使用机器学习方法进行更复杂的建模
4. 验证预测结果的生物学合理性

## 输出文件
- 特征相关性: feature_correlations.tsv
- 分析报告: simple_temperature_analysis_report.md
"""
        
        # 保存报告
        with open('simple_temperature_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        # 保存相关性数据
        correlations_df.to_csv('feature_correlations.tsv', sep='\t', index=False, encoding='utf-8')
        
        print("分析报告已保存: simple_temperature_analysis_report.md")
        print("特征相关性已保存: feature_correlations.tsv")

def main():
    """主函数"""
    print("基因组温度特征简单分析")
    print("=" * 40)
    
    # 检查特征文件
    features_file = "test_features/genome_temperature_features.tsv"
    if not os.path.exists(features_file):
        print(f"❌ 特征文件不存在: {features_file}")
        print("请先运行特征提取器生成特征数据")
        return
    
    # 创建分析器
    analyzer = SimpleTemperatureAnalyzer(features_file)
    
    # 加载数据
    analyzer.load_data()
    
    # 分析相关性
    correlations_df = analyzer.analyze_correlations()
    
    # 分析特征统计
    stats_df = analyzer.analyze_feature_statistics()
    
    # 温度分组分析
    group_stats = analyzer.analyze_temperature_groups()
    
    # 简单线性预测
    prediction_result = analyzer.simple_linear_prediction(correlations_df)
    
    # 生成报告
    analyzer.generate_analysis_report(correlations_df, stats_df, group_stats, prediction_result)
    
    print("\n✅ 温度特征分析完成！")

if __name__ == "__main__":
    main()
