#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的温度特征提取示例脚本

这个脚本解决了原始脚本的导入问题，提供了一个可以直接运行的特征提取工具。

使用方法:
    # 单个基因组特征提取
    python simple_extract_features.py single --genome genome.fna --output features.npz
    
    # 批量特征提取
    python simple_extract_features.py batch --metadata genomes.tsv --output-dir features/ --threads 8
"""

import os
import sys
import argparse
import logging
import time
import pandas as pd
import numpy as np
from pathlib import Path

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 尝试导入优化的提取器
try:
    from optimized_extractor import OptimizedTemperatureExtractor
    OPTIMIZED_AVAILABLE = True
    print("✅ 使用优化的特征提取器")
except ImportError as e:
    print(f"⚠️  优化提取器不可用: {e}")
    OPTIMIZED_AVAILABLE = False

# 如果优化提取器不可用，创建简化版本
if not OPTIMIZED_AVAILABLE:
    class SimpleTemperatureExtractor:
        """简化的温度特征提取器"""
        
        def __init__(self, output_dir="features", **kwargs):
            self.output_dir = Path(output_dir)
            self.output_dir.mkdir(parents=True, exist_ok=True)
            
            # 设置日志
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger("SimpleExtractor")
        
        def extract_single_genome(self, genome_id, genome_file, **kwargs):
            """提取单个基因组的基本特征"""
            features = {}
            
            try:
                # 读取基因组文件
                with open(genome_file, 'r') as f:
                    content = f.read()
                
                # 提取序列（去除FASTA头）
                lines = content.strip().split('\n')
                sequence = ''.join(line for line in lines if not line.startswith('>'))
                sequence = sequence.upper()
                
                # 基本特征
                features['genome_size'] = len(sequence)
                features['gc_content'] = (sequence.count('G') + sequence.count('C')) / len(sequence) if len(sequence) > 0 else 0
                features['at_content'] = (sequence.count('A') + sequence.count('T')) / len(sequence) if len(sequence) > 0 else 0
                
                # 核苷酸频率
                for base in 'ATGC':
                    features[f'{base.lower()}_freq'] = sequence.count(base) / len(sequence) if len(sequence) > 0 else 0
                
                # 二核苷酸频率
                dinucs = ['AA', 'AT', 'AG', 'AC', 'TA', 'TT', 'TG', 'TC', 'GA', 'GT', 'GG', 'GC', 'CA', 'CT', 'CG', 'CC']
                for dinuc in dinucs:
                    count = 0
                    for i in range(len(sequence) - 1):
                        if sequence[i:i+2] == dinuc:
                            count += 1
                    features[f'dinuc_{dinuc.lower()}'] = count / (len(sequence) - 1) if len(sequence) > 1 else 0
                
                # 添加温度和ID
                if 'temperature' in kwargs and kwargs['temperature'] is not None:
                    features['temperature'] = kwargs['temperature']
                features['genome_id'] = genome_id
                
                self.logger.info(f"提取了基因组 {genome_id} 的 {len(features)} 个特征")
                return features
                
            except Exception as e:
                self.logger.error(f"提取基因组 {genome_id} 特征失败: {e}")
                raise
        
        def extract_batch(self, metadata_file, output_file="batch_features.npz", **kwargs):
            """批量提取特征"""
            try:
                # 读取元数据
                metadata = pd.read_csv(metadata_file, sep='\t')
                self.logger.info(f"读取了 {len(metadata)} 个基因组的元数据")
                
                all_features = []
                feature_names = None
                
                for idx, row in metadata.iterrows():
                    genome_id = row.get('genome_id', f'genome_{idx}')
                    genome_file = row.get('genome_file')
                    temperature = row.get('temperature')
                    
                    if pd.isna(genome_file) or not os.path.exists(genome_file):
                        self.logger.warning(f"跳过基因组 {genome_id}: 文件不存在")
                        continue
                    
                    # 提取特征
                    features = self.extract_single_genome(
                        genome_id=genome_id,
                        genome_file=genome_file,
                        temperature=temperature if not pd.isna(temperature) else None
                    )
                    
                    # 转换为向量
                    feature_vector, names = self._features_to_vector(features)
                    if feature_names is None:
                        feature_names = names
                    
                    all_features.append(feature_vector)
                
                # 保存结果
                if all_features:
                    X = np.array(all_features)
                    output_path = self.output_dir / output_file
                    
                    np.savez_compressed(
                        str(output_path),
                        features=X,
                        feature_names=feature_names,
                        metadata=metadata.to_dict('records')
                    )
                    
                    # 保存TSV
                    tsv_path = output_path.with_suffix('.tsv')
                    feature_df = pd.DataFrame(X, columns=feature_names)
                    feature_df.to_csv(str(tsv_path), sep='\t', index=False)
                    
                    self.logger.info(f"批量特征提取完成: {output_path}")
                    return str(output_path)
                else:
                    raise ValueError("没有成功提取任何特征")
                    
            except Exception as e:
                self.logger.error(f"批量特征提取失败: {e}")
                raise
        
        def _features_to_vector(self, features):
            """将特征字典转换为向量"""
            vector = []
            names = []
            
            for key, value in features.items():
                if key in ['genome_id']:  # 跳过非数值特征
                    continue
                if isinstance(value, (int, float)):
                    vector.append(float(value))
                    names.append(key)
            
            return np.array(vector), names

def extract_single(args):
    """单个基因组特征提取"""
    print("=== 单个基因组特征提取 ===")
    
    # 创建提取器
    if OPTIMIZED_AVAILABLE:
        extractor = OptimizedTemperatureExtractor(
            num_threads=args.threads,
            enable_preprocessing=False,
            enable_feature_selection=False,
            enable_scaling=False,
            output_dir=os.path.dirname(args.output) or ".",
            log_level=args.log_level
        )
    else:
        extractor = SimpleTemperatureExtractor(
            output_dir=os.path.dirname(args.output) or "."
        )
    
    try:
        # 提取特征
        features = extractor.extract_single_genome(
            genome_id=args.genome_id or Path(args.genome).stem,
            genome_file=args.genome,
            cds_file=getattr(args, 'cds', None),
            protein_file=getattr(args, 'protein', None),
            ko_file=getattr(args, 'ko', None),
            temperature=getattr(args, 'temperature', None)
        )
        
        # 保存结果
        if OPTIMIZED_AVAILABLE:
            feature_vector, feature_names = extractor._features_to_vector(features)
        else:
            feature_vector, feature_names = extractor._features_to_vector(features)
        
        np.savez_compressed(
            args.output,
            features=feature_vector.reshape(1, -1),
            feature_names=feature_names,
            genome_id=args.genome_id or Path(args.genome).stem
        )
        
        print(f"✅ 特征提取完成!")
        print(f"   基因组: {args.genome}")
        print(f"   特征数: {len(feature_names)}")
        print(f"   输出: {args.output}")
        
    except Exception as e:
        print(f"❌ 特征提取失败: {e}")
        return 1
    
    return 0

def extract_batch(args):
    """批量特征提取"""
    print("=== 批量特征提取 ===")
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建提取器
    if OPTIMIZED_AVAILABLE:
        extractor = OptimizedTemperatureExtractor(
            num_threads=args.threads,
            enable_preprocessing=False,
            enable_feature_selection=False,
            enable_scaling=False,
            output_dir=str(output_dir),
            log_level=args.log_level
        )
    else:
        extractor = SimpleTemperatureExtractor(
            output_dir=str(output_dir)
        )
    
    try:
        start_time = time.time()
        
        # 批量提取特征
        output_file = extractor.extract_batch(
            metadata_file=args.metadata,
            output_file=args.output_file
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 批量特征提取完成!")
        print(f"   元数据文件: {args.metadata}")
        print(f"   输出文件: {output_file}")
        print(f"   处理时间: {duration:.2f} 秒")
        print(f"   提取器类型: {'优化版' if OPTIMIZED_AVAILABLE else '简化版'}")
        
    except Exception as e:
        print(f"❌ 批量特征提取失败: {e}")
        return 1
    
    return 0

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="简化的温度特征提取器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 单个基因组提取
    single_parser = subparsers.add_parser('single', help='单个基因组特征提取')
    single_parser.add_argument('--genome', required=True, help='基因组FASTA文件路径')
    single_parser.add_argument('--genome-id', help='基因组ID')
    single_parser.add_argument('--output', required=True, help='输出文件路径')
    single_parser.add_argument('--temperature', type=float, help='已知温度')
    
    # 批量提取
    batch_parser = subparsers.add_parser('batch', help='批量特征提取')
    batch_parser.add_argument('--metadata', required=True, help='元数据TSV文件路径')
    batch_parser.add_argument('--output-dir', required=True, help='输出目录')
    batch_parser.add_argument('--output-file', default='batch_features.npz', help='输出文件名')
    
    # 共同参数
    for p in [single_parser, batch_parser]:
        p.add_argument('--threads', type=int, default=4, help='线程数')
        p.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                      default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # 执行相应命令
    if args.command == 'single':
        return extract_single(args)
    elif args.command == 'batch':
        return extract_batch(args)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
