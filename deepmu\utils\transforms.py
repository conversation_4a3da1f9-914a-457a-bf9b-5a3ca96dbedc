"""
DeepMu的数据变换工具。
"""

import torch
import numpy as np
from typing import Dict, Optional, Union, Tuple

class LogTransform:
    """
    目标值的对数变换。

    该变换应用对数变换，使模型更关注
    相对误差而不是绝对误差。
    """

    def __init__(self, epsilon=1e-8):
        """
        初始化对数变换。

        参数:
            epsilon: 避免log(0)的小常数 (默认: 1e-8)
        """
        self.epsilon = epsilon

    def __call__(self, x):
        """
        应用对数变换。

        参数:
            x: 输入张量或数组

        返回:
            对数变换后的值
        """
        if isinstance(x, torch.Tensor):
            return torch.log(torch.abs(x) + self.epsilon)
        else:
            return np.log(np.abs(x) + self.epsilon)

    def inverse(self, x):
        """
        应用逆对数变换。

        参数:
            x: 对数变换后的张量或数组

        返回:
            原始尺度的值
        """
        if isinstance(x, torch.Tensor):
            return torch.exp(x) - self.epsilon
        else:
            return np.exp(x) - self.epsilon

class MinMaxScaler:
    """
    特征归一化的最小-最大缩放。

    该变换将特征缩放到指定范围，通常是[0, 1]。
    """

    def __init__(self, feature_range=(0, 1)):
        """
        初始化最小-最大缩放器。

        参数:
            feature_range: 缩放数据的输出范围 (默认: (0, 1))
        """
        self.feature_range = feature_range
        self.min_ = None
        self.scale_ = None

    def fit(self, x):
        """
        计算用于缩放的最小值和最大值。

        参数:
            x: 输入张量或数组

        返回:
            自身
        """
        if isinstance(x, torch.Tensor):
            self.min_ = torch.min(x, dim=0)[0]
            self.max_ = torch.max(x, dim=0)[0]
            self.scale_ = (self.feature_range[1] - self.feature_range[0]) / (self.max_ - self.min_ + 1e-8)
        else:
            self.min_ = np.min(x, axis=0)
            self.max_ = np.max(x, axis=0)
            self.scale_ = (self.feature_range[1] - self.feature_range[0]) / (self.max_ - self.min_ + 1e-8)
        return self

    def transform(self, x):
        """
        将特征缩放到特征范围。

        参数:
            x: 输入张量或数组

        返回:
            缩放后的值
        """
        if self.min_ is None or self.scale_ is None:
            raise ValueError("缩放器尚未拟合。请先调用fit()。")

        if isinstance(x, torch.Tensor):
            return self.feature_range[0] + (x - self.min_) * self.scale_
        else:
            return self.feature_range[0] + (x - self.min_) * self.scale_

    def fit_transform(self, x):
        """
        拟合数据，然后变换它。

        参数:
            x: 输入张量或数组

        返回:
            缩放后的值
        """
        return self.fit(x).transform(x)

    def inverse_transform(self, x):
        """
        撤销缩放。

        参数:
            x: 缩放后的张量或数组

        返回:
            原始尺度的值
        """
        if self.min_ is None or self.scale_ is None:
            raise ValueError("缩放器尚未拟合。请先调用fit()。")

        if isinstance(x, torch.Tensor):
            return self.min_ + (x - self.feature_range[0]) / self.scale_
        else:
            return self.min_ + (x - self.feature_range[0]) / self.scale_

class StandardScaler:
    """
    特征归一化的标准化。

    该变换通过移除均值并缩放到单位方差来标准化特征。
    """

    def __init__(self):
        """初始化标准缩放器。"""
        self.mean_ = None
        self.std_ = None

    def fit(self, x):
        """
        计算用于缩放的均值和标准差。

        参数:
            x: 输入张量或数组

        返回:
            自身
        """
        if isinstance(x, torch.Tensor):
            self.mean_ = torch.mean(x, dim=0)
            self.std_ = torch.std(x, dim=0) + 1e-8  # 添加小的epsilon避免除零
        else:
            self.mean_ = np.mean(x, axis=0)
            self.std_ = np.std(x, axis=0) + 1e-8
        return self

    def transform(self, x):
        """
        标准化特征。

        参数:
            x: 输入张量或数组

        返回:
            标准化后的值
        """
        if self.mean_ is None or self.std_ is None:
            raise ValueError("缩放器尚未拟合。请先调用fit()。")

        if isinstance(x, torch.Tensor):
            return (x - self.mean_) / self.std_
        else:
            return (x - self.mean_) / self.std_

    def fit_transform(self, x):
        """
        拟合数据，然后变换它。

        参数:
            x: 输入张量或数组

        返回:
            标准化后的值
        """
        return self.fit(x).transform(x)

    def inverse_transform(self, x):
        """
        撤销标准化。

        参数:
            x: 标准化后的张量或数组

        返回:
            原始尺度的值
        """
        if self.mean_ is None or self.std_ is None:
            raise ValueError("缩放器尚未拟合。请先调用fit()。")

        if isinstance(x, torch.Tensor):
            return x * self.std_ + self.mean_
        else:
            return x * self.std_ + self.mean_
