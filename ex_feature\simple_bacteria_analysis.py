#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的细菌基因组温度特征分析器

不依赖sklearn，提供基本的相关性分析和可视化功能。
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
import logging
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleBacteriaAnalyzer:
    """简化的细菌基因组特征分析器"""
    
    def __init__(self, features_file: str, output_dir: str = "simple_analysis"):
        """初始化分析器"""
        self.features_file = features_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载数据
        self.df = self._load_data()
        
        # 分离特征和目标
        self.feature_columns = [col for col in self.df.columns 
                               if col not in ['taxid', 'species_name', 'temperature', 'organism_type', 'assembly_level', 'quality_score']]
        
        logger.info(f"加载了 {len(self.df)} 个基因组，{len(self.feature_columns)} 个特征")
    
    def _load_data(self) -> pd.DataFrame:
        """加载特征数据"""
        try:
            df = pd.read_csv(self.features_file, sep='\t')
            logger.info(f"成功加载特征数据: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"加载特征数据失败: {e}")
            raise
    
    def analyze_correlations(self) -> pd.DataFrame:
        """分析特征与温度的相关性"""
        logger.info("分析特征与温度的相关性")
        
        correlations = []
        
        for feature in self.feature_columns:
            try:
                # 计算皮尔逊相关系数
                corr = np.corrcoef(self.df[feature], self.df['temperature'])[0, 1]
                
                if not np.isnan(corr):
                    # 确定特征类别
                    if feature.startswith('genomic_'):
                        category = '基因组特征'
                    elif feature.startswith('cds_'):
                        category = '密码子特征'
                    elif any(x in feature for x in ['gene_count', 'cds_count', 'trna_count', 'rrna_count']):
                        category = '基因注释特征'
                    else:
                        category = '其他特征'
                    
                    correlations.append({
                        'feature': feature,
                        'correlation': corr,
                        'abs_correlation': abs(corr),
                        'category': category
                    })
            except Exception as e:
                logger.warning(f"计算特征 {feature} 相关性失败: {e}")
        
        # 转换为DataFrame并排序
        corr_df = pd.DataFrame(correlations)
        corr_df = corr_df.sort_values('abs_correlation', ascending=False)
        
        # 保存相关性结果
        output_file = self.output_dir / "feature_correlations.tsv"
        corr_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
        
        logger.info(f"相关性分析完成，结果保存到: {output_file}")
        
        return corr_df
    
    def generate_visualizations(self, corr_df: pd.DataFrame):
        """生成可视化图表"""
        logger.info("生成可视化图表")
        
        try:
            # 设置图表样式
            plt.style.use('default')
            sns.set_palette("husl")
            
            # 创建主要分析图
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            
            # 1. 温度分布直方图
            axes[0, 0].hist(self.df['temperature'], bins=8, alpha=0.7, edgecolor='black', color='skyblue')
            axes[0, 0].set_xlabel('Temperature (°C)')
            axes[0, 0].set_ylabel('Number of Genomes')
            axes[0, 0].set_title('Temperature Distribution')
            axes[0, 0].grid(alpha=0.3)
            
            # 2. GC含量与温度散点图
            axes[0, 1].scatter(self.df['genomic_gc_content'], self.df['temperature'], 
                              alpha=0.8, s=100, color='coral')
            axes[0, 1].set_xlabel('GC Content')
            axes[0, 1].set_ylabel('Temperature (°C)')
            axes[0, 1].set_title('GC Content vs Temperature')
            
            # 添加趋势线
            z = np.polyfit(self.df['genomic_gc_content'], self.df['temperature'], 1)
            p = np.poly1d(z)
            axes[0, 1].plot(self.df['genomic_gc_content'], p(self.df['genomic_gc_content']), 
                           "r--", alpha=0.8, linewidth=2)
            
            # 计算相关系数
            gc_corr = np.corrcoef(self.df['genomic_gc_content'], self.df['temperature'])[0, 1]
            axes[0, 1].text(0.05, 0.95, f'r = {gc_corr:.3f}', transform=axes[0, 1].transAxes,
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
            
            # 3. 基因组大小与温度
            axes[1, 0].scatter(self.df['genomic_genome_size']/1e6, self.df['temperature'], 
                              alpha=0.8, s=100, color='lightgreen')
            axes[1, 0].set_xlabel('Genome Size (Mb)')
            axes[1, 0].set_ylabel('Temperature (°C)')
            axes[1, 0].set_title('Genome Size vs Temperature')
            
            # 4. 特征相关性条形图（前10个）
            top_features = corr_df.head(10)
            y_pos = np.arange(len(top_features))
            
            colors = ['red' if x < 0 else 'blue' for x in top_features['correlation']]
            bars = axes[1, 1].barh(y_pos, top_features['correlation'], color=colors, alpha=0.7)
            axes[1, 1].set_yticks(y_pos)
            axes[1, 1].set_yticklabels([f.replace('genomic_', '').replace('cds_', '') 
                                       for f in top_features['feature']], fontsize=8)
            axes[1, 1].set_xlabel('Correlation with Temperature')
            axes[1, 1].set_title('Top 10 Most Correlated Features')
            axes[1, 1].grid(axis='x', alpha=0.3)
            
            # 添加数值标签
            for i, (bar, corr) in enumerate(zip(bars, top_features['correlation'])):
                axes[1, 1].text(corr + (0.01 if corr > 0 else -0.01), i, f'{corr:.3f}', 
                               va='center', ha='left' if corr > 0 else 'right', fontsize=8)
            
            plt.tight_layout()
            plt.savefig(self.output_dir / 'bacteria_feature_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 生成详细的特征分析图
            self._generate_detailed_plots(corr_df)
            
            logger.info("可视化图表生成完成")
            
        except Exception as e:
            logger.error(f"生成可视化失败: {e}")
    
    def _generate_detailed_plots(self, corr_df: pd.DataFrame):
        """生成详细的特征分析图"""
        try:
            # 选择最相关的6个特征
            top_6_features = corr_df.head(6)['feature'].tolist()
            
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            axes = axes.flatten()
            
            for i, feature in enumerate(top_6_features):
                ax = axes[i]
                
                # 散点图
                ax.scatter(self.df[feature], self.df['temperature'], alpha=0.8, s=100)
                
                # 趋势线
                z = np.polyfit(self.df[feature], self.df['temperature'], 1)
                p = np.poly1d(z)
                ax.plot(self.df[feature], p(self.df[feature]), "r--", alpha=0.8, linewidth=2)
                
                # 标签和标题
                feature_name = feature.replace('genomic_', '').replace('cds_', '')
                ax.set_xlabel(feature_name)
                ax.set_ylabel('Temperature (°C)')
                
                # 相关系数
                corr = corr_df[corr_df['feature'] == feature]['correlation'].iloc[0]
                ax.set_title(f'{feature_name}\nr = {corr:.3f}')
                
                # 添加基因组标签
                for idx, row in self.df.iterrows():
                    ax.annotate(f"{row['taxid']}", 
                               (row[feature], row['temperature']),
                               xytext=(5, 5), textcoords='offset points',
                               fontsize=8, alpha=0.7)
            
            plt.tight_layout()
            plt.savefig(self.output_dir / 'top_features_detailed.png', dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            logger.error(f"生成详细图表失败: {e}")
    
    def simple_linear_regression(self, x: np.ndarray, y: np.ndarray) -> dict:
        """简单线性回归"""
        # 计算回归系数
        n = len(x)
        x_mean = np.mean(x)
        y_mean = np.mean(y)
        
        # 计算斜率和截距
        numerator = np.sum((x - x_mean) * (y - y_mean))
        denominator = np.sum((x - x_mean) ** 2)
        
        if denominator == 0:
            return {'slope': 0, 'intercept': y_mean, 'r2': 0, 'rmse': np.std(y)}
        
        slope = numerator / denominator
        intercept = y_mean - slope * x_mean
        
        # 预测值
        y_pred = slope * x + intercept
        
        # 计算R²
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - y_mean) ** 2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # 计算RMSE
        rmse = np.sqrt(np.mean((y - y_pred) ** 2))
        
        return {
            'slope': slope,
            'intercept': intercept,
            'r2': r2,
            'rmse': rmse,
            'predictions': y_pred
        }
    
    def build_simple_models(self, corr_df: pd.DataFrame):
        """构建简单的预测模型"""
        logger.info("构建简单预测模型")
        
        # 选择前5个最相关的特征
        top_features = corr_df.head(5)['feature'].tolist()
        
        y = self.df['temperature'].values
        models = {}
        
        # 单特征模型
        for feature in top_features:
            x = self.df[feature].values
            model = self.simple_linear_regression(x, y)
            models[feature] = model
            
            logger.info(f"{feature}: R² = {model['r2']:.3f}, RMSE = {model['rmse']:.2f}°C")
        
        # 保存模型结果
        self._save_simple_model_results(models, top_features)
        
        return models
    
    def _save_simple_model_results(self, models: dict, features: list):
        """保存简单模型结果"""
        # 创建预测结果DataFrame
        predictions_df = self.df[['taxid', 'species_name', 'temperature']].copy()
        
        for feature in features:
            if feature in models:
                predictions_df[f'{feature}_prediction'] = models[feature]['predictions']
                predictions_df[f'{feature}_error'] = predictions_df['temperature'] - models[feature]['predictions']
        
        # 保存预测结果
        output_file = self.output_dir / "simple_predictions.tsv"
        predictions_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
        
        logger.info(f"预测结果保存到: {output_file}")
    
    def generate_simple_report(self, corr_df: pd.DataFrame, models: dict):
        """生成简化分析报告"""
        logger.info("生成分析报告")
        
        # 计算基本统计
        temp_stats = self.df['temperature'].describe()
        gc_corr = corr_df[corr_df['feature'] == 'genomic_gc_content']['correlation'].iloc[0]
        
        # 按特征类别统计
        category_stats = corr_df.groupby('category')['abs_correlation'].agg(['count', 'mean', 'max']).round(4)
        
        report_content = f"""# 细菌基因组温度特征分析报告

## 分析概览
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {len(self.df)}
- 特征数量: {len(self.feature_columns)}
- 温度范围: {temp_stats['min']:.1f}°C - {temp_stats['max']:.1f}°C
- 平均温度: {temp_stats['mean']:.1f}°C

## 关键发现

### 1. GC含量与温度关系
- **相关系数**: {gc_corr:.4f}
- **生物学意义**: {'强正相关，验证了高温菌具有更高GC含量的理论' if gc_corr > 0.5 else '相关性中等，可能受其他因素影响'}

### 2. 最重要的温度预测特征

| 排名 | 特征名称 | 相关系数 | 特征类别 |
|------|----------|----------|----------|
"""
        
        for i, (_, row) in enumerate(corr_df.head(10).iterrows(), 1):
            feature_name = row['feature'].replace('genomic_', '').replace('cds_', '')
            correlation = row['correlation']
            category = row['category']
            
            report_content += f"| {i} | {feature_name} | {correlation:+.4f} | {category} |\n"
        
        report_content += f"""
### 3. 特征类别统计

| 特征类别 | 特征数量 | 平均相关性 | 最大相关性 |
|----------|----------|------------|------------|
"""
        
        for category, stats in category_stats.iterrows():
            report_content += f"| {category} | {stats['count']} | {stats['mean']:.4f} | {stats['max']:.4f} |\n"
        
        # 添加模型结果
        if models:
            report_content += f"""
## 简单预测模型结果

| 特征 | R² | RMSE (°C) |
|------|----|-----------| 
"""
            
            for feature, model in models.items():
                feature_name = feature.replace('genomic_', '').replace('cds_', '')
                report_content += f"| {feature_name} | {model['r2']:.3f} | {model['rmse']:.2f} |\n"
        
        report_content += f"""
## 基因组详情

| 基因组ID | 物种名称 | 温度 | GC含量 | 基因组大小 |
|----------|----------|------|--------|------------|
"""
        
        for idx, row in self.df.iterrows():
            taxid = row['taxid']
            species = row['species_name']
            temp = row['temperature']
            gc = row['genomic_gc_content']
            size = row['genomic_genome_size'] / 1e6
            
            report_content += f"| {taxid} | {species} | {temp:.1f}°C | {gc:.3f} | {size:.2f} Mb |\n"
        
        report_content += f"""
## 生物学解释

### 温度适应机制
1. **GC含量效应**: 高GC含量增强DNA热稳定性
2. **密码子偏好**: 影响蛋白质热稳定性
3. **基因组结构**: 基因密度和排列的影响

### 应用价值
- 快速预测微生物最适生长温度
- 指导耐热酶的筛选和设计
- 分析温度适应的分子机制

## 输出文件
- 特征相关性: feature_correlations.tsv
- 预测结果: simple_predictions.tsv
- 可视化图表: bacteria_feature_analysis.png, top_features_detailed.png

---
*报告由简化细菌基因组特征分析器生成*
"""
        
        # 保存报告
        report_file = self.output_dir / "analysis_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"分析报告已生成: {report_file}")
    
    def run_analysis(self):
        """运行完整分析"""
        logger.info("开始特征分析")
        
        # 1. 相关性分析
        corr_df = self.analyze_correlations()
        
        # 2. 生成可视化
        self.generate_visualizations(corr_df)
        
        # 3. 构建简单模型
        models = self.build_simple_models(corr_df)
        
        # 4. 生成报告
        self.generate_simple_report(corr_df, models)
        
        logger.info("✅ 分析完成!")
        
        return {
            'correlations': corr_df,
            'models': models,
            'output_dir': self.output_dir
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化的细菌基因组温度特征分析器")
    parser.add_argument('--features', required=True, help='特征TSV文件路径')
    parser.add_argument('--output-dir', default='simple_analysis', help='输出目录')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # 检查输入文件
    if not os.path.exists(args.features):
        logger.error(f"特征文件不存在: {args.features}")
        return 1
    
    try:
        # 创建分析器
        analyzer = SimpleBacteriaAnalyzer(
            features_file=args.features,
            output_dir=args.output_dir
        )
        
        # 运行分析
        results = analyzer.run_analysis()
        
        # 输出结果摘要
        logger.info("=" * 60)
        logger.info("🎉 分析完成！主要结果:")
        logger.info(f"   📊 分析了 {len(analyzer.df)} 个细菌基因组")
        logger.info(f"   🔍 评估了 {len(analyzer.feature_columns)} 个特征")
        logger.info(f"   📁 结果保存在: {results['output_dir']}")
        
        # 显示最重要的特征
        top_5_features = results['correlations'].head(5)
        logger.info("\n🏆 前5个最重要的温度预测特征:")
        for i, (_, row) in enumerate(top_5_features.iterrows(), 1):
            feature_name = row['feature'].replace('genomic_', '').replace('cds_', '')
            logger.info(f"   {i}. {feature_name}: r = {row['correlation']:+.4f}")
        
        logger.info("=" * 60)
        
        return 0
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
