"""
可扩展温度预测特征提取模块

这个模块专门为微生物最适温度预测提供可扩展的特征提取框架，
基于DeepMu项目的研究成果，整合了多种生物学特征类型。

主要特点：
1. 模块化设计 - 每种特征类型独立实现，便于扩展
2. 多线程处理 - 支持并行特征提取，提高处理效率
3. 断点恢复 - 支持中断后从断点继续处理
4. 温度特异性 - 专门针对温度预测优化的特征选择
5. 可扩展架构 - 易于添加新的特征类型

特征类型：
- 密码子特征：密码子使用偏好、温度适应性密码子模式
- 基因组特征：GC含量、基因组结构、热稳定性指标
- 蛋白质特征：氨基酸组成、热稳定性、等电点分布
- 代谢特征：代谢途径完整性、温度相关酶系统
- 系统发育特征：分类学信息、进化距离
- RNA特征：tRNA/rRNA结构稳定性、修饰模式

使用示例：
```python
from ex_feature import TemperatureFeatureExtractor

# 初始化特征提取器
extractor = TemperatureFeatureExtractor(
    num_threads=8,
    enable_checkpoint=True,
    checkpoint_dir="checkpoints"
)

# 批量提取特征
extractor.extract_batch(
    metadata_file="genomes.tsv",
    output_dir="temperature_features",
    feature_types=["codon", "genomic", "protein", "pathway"]
)

# 单个基因组特征提取
features = extractor.extract_single(
    genome_id="example_genome",
    genome_file="genome.fna",
    cds_file="cds.fna",
    protein_file="proteins.faa",
    ko_file="annotations.ko"
)
```

版本: 1.0.0
作者: DeepMu扩展开发团队
"""

from .core.extractor import TemperatureFeatureExtractor
from .core.manager import FeatureManager
from .core.checkpoint import CheckpointManager
from .features.codon_temp import CodonTemperatureFeatures
from .features.genomic_temp import GenomicTemperatureFeatures
from .features.protein_temp import ProteinTemperatureFeatures
from .features.pathway_temp import PathwayTemperatureFeatures
from .features.phylo_temp import PhylogeneticTemperatureFeatures
from .features.rna_temp import RNATemperatureFeatures
from .utils.parallel import ParallelProcessor
from .utils.validation import FeatureValidator
from .utils.config import TemperatureConfig

__version__ = "1.0.0"
__author__ = "DeepMu扩展开发团队"

# 导出主要类和函数
__all__ = [
    # 核心组件
    'TemperatureFeatureExtractor',
    'FeatureManager', 
    'CheckpointManager',
    
    # 特征提取器
    'CodonTemperatureFeatures',
    'GenomicTemperatureFeatures', 
    'ProteinTemperatureFeatures',
    'PathwayTemperatureFeatures',
    'PhylogeneticTemperatureFeatures',
    'RNATemperatureFeatures',
    
    # 工具类
    'ParallelProcessor',
    'FeatureValidator',
    'TemperatureConfig',
    
    # 版本信息
    '__version__',
    '__author__'
]

# 默认配置
DEFAULT_CONFIG = {
    'num_threads': 4,
    'enable_checkpoint': True,
    'checkpoint_interval': 100,
    'feature_types': ['codon', 'genomic', 'protein', 'pathway', 'phylo', 'rna'],
    'output_format': 'npz',
    'validation_enabled': True,
    'log_level': 'INFO'
}

def get_default_config():
    """获取默认配置"""
    return DEFAULT_CONFIG.copy()

def create_extractor(**kwargs):
    """创建温度特征提取器的便捷函数
    
    参数:
        **kwargs: 传递给TemperatureFeatureExtractor的参数
        
    返回:
        TemperatureFeatureExtractor: 配置好的特征提取器实例
    """
    config = get_default_config()
    config.update(kwargs)
    return TemperatureFeatureExtractor(**config)
