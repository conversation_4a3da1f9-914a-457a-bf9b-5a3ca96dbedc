#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基因组序列最适生长温度预测特征提取器

基于DeepMu项目和相关论文的方法，提取用于微生物最适生长温度预测的基因组特征。
主要特征包括：
1. 密码子使用偏好 (Codon Usage Bias)
2. 氨基酸组成特征
3. GC含量及其分布
4. tRNA特征 (tRNA thermometer)
5. 蛋白质等电点特征
6. 基因组结构特征
7. 核苷酸偏斜特征

参考文献：
- Building a tRNA thermometer to estimate microbial growth temperature
- DeepMu: Enhanced microbial growth rate and temperature predictor
"""

import os
import argparse
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from collections import Counter, defaultdict
try:
    from Bio import SeqIO
    from Bio.SeqUtils.ProtParam import ProteinAnalysis
    from Bio.Data import CodonTable
    BIOPYTHON_AVAILABLE = True
except ImportError:
    BIOPYTHON_AVAILABLE = False
    logger.warning("BioPython未安装，某些功能将不可用")
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TemperatureFeatureExtractor:
    """基因组序列最适生长温度预测特征提取器"""
    
    def __init__(self, genetic_code=11):
        """初始化特征提取器

        Args:
            genetic_code: 遗传密码表编号 (默认11为细菌)
        """
        self.genetic_code = genetic_code
        if BIOPYTHON_AVAILABLE:
            self.codon_table = CodonTable.unambiguous_dna_by_id[genetic_code]
        else:
            self.codon_table = None
        self.amino_acids = "ACDEFGHIKLMNPQRSTVWY"
        
        # 氨基酸理化性质
        self.aa_properties = {
            'A': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 89.1},
            'C': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 121.0},
            'D': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1, 'molecular_weight': 133.1},
            'E': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1, 'molecular_weight': 147.1},
            'F': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 165.2},
            'G': {'hydrophobic': False, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 75.1},
            'H': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'molecular_weight': 155.2},
            'I': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 131.2},
            'K': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'molecular_weight': 146.2},
            'L': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 131.2},
            'M': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 149.2},
            'N': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 132.1},
            'P': {'hydrophobic': False, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 115.1},
            'Q': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 146.2},
            'R': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'molecular_weight': 174.2},
            'S': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 105.1},
            'T': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 119.1},
            'V': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 117.1},
            'W': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 204.2},
            'Y': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 181.2}
        }
    
    def extract_features_from_genome(self, genome_file: str, cds_file: Optional[str] = None, 
                                   protein_file: Optional[str] = None) -> Dict[str, float]:
        """从基因组文件提取温度预测特征
        
        Args:
            genome_file: 基因组序列文件路径
            cds_file: CDS序列文件路径 (可选)
            protein_file: 蛋白质序列文件路径 (可选)
            
        Returns:
            特征字典
        """
        features = {}
        
        logger.info(f"开始提取基因组特征: {genome_file}")
        
        # 1. 基因组基本特征
        genome_features = self._extract_genome_features(genome_file)
        features.update(genome_features)
        
        # 2. 密码子使用特征 (如果有CDS文件)
        if cds_file and os.path.exists(cds_file):
            codon_features = self._extract_codon_features(cds_file)
            features.update(codon_features)
        
        # 3. 氨基酸组成特征 (如果有蛋白质文件)
        if protein_file and os.path.exists(protein_file):
            aa_features = self._extract_amino_acid_features(protein_file)
            features.update(aa_features)
        
        # 4. 蛋白质等电点特征 (如果有蛋白质文件)
        if protein_file and os.path.exists(protein_file):
            pi_features = self._extract_protein_pi_features(protein_file)
            features.update(pi_features)
        
        logger.info(f"特征提取完成，共提取 {len(features)} 个特征")
        return features
    
    def _extract_genome_features(self, genome_file: str) -> Dict[str, float]:
        """提取基因组基本特征"""
        features = {}
        
        try:
            sequences = []
            total_length = 0
            
            # 读取基因组序列
            if BIOPYTHON_AVAILABLE:
                with open(genome_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        seq_str = str(record.seq).upper()
                        sequences.append(seq_str)
                        total_length += len(seq_str)
            else:
                # 简单的FASTA解析
                with open(genome_file, 'r') as f:
                    current_seq = ""
                    for line in f:
                        line = line.strip()
                        if line.startswith('>'):
                            if current_seq:
                                sequences.append(current_seq.upper())
                                total_length += len(current_seq)
                                current_seq = ""
                        else:
                            current_seq += line
                    if current_seq:
                        sequences.append(current_seq.upper())
                        total_length += len(current_seq)
            
            if not sequences:
                logger.warning(f"未找到序列: {genome_file}")
                return features
            
            # 合并所有序列
            genome_seq = ''.join(sequences)
            
            # 基本统计
            features['genome_size'] = total_length
            features['num_contigs'] = len(sequences)
            
            # GC含量
            gc_count = genome_seq.count('G') + genome_seq.count('C')
            features['gc_content'] = gc_count / total_length if total_length > 0 else 0
            
            # AT含量
            at_count = genome_seq.count('A') + genome_seq.count('T')
            features['at_content'] = at_count / total_length if total_length > 0 else 0
            
            # 核苷酸频率
            for base in 'ATGC':
                features[f'{base.lower()}_freq'] = genome_seq.count(base) / total_length if total_length > 0 else 0
            
            # 二核苷酸频率
            dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TA', 'TT', 'TG', 'TC',
                           'GA', 'GT', 'GG', 'GC', 'CA', 'CT', 'CG', 'CC']
            for dinuc in dinucleotides:
                count = 0
                for i in range(len(genome_seq) - 1):
                    if genome_seq[i:i+2] == dinuc:
                        count += 1
                features[f'dinuc_{dinuc.lower()}_freq'] = count / (total_length - 1) if total_length > 1 else 0
            
            # GC偏斜 (GC skew)
            g_count = genome_seq.count('G')
            c_count = genome_seq.count('C')
            features['gc_skew'] = (g_count - c_count) / (g_count + c_count) if (g_count + c_count) > 0 else 0
            
            # AT偏斜 (AT skew)
            a_count = genome_seq.count('A')
            t_count = genome_seq.count('T')
            features['at_skew'] = (a_count - t_count) / (a_count + t_count) if (a_count + t_count) > 0 else 0
            
            logger.info(f"基因组特征提取完成: {len(features)} 个特征")
            
        except Exception as e:
            logger.error(f"基因组特征提取失败: {e}")
        
        return features
    
    def _extract_codon_features(self, cds_file: str) -> Dict[str, float]:
        """提取密码子使用特征"""
        features = {}

        if not BIOPYTHON_AVAILABLE or not self.codon_table:
            logger.warning("BioPython不可用，跳过密码子特征提取")
            return features

        try:
            # 读取CDS序列
            cds_sequences = []
            if BIOPYTHON_AVAILABLE:
                with open(cds_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        seq_str = str(record.seq).upper()
                        # 确保序列长度是3的倍数
                        if len(seq_str) % 3 == 0:
                            cds_sequences.append(seq_str)
            else:
                # 简单的FASTA解析
                with open(cds_file, 'r') as f:
                    current_seq = ""
                    for line in f:
                        line = line.strip()
                        if line.startswith('>'):
                            if current_seq and len(current_seq) % 3 == 0:
                                cds_sequences.append(current_seq.upper())
                                current_seq = ""
                        else:
                            current_seq += line
                    if current_seq and len(current_seq) % 3 == 0:
                        cds_sequences.append(current_seq.upper())
            
            if not cds_sequences:
                logger.warning(f"未找到有效的CDS序列: {cds_file}")
                return features
            
            # 合并所有CDS序列
            all_cds = ''.join(cds_sequences)
            
            # 统计密码子
            codon_counts = defaultdict(int)
            aa_counts = defaultdict(int)
            
            for i in range(0, len(all_cds), 3):
                codon = all_cds[i:i+3]
                if len(codon) == 3 and all(base in 'ATGC' for base in codon):
                    codon_counts[codon] += 1
                    if codon in self.codon_table.forward_table:
                        aa = self.codon_table.forward_table[codon]
                        aa_counts[aa] += 1
            
            total_codons = sum(codon_counts.values())
            
            # 密码子频率
            for codon in self.codon_table.forward_table:
                features[f'codon_{codon.lower()}_freq'] = codon_counts[codon] / total_codons if total_codons > 0 else 0
            
            # 计算有效密码子数 (ENC)
            enc = self._calculate_enc(codon_counts, aa_counts)
            features['enc'] = enc
            
            # 密码子使用偏好 (CUB)
            cub = self._calculate_cub(codon_counts, aa_counts)
            features['cub'] = cub
            
            # GC含量在密码子位置
            gc1_count = gc2_count = gc3_count = 0
            total_positions = 0
            
            for i in range(0, len(all_cds), 3):
                codon = all_cds[i:i+3]
                if len(codon) == 3:
                    if codon[0] in 'GC': gc1_count += 1
                    if codon[1] in 'GC': gc2_count += 1
                    if codon[2] in 'GC': gc3_count += 1
                    total_positions += 1
            
            features['gc1'] = gc1_count / total_positions if total_positions > 0 else 0
            features['gc2'] = gc2_count / total_positions if total_positions > 0 else 0
            features['gc3'] = gc3_count / total_positions if total_positions > 0 else 0
            
            logger.info(f"密码子特征提取完成: {len([k for k in features.keys() if 'codon' in k or k in ['enc', 'cub', 'gc1', 'gc2', 'gc3']])} 个特征")
            
        except Exception as e:
            logger.error(f"密码子特征提取失败: {e}")
        
        return features

    def _calculate_enc(self, codon_counts: Dict[str, int], aa_counts: Dict[str, int]) -> float:
        """计算有效密码子数 (Effective Number of Codons)"""
        try:
            # 按氨基酸分组密码子
            aa_codon_groups = defaultdict(list)
            for codon, aa in self.codon_table.forward_table.items():
                aa_codon_groups[aa].append(codon)

            # 计算每个氨基酸的Nc值
            nc_values = []
            for aa, codons in aa_codon_groups.items():
                if aa_counts[aa] > 0:
                    # 计算该氨基酸的密码子使用方差
                    frequencies = [codon_counts[codon] / aa_counts[aa] for codon in codons]
                    if len(frequencies) > 1:
                        variance = sum(f * f for f in frequencies)
                        nc = 1 / variance if variance > 0 else len(codons)
                        nc_values.append(nc)

            # 计算平均Nc值
            return np.mean(nc_values) if nc_values else 20.0

        except Exception as e:
            logger.error(f"ENC计算失败: {e}")
            return 20.0

    def _calculate_cub(self, codon_counts: Dict[str, int], aa_counts: Dict[str, int]) -> float:
        """计算密码子使用偏好 (Codon Usage Bias)"""
        try:
            # 按氨基酸分组密码子
            aa_codon_groups = defaultdict(list)
            for codon, aa in self.codon_table.forward_table.items():
                aa_codon_groups[aa].append(codon)

            bias_scores = []
            for aa, codons in aa_codon_groups.items():
                if aa_counts[aa] > 0 and len(codons) > 1:
                    # 计算该氨基酸的密码子频率
                    frequencies = [codon_counts[codon] / aa_counts[aa] for codon in codons]
                    # 计算偏好度 (最大频率 - 平均频率)
                    max_freq = max(frequencies)
                    avg_freq = 1.0 / len(codons)
                    bias = max_freq - avg_freq
                    bias_scores.append(bias)

            return np.mean(bias_scores) if bias_scores else 0.0

        except Exception as e:
            logger.error(f"CUB计算失败: {e}")
            return 0.0

    def _extract_amino_acid_features(self, protein_file: str) -> Dict[str, float]:
        """提取氨基酸组成特征"""
        features = {}

        try:
            # 读取蛋白质序列
            protein_sequences = []
            if BIOPYTHON_AVAILABLE:
                with open(protein_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        seq_str = str(record.seq).upper().replace('*', '')  # 移除终止密码子
                        if seq_str:
                            protein_sequences.append(seq_str)
            else:
                # 简单的FASTA解析
                with open(protein_file, 'r') as f:
                    current_seq = ""
                    for line in f:
                        line = line.strip()
                        if line.startswith('>'):
                            if current_seq:
                                seq_str = current_seq.upper().replace('*', '')
                                if seq_str:
                                    protein_sequences.append(seq_str)
                                current_seq = ""
                        else:
                            current_seq += line
                    if current_seq:
                        seq_str = current_seq.upper().replace('*', '')
                        if seq_str:
                            protein_sequences.append(seq_str)

            if not protein_sequences:
                logger.warning(f"未找到蛋白质序列: {protein_file}")
                return features

            # 合并所有蛋白质序列
            all_proteins = ''.join(protein_sequences)
            total_aa = len(all_proteins)

            # 氨基酸频率
            aa_counts = Counter(all_proteins)
            for aa in self.amino_acids:
                features[f'aa_{aa.lower()}_freq'] = aa_counts[aa] / total_aa if total_aa > 0 else 0

            # 氨基酸理化性质统计
            hydrophobic_count = polar_count = charged_count = 0
            positive_count = negative_count = 0
            total_mw = 0

            for aa in all_proteins:
                if aa in self.aa_properties:
                    props = self.aa_properties[aa]
                    if props['hydrophobic']:
                        hydrophobic_count += 1
                    if props['polar']:
                        polar_count += 1
                    if props['charged']:
                        charged_count += 1
                        if props['charge'] > 0:
                            positive_count += 1
                        elif props['charge'] < 0:
                            negative_count += 1
                    total_mw += props['molecular_weight']

            features['aa_hydrophobic_ratio'] = hydrophobic_count / total_aa if total_aa > 0 else 0
            features['aa_polar_ratio'] = polar_count / total_aa if total_aa > 0 else 0
            features['aa_charged_ratio'] = charged_count / total_aa if total_aa > 0 else 0
            features['aa_positive_ratio'] = positive_count / total_aa if total_aa > 0 else 0
            features['aa_negative_ratio'] = negative_count / total_aa if total_aa > 0 else 0
            features['aa_avg_molecular_weight'] = total_mw / total_aa if total_aa > 0 else 0

            # 氨基酸多样性 (Shannon熵)
            aa_freqs = [aa_counts[aa] / total_aa for aa in self.amino_acids if aa_counts[aa] > 0]
            if aa_freqs:
                shannon_entropy = -sum(f * np.log2(f) for f in aa_freqs if f > 0)
                features['aa_shannon_entropy'] = shannon_entropy
            else:
                features['aa_shannon_entropy'] = 0.0

            logger.info(f"氨基酸特征提取完成: {len([k for k in features.keys() if 'aa_' in k])} 个特征")

        except Exception as e:
            logger.error(f"氨基酸特征提取失败: {e}")

        return features

    def _extract_protein_pi_features(self, protein_file: str) -> Dict[str, float]:
        """提取蛋白质等电点特征"""
        features = {}

        if not BIOPYTHON_AVAILABLE:
            logger.warning("BioPython不可用，跳过蛋白质pI特征提取")
            return features

        try:
            pi_values = []

            # 计算每个蛋白质的等电点
            if BIOPYTHON_AVAILABLE:
                with open(protein_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        seq_str = str(record.seq).upper().replace('*', '')
                        if seq_str and all(aa in self.amino_acids for aa in seq_str):
                            try:
                                analysis = ProteinAnalysis(seq_str)
                                pi = analysis.isoelectric_point()
                                if 3.0 <= pi <= 13.0:  # 合理的pI范围
                                    pi_values.append(pi)
                            except:
                                continue

            if pi_values:
                features['protein_pi_mean'] = np.mean(pi_values)
                features['protein_pi_std'] = np.std(pi_values)
                features['protein_pi_median'] = np.median(pi_values)
                features['protein_pi_min'] = np.min(pi_values)
                features['protein_pi_max'] = np.max(pi_values)

                # 酸性和碱性蛋白质比例
                acidic_count = sum(1 for pi in pi_values if pi < 7.0)
                basic_count = sum(1 for pi in pi_values if pi > 7.0)
                total_proteins = len(pi_values)

                features['protein_acidic_ratio'] = acidic_count / total_proteins
                features['protein_basic_ratio'] = basic_count / total_proteins
                features['protein_neutral_ratio'] = (total_proteins - acidic_count - basic_count) / total_proteins

                logger.info(f"蛋白质pI特征提取完成: 分析了 {len(pi_values)} 个蛋白质")
            else:
                logger.warning("未能计算任何有效的蛋白质等电点")

        except Exception as e:
            logger.error(f"蛋白质pI特征提取失败: {e}")

        return features

class BatchFeatureExtractor:
    """批量特征提取器"""

    def __init__(self, output_dir: str = "genome_features"):
        """初始化批量特征提取器"""
        self.output_dir = output_dir
        self.extractor = TemperatureFeatureExtractor()
        os.makedirs(output_dir, exist_ok=True)

    def extract_from_download_results(self, download_results_file: str,
                                    genome_base_dir: str) -> pd.DataFrame:
        """从下载结果文件批量提取特征

        Args:
            download_results_file: 下载结果TSV文件路径
            genome_base_dir: 基因组文件基础目录

        Returns:
            包含特征的DataFrame
        """
        logger.info(f"开始批量特征提取")
        logger.info(f"下载结果文件: {download_results_file}")
        logger.info(f"基因组目录: {genome_base_dir}")

        # 读取下载结果
        df = pd.read_csv(download_results_file, sep='\t', encoding='utf-8')
        success_df = df[df['download_status'] == 'Success'].copy()

        logger.info(f"找到 {len(success_df)} 个成功下载的基因组")

        # 提取特征
        all_features = []

        for idx, row in success_df.iterrows():
            try:
                taxid = row['species_taxonid']
                download_path = row['download_path']

                logger.info(f"处理 taxid {taxid} ({idx+1}/{len(success_df)})")

                # 构建文件路径
                genome_dir = os.path.join(genome_base_dir, download_path)
                fasta_file = os.path.join(genome_dir, f"{taxid}_genomic.fna.gz")

                # 检查文件是否存在
                if not os.path.exists(fasta_file):
                    logger.warning(f"基因组文件不存在: {fasta_file}")
                    continue

                # 解压文件 (如果需要)
                temp_fasta = self._prepare_fasta_file(fasta_file)

                if temp_fasta:
                    # 提取特征
                    features = self.extractor.extract_features_from_genome(temp_fasta)

                    # 添加基本信息
                    features['taxid'] = taxid
                    features['species_name'] = row.get('species_name', '')
                    features['organism_type'] = row.get('organism_type.1', '')
                    features['optimum_temperature'] = row.get('optimum_temperature_for_growth', 0)

                    all_features.append(features)

                    # 清理临时文件
                    if temp_fasta != fasta_file:
                        os.remove(temp_fasta)

            except Exception as e:
                logger.error(f"处理 taxid {taxid} 失败: {e}")
                continue

        # 转换为DataFrame
        if all_features:
            features_df = pd.DataFrame(all_features)

            # 保存结果
            output_file = os.path.join(self.output_dir, 'genome_temperature_features.tsv')
            features_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')

            logger.info(f"特征提取完成: {len(features_df)} 个基因组")
            logger.info(f"特征数量: {len(features_df.columns) - 4}")  # 减去基本信息列
            logger.info(f"结果保存至: {output_file}")

            return features_df
        else:
            logger.error("未能提取任何特征")
            return pd.DataFrame()

    def _prepare_fasta_file(self, fasta_file: str) -> Optional[str]:
        """准备FASTA文件 (解压缩如果需要)"""
        try:
            if fasta_file.endswith('.gz'):
                import gzip
                import tempfile

                # 创建临时文件
                temp_fd, temp_path = tempfile.mkstemp(suffix='.fasta')

                with gzip.open(fasta_file, 'rt') as gz_file:
                    with os.fdopen(temp_fd, 'w') as temp_file:
                        temp_file.write(gz_file.read())

                return temp_path
            else:
                return fasta_file

        except Exception as e:
            logger.error(f"准备FASTA文件失败: {e}")
            return None

    def generate_feature_report(self, features_df: pd.DataFrame):
        """生成特征分析报告"""
        if features_df.empty:
            logger.warning("特征DataFrame为空，无法生成报告")
            return

        logger.info("生成特征分析报告")

        # 基本统计
        num_genomes = len(features_df)
        num_features = len(features_df.columns) - 4  # 减去基本信息列

        # 按生物类型统计
        organism_counts = features_df['organism_type'].value_counts()

        # 温度范围统计
        temp_stats = features_df['optimum_temperature'].describe()

        report_content = f"""# 基因组温度预测特征提取报告

## 提取概览
- 提取时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {num_genomes:,}
- 特征数量: {num_features}

## 按生物类型分布
"""

        for org_type, count in organism_counts.items():
            report_content += f"- **{org_type}**: {count} 个基因组\n"

        report_content += f"""
## 温度分布统计
- 平均温度: {temp_stats['mean']:.1f}°C
- 温度范围: {temp_stats['min']:.1f}°C - {temp_stats['max']:.1f}°C
- 标准差: {temp_stats['std']:.1f}°C

## 特征类别
1. **基因组基本特征**: GC含量、基因组大小、核苷酸频率等
2. **密码子使用特征**: 密码子频率、ENC、CUB等
3. **氨基酸组成特征**: 氨基酸频率、理化性质统计等
4. **蛋白质特征**: 等电点分布、酸碱性比例等

## 特征统计
"""

        # 选择一些关键特征进行统计
        key_features = ['gc_content', 'genome_size', 'enc', 'cub', 'aa_hydrophobic_ratio', 'protein_pi_mean']

        for feature in key_features:
            if feature in features_df.columns:
                stats = features_df[feature].describe()
                report_content += f"\n### {feature}\n"
                report_content += f"- 平均值: {stats['mean']:.4f}\n"
                report_content += f"- 标准差: {stats['std']:.4f}\n"
                report_content += f"- 范围: {stats['min']:.4f} - {stats['max']:.4f}\n"

        report_content += f"""
## 使用建议
1. 可以使用这些特征训练机器学习模型预测最适生长温度
2. 建议使用特征选择方法筛选最重要的特征
3. 可以考虑特征标准化或归一化
4. 建议使用交叉验证评估模型性能

## 输出文件
- 特征数据: genome_temperature_features.tsv
- 分析报告: feature_extraction_report.md
"""

        # 保存报告
        report_file = os.path.join(self.output_dir, 'feature_extraction_report.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"特征分析报告已保存: {report_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基因组序列最适生长温度预测特征提取器')
    parser.add_argument('--download-results', required=True,
                       help='下载结果TSV文件路径')
    parser.add_argument('--genome-dir', required=True,
                       help='基因组文件基础目录')
    parser.add_argument('--output-dir', default='genome_features',
                       help='输出目录 (默认: genome_features)')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.download_results):
        logger.error(f"下载结果文件不存在: {args.download_results}")
        return

    if not os.path.exists(args.genome_dir):
        logger.error(f"基因组目录不存在: {args.genome_dir}")
        return

    # 创建批量提取器
    batch_extractor = BatchFeatureExtractor(args.output_dir)

    # 提取特征
    features_df = batch_extractor.extract_from_download_results(
        args.download_results,
        args.genome_dir
    )

    # 生成报告
    if not features_df.empty:
        batch_extractor.generate_feature_report(features_df)
        logger.info("✅ 特征提取任务完成！")
    else:
        logger.error("❌ 特征提取任务失败！")

if __name__ == "__main__":
    main()
