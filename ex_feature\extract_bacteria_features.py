#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
细菌基因组温度特征提取器

专门用于提取temp_data/high_quality/resume_test/Bacteria目录下的基因组数据特征，
结合NCBI下载的GFF注释文件，提取全面的基因组特征用于温度预测。

主要功能：
1. 自动扫描Bacteria目录下的所有基因组
2. 解析NCBI GFF注释文件提取基因信息
3. 提取基因组、基因、密码子等多层次特征
4. 结合温度信息进行特征分析
5. 输出TSV格式的特征文件
"""

import os
import sys
import gzip
import json
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from collections import Counter, defaultdict
import tempfile
import argparse
from datetime import datetime

# 添加ex_feature路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入简化的特征提取器
try:
    from simple_genomic_features import SimpleGenomicFeatures, SimpleCodonFeatures
except ImportError:
    print("错误: 无法导入simple_genomic_features模块")
    sys.exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BacteriaGenomeFeatureExtractor:
    """细菌基因组特征提取器"""
    
    def __init__(self, bacteria_dir: str, download_results_file: str, output_dir: str = "bacteria_features"):
        """
        初始化提取器
        
        参数:
            bacteria_dir: Bacteria目录路径
            download_results_file: 下载结果TSV文件路径
            output_dir: 输出目录
        """
        self.bacteria_dir = Path(bacteria_dir)
        self.download_results_file = download_results_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化特征提取器
        self.genomic_extractor = SimpleGenomicFeatures()
        self.codon_extractor = SimpleCodonFeatures()
        
        # 读取下载结果
        self.download_results = self._load_download_results()
        
        logger.info(f"初始化完成，找到 {len(self.download_results)} 个基因组记录")
    
    def _load_download_results(self) -> pd.DataFrame:
        """加载下载结果文件"""
        try:
            df = pd.read_csv(self.download_results_file, sep='\t')
            # 只保留成功下载的记录
            success_df = df[df['download_status'] == 'Success'].copy()
            logger.info(f"加载下载结果: {len(df)} 总记录, {len(success_df)} 成功下载")
            return success_df
        except Exception as e:
            logger.error(f"加载下载结果失败: {e}")
            return pd.DataFrame()
    
    def extract_all_features(self) -> pd.DataFrame:
        """提取所有基因组的特征"""
        logger.info("开始批量特征提取")
        
        all_features = []
        
        for idx, row in self.download_results.iterrows():
            try:
                taxid = row['species_taxonid']
                species_name = row['species_name']
                temperature = row['optimum_temperature_for_growth']
                download_path = row['download_path']
                
                logger.info(f"处理基因组 {taxid} ({species_name}) - 温度: {temperature}°C")
                
                # 构建文件路径
                # download_path已经包含了"Bacteria\"，所以需要去掉重复的部分
                if download_path.startswith('Bacteria\\') or download_path.startswith('Bacteria/'):
                    relative_path = download_path[9:]  # 去掉"Bacteria\"或"Bacteria/"
                else:
                    relative_path = download_path

                genome_dir = self.bacteria_dir / relative_path
                fasta_file = genome_dir / f"{taxid}_genomic.fna.gz"
                gff_file = genome_dir / f"{taxid}_genomic.gff.gz"
                
                # 检查文件是否存在
                if not fasta_file.exists():
                    logger.warning(f"基因组文件不存在: {fasta_file}")
                    continue
                
                if not gff_file.exists():
                    logger.warning(f"GFF文件不存在: {gff_file}")
                    continue
                
                # 提取特征
                features = self._extract_single_genome_features(
                    taxid=taxid,
                    species_name=species_name,
                    temperature=temperature,
                    fasta_file=str(fasta_file),
                    gff_file=str(gff_file),
                    row=row
                )
                
                if features:
                    all_features.append(features)
                    logger.info(f"成功提取 {len(features)} 个特征")
                
            except Exception as e:
                logger.error(f"处理基因组 {taxid} 失败: {e}")
                continue
        
        if all_features:
            # 转换为DataFrame
            features_df = pd.DataFrame(all_features)
            logger.info(f"特征提取完成: {len(features_df)} 个基因组, {len(features_df.columns)} 个特征")
            return features_df
        else:
            logger.error("未能提取任何特征")
            return pd.DataFrame()
    
    def _extract_single_genome_features(self, taxid: int, species_name: str, temperature: float,
                                      fasta_file: str, gff_file: str, row: pd.Series) -> Optional[Dict]:
        """提取单个基因组的特征"""
        try:
            features = {}
            
            # 基本信息
            features['taxid'] = taxid
            features['species_name'] = species_name
            features['temperature'] = temperature
            features['organism_type'] = row.get('organism_type.1', 'Bacteria')
            features['assembly_level'] = row.get('assembly_level', 'Unknown')
            features['quality_score'] = row.get('quality_score', 0)
            
            # 1. 提取基因组特征
            logger.debug(f"提取基因组特征: {fasta_file}")
            
            # 解压基因组文件到临时文件
            temp_fasta = self._decompress_file(fasta_file)
            if temp_fasta:
                genomic_features = self.genomic_extractor.extract_features(temp_fasta)
                # 添加前缀避免命名冲突
                for key, value in genomic_features.items():
                    features[f'genomic_{key}'] = value
                
                # 清理临时文件
                os.remove(temp_fasta)
            
            # 2. 解析GFF文件提取基因信息
            logger.debug(f"解析GFF文件: {gff_file}")
            gff_features = self._extract_gff_features(gff_file)
            features.update(gff_features)
            
            # 3. 提取CDS序列并分析密码子
            logger.debug("提取CDS序列")
            cds_features = self._extract_cds_features(fasta_file, gff_file)
            features.update(cds_features)
            
            return features
            
        except Exception as e:
            logger.error(f"提取基因组 {taxid} 特征失败: {e}")
            return None
    
    def _decompress_file(self, gz_file: str) -> Optional[str]:
        """解压缩文件到临时文件"""
        try:
            # 创建临时文件
            temp_fd, temp_path = tempfile.mkstemp(suffix='.fasta')
            
            with gzip.open(gz_file, 'rt') as gz_f:
                with os.fdopen(temp_fd, 'w') as temp_f:
                    temp_f.write(gz_f.read())
            
            return temp_path
            
        except Exception as e:
            logger.error(f"解压文件失败 {gz_file}: {e}")
            return None
    
    def _extract_gff_features(self, gff_file: str) -> Dict[str, float]:
        """从GFF文件提取基因注释特征"""
        features = {}
        
        try:
            gene_count = 0
            cds_count = 0
            trna_count = 0
            rrna_count = 0
            pseudogene_count = 0
            
            gene_lengths = []
            cds_lengths = []
            
            with gzip.open(gff_file, 'rt') as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    
                    parts = line.strip().split('\t')
                    if len(parts) < 9:
                        continue
                    
                    feature_type = parts[2]
                    start = int(parts[3])
                    end = int(parts[4])
                    length = end - start + 1
                    
                    # 统计不同类型的特征
                    if feature_type == 'gene':
                        gene_count += 1
                        gene_lengths.append(length)
                    elif feature_type == 'CDS':
                        cds_count += 1
                        cds_lengths.append(length)
                    elif feature_type == 'tRNA':
                        trna_count += 1
                    elif feature_type == 'rRNA':
                        rrna_count += 1
                    elif 'pseudogene' in feature_type.lower():
                        pseudogene_count += 1
            
            # 基因统计特征
            features['gene_count'] = gene_count
            features['cds_count'] = cds_count
            features['trna_count'] = trna_count
            features['rrna_count'] = rrna_count
            features['pseudogene_count'] = pseudogene_count
            
            # 基因长度统计
            if gene_lengths:
                features['avg_gene_length'] = np.mean(gene_lengths)
                features['median_gene_length'] = np.median(gene_lengths)
                features['std_gene_length'] = np.std(gene_lengths)
                features['min_gene_length'] = np.min(gene_lengths)
                features['max_gene_length'] = np.max(gene_lengths)
            else:
                features['avg_gene_length'] = 0
                features['median_gene_length'] = 0
                features['std_gene_length'] = 0
                features['min_gene_length'] = 0
                features['max_gene_length'] = 0
            
            # CDS长度统计
            if cds_lengths:
                features['avg_cds_length'] = np.mean(cds_lengths)
                features['median_cds_length'] = np.median(cds_lengths)
                features['std_cds_length'] = np.std(cds_lengths)
            else:
                features['avg_cds_length'] = 0
                features['median_cds_length'] = 0
                features['std_cds_length'] = 0
            
            # 基因密度
            if 'genomic_genome_size' in features:
                genome_size = features['genomic_genome_size']
                features['gene_density'] = gene_count / (genome_size / 1000) if genome_size > 0 else 0
                features['coding_density'] = sum(cds_lengths) / genome_size if genome_size > 0 else 0
            
            logger.debug(f"GFF特征: {gene_count} 基因, {cds_count} CDS, {trna_count} tRNA, {rrna_count} rRNA")
            
        except Exception as e:
            logger.error(f"解析GFF文件失败: {e}")
        
        return features
    
    def _extract_cds_features(self, fasta_file: str, gff_file: str) -> Dict[str, float]:
        """提取CDS序列并分析密码子特征"""
        features = {}
        
        try:
            # 从GFF文件提取CDS坐标
            cds_regions = self._parse_cds_from_gff(gff_file)
            
            if not cds_regions:
                logger.warning("未找到CDS区域")
                return features
            
            # 从基因组序列提取CDS序列
            cds_sequences = self._extract_cds_sequences(fasta_file, cds_regions)
            
            if cds_sequences:
                # 创建临时CDS文件
                temp_cds_file = self._create_temp_cds_file(cds_sequences)
                
                if temp_cds_file:
                    # 使用密码子特征提取器
                    codon_features = self.codon_extractor.extract_features(temp_cds_file)
                    
                    # 添加前缀
                    for key, value in codon_features.items():
                        features[f'cds_{key}'] = value
                    
                    # 清理临时文件
                    os.remove(temp_cds_file)
                    
                    logger.debug(f"提取了 {len(codon_features)} 个密码子特征")
            
        except Exception as e:
            logger.error(f"提取CDS特征失败: {e}")
        
        return features

    def _parse_cds_from_gff(self, gff_file: str) -> List[Tuple[str, int, int, str]]:
        """从GFF文件解析CDS区域"""
        cds_regions = []

        try:
            with gzip.open(gff_file, 'rt') as f:
                for line in f:
                    if line.startswith('#'):
                        continue

                    parts = line.strip().split('\t')
                    if len(parts) < 9:
                        continue

                    if parts[2] == 'CDS':
                        contig = parts[0]
                        start = int(parts[3])
                        end = int(parts[4])
                        strand = parts[6]

                        cds_regions.append((contig, start, end, strand))

            logger.debug(f"找到 {len(cds_regions)} 个CDS区域")

        except Exception as e:
            logger.error(f"解析CDS区域失败: {e}")

        return cds_regions

    def _extract_cds_sequences(self, fasta_file: str, cds_regions: List[Tuple[str, int, int, str]]) -> List[str]:
        """从基因组序列提取CDS序列"""
        cds_sequences = []

        try:
            # 读取基因组序列
            genome_sequences = {}

            with gzip.open(fasta_file, 'rt') as f:
                current_contig = None
                current_seq = ""

                for line in f:
                    line = line.strip()
                    if line.startswith('>'):
                        if current_contig and current_seq:
                            genome_sequences[current_contig] = current_seq

                        # 提取contig名称
                        current_contig = line[1:].split()[0]
                        current_seq = ""
                    else:
                        current_seq += line.upper()

                # 添加最后一个序列
                if current_contig and current_seq:
                    genome_sequences[current_contig] = current_seq

            # 提取CDS序列
            for contig, start, end, strand in cds_regions:
                if contig in genome_sequences:
                    sequence = genome_sequences[contig]

                    # 提取序列（GFF坐标是1-based）
                    cds_seq = sequence[start-1:end]

                    # 如果是负链，需要反向互补
                    if strand == '-':
                        cds_seq = self._reverse_complement(cds_seq)

                    # 只保留长度为3的倍数的序列
                    if len(cds_seq) % 3 == 0 and len(cds_seq) >= 60:  # 至少20个密码子
                        cds_sequences.append(cds_seq)

            logger.debug(f"提取了 {len(cds_sequences)} 个有效CDS序列")

        except Exception as e:
            logger.error(f"提取CDS序列失败: {e}")

        return cds_sequences

    def _reverse_complement(self, sequence: str) -> str:
        """计算反向互补序列"""
        complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G', 'N': 'N'}
        return ''.join(complement.get(base, base) for base in reversed(sequence))

    def _create_temp_cds_file(self, cds_sequences: List[str]) -> Optional[str]:
        """创建临时CDS文件"""
        try:
            temp_fd, temp_path = tempfile.mkstemp(suffix='.fasta')

            with os.fdopen(temp_fd, 'w') as f:
                for i, seq in enumerate(cds_sequences):
                    f.write(f">CDS_{i+1}\n{seq}\n")

            return temp_path

        except Exception as e:
            logger.error(f"创建临时CDS文件失败: {e}")
            return None

    def save_features(self, features_df: pd.DataFrame, filename: str = "bacteria_genome_features.tsv"):
        """保存特征到TSV文件"""
        try:
            output_file = self.output_dir / filename
            features_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')

            logger.info(f"特征已保存到: {output_file}")
            logger.info(f"数据形状: {features_df.shape}")

            # 生成特征统计报告
            self._generate_feature_report(features_df, output_file)

            return str(output_file)

        except Exception as e:
            logger.error(f"保存特征失败: {e}")
            return None

    def _generate_feature_report(self, features_df: pd.DataFrame, output_file: Path):
        """生成特征统计报告"""
        try:
            report_file = output_file.with_suffix('.md')

            # 计算基本统计
            num_genomes = len(features_df)
            num_features = len(features_df.columns) - 6  # 减去基本信息列

            # 温度统计
            temp_stats = features_df['temperature'].describe()

            # 按生物类型统计
            organism_counts = features_df['organism_type'].value_counts()

            # 特征类别统计
            feature_categories = {
                'genomic': len([col for col in features_df.columns if col.startswith('genomic_')]),
                'gene': len([col for col in features_df.columns if col.startswith('gene_') or col.startswith('cds_count') or col.startswith('trna_') or col.startswith('rrna_')]),
                'cds': len([col for col in features_df.columns if col.startswith('cds_')])
            }

            report_content = f"""# 细菌基因组温度特征提取报告

## 提取概览
- 提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {num_genomes:,}
- 特征数量: {num_features}
- 数据来源: temp_data/high_quality/resume_test/Bacteria

## 数据统计

### 温度分布
- 平均温度: {temp_stats['mean']:.1f}°C
- 温度范围: {temp_stats['min']:.1f}°C - {temp_stats['max']:.1f}°C
- 标准差: {temp_stats['std']:.1f}°C
- 中位数: {temp_stats['50%']:.1f}°C

### 生物类型分布
"""

            for org_type, count in organism_counts.items():
                report_content += f"- **{org_type}**: {count} 个基因组\n"

            report_content += f"""
### 特征类别分布
- **基因组特征**: {feature_categories['genomic']} 个 (GC含量、序列复杂度等)
- **基因注释特征**: {feature_categories['gene']} 个 (基因数量、长度统计等)
- **密码子特征**: {feature_categories['cds']} 个 (密码子使用、偏好性等)

## 特征详情

### 1. 基因组基本特征
从FASTA文件提取的基因组序列特征：
- 基因组大小、GC含量、核苷酸组成
- 二核苷酸频率、序列复杂度
- k-mer多样性、重复序列含量

### 2. 基因注释特征
从GFF文件提取的基因注释信息：
- 基因数量统计 (gene, CDS, tRNA, rRNA)
- 基因长度分布 (平均值、中位数、标准差)
- 基因密度、编码密度

### 3. 密码子使用特征
从CDS序列提取的密码子特征：
- 密码子使用频率
- 密码子位置GC含量 (GC1, GC2, GC3)
- 密码子偏好性分析

## 数据质量

### 成功提取的基因组
- 总计: {num_genomes} 个基因组
- 平均特征数: {num_features} 个/基因组
- 数据完整性: 高

### 文件来源
- 基因组序列: *_genomic.fna.gz
- 基因注释: *_genomic.gff.gz
- 温度数据: download_results.tsv

## 使用建议

### 温度预测模型
1. **特征选择**: 建议使用相关性分析筛选重要特征
2. **数据预处理**: 对特征进行标准化处理
3. **模型选择**: 可尝试线性回归、随机森林等算法
4. **验证方法**: 使用交叉验证评估模型性能

### 生物学分析
1. **GC含量分析**: 验证GC含量与温度的关系
2. **基因密度**: 分析基因组紧密度与温度适应性
3. **密码子偏好**: 研究密码子使用与温度的关联

## 输出文件
- **特征数据**: {output_file.name}
- **分析报告**: {report_file.name}

---
*报告由细菌基因组特征提取器自动生成*
"""

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)

            logger.info(f"特征报告已生成: {report_file}")

        except Exception as e:
            logger.error(f"生成特征报告失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="细菌基因组温度特征提取器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
    # 基本用法
    python extract_bacteria_features.py

    # 指定输出目录
    python extract_bacteria_features.py --output-dir my_features

    # 指定数据路径
    python extract_bacteria_features.py --bacteria-dir /path/to/Bacteria --download-results /path/to/results.tsv
        """
    )

    parser.add_argument('--bacteria-dir',
                       default='temp_data/high_quality/resume_test/Bacteria',
                       help='Bacteria目录路径')
    parser.add_argument('--download-results',
                       default='temp_data/high_quality/resume_test/download_results.tsv',
                       help='下载结果TSV文件路径')
    parser.add_argument('--output-dir',
                       default='bacteria_features',
                       help='输出目录')
    parser.add_argument('--output-file',
                       default='bacteria_genome_features.tsv',
                       help='输出文件名')
    parser.add_argument('--log-level',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO',
                       help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # 检查输入路径
    if not os.path.exists(args.bacteria_dir):
        logger.error(f"Bacteria目录不存在: {args.bacteria_dir}")
        return 1

    if not os.path.exists(args.download_results):
        logger.error(f"下载结果文件不存在: {args.download_results}")
        return 1

    try:
        # 创建特征提取器
        extractor = BacteriaGenomeFeatureExtractor(
            bacteria_dir=args.bacteria_dir,
            download_results_file=args.download_results,
            output_dir=args.output_dir
        )

        # 提取特征
        logger.info("开始特征提取...")
        features_df = extractor.extract_all_features()

        if features_df.empty:
            logger.error("未能提取任何特征")
            return 1

        # 保存结果
        output_file = extractor.save_features(features_df, args.output_file)

        if output_file:
            logger.info("✅ 特征提取完成!")
            logger.info(f"   基因组数量: {len(features_df)}")
            logger.info(f"   特征数量: {len(features_df.columns) - 6}")
            logger.info(f"   输出文件: {output_file}")
            return 0
        else:
            logger.error("保存特征失败")
            return 1

    except Exception as e:
        logger.error(f"特征提取失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
