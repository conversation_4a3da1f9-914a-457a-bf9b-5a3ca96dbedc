# Ex_Feature: 可扩展温度预测特征提取框架

基于DeepMu项目研究成果开发的专业温度预测特征提取工具，支持多线程处理和断点恢复功能。

## 🌟 主要特点

- **🧬 专业特征提取**: 基于DeepMu论文的温度适应性特征
- **⚡ 多线程处理**: 支持并行特征提取，大幅提升处理速度
- **🔄 断点恢复**: 支持中断后从断点继续处理
- **🔧 模块化设计**: 易于扩展新的特征类型
- **✅ 质量控制**: 内置特征验证和质量评估
- **📊 详细报告**: 生成详细的提取统计报告

## 📦 安装要求

```bash
# 基础依赖
pip install numpy pandas scipy scikit-learn
pip install biopython pyyaml
pip install torch  # 可选，用于高级特征

# 开发依赖
pip install pytest black flake8  # 可选
```

## 🚀 快速开始

### 1. 单个基因组特征提取

```python
from ex_feature import TemperatureFeatureExtractor

# 创建特征提取器
extractor = TemperatureFeatureExtractor(
    num_threads=4,
    enable_checkpoint=True,
    feature_types=['codon', 'genomic', 'protein']
)

# 提取特征
features = extractor.extract_single(
    genome_id="example_genome",
    genome_file="genome.fna",
    cds_file="cds.fna",
    protein_file="proteins.faa",
    ko_file="annotations.ko"
)

print(f"提取了 {len(features)} 个特征")
```

### 2. 批量处理

```python
# 批量提取特征
extractor.extract_batch(
    metadata_file="genomes.tsv",
    output_dir="temperature_features",
    genome_dir="genomes/",
    cds_dir="cds/",
    protein_dir="proteins/",
    ko_dir="annotations/",
    resume=True  # 支持断点恢复
)
```

### 3. 命令行使用

```bash
# 单个基因组
python examples/extract_temperature_features.py single \
    --genome genome.fna \
    --cds cds.fna \
    --protein proteins.faa \
    --ko annotations.ko \
    --output features.npz

# 批量处理（8线程）
python examples/extract_temperature_features.py batch \
    --metadata genomes.tsv \
    --output-dir features/ \
    --genome-dir genomes/ \
    --cds-dir cds/ \
    --threads 8 \
    --resume
```

## 🧬 特征类型

### 1. 密码子特征 (Codon Features)
- 温度适应性密码子使用偏好
- 热稳定性相关密码子模式
- 密码子-反密码子相互作用强度
- 同义密码子使用偏好 (RSCU)
- 高表达基因vs背景基因差异

### 2. 基因组特征 (Genomic Features)
- GC含量及其分布模式
- 基因组大小和结构特征
- 序列复杂度和重复序列
- 热稳定性相关序列特征
- 核苷酸组成和偏斜分析

### 3. 蛋白质特征 (Protein Features)
- 氨基酸组成和理化性质
- 蛋白质稳定性指标
- 疏水性和电荷分布
- 分子量和等电点
- 温度敏感性分析

### 4. 代谢途径特征 (Pathway Features)
- KEGG途径完整性
- 温度相关代谢模块
- 酶系统分析
- 代谢网络连通性

### 5. 系统发育特征 (Phylogenetic Features)
- 分类学层级信息
- 进化距离特征
- 系统发育保守性

### 6. RNA特征 (RNA Features)
- tRNA/rRNA结构稳定性
- RNA修饰模式
- 二级结构预测

## ⚙️ 配置文件

使用YAML配置文件自定义提取参数：

```yaml
# config/temperature_config.yaml
genetic_code: 11
num_threads: 8
feature_types:
  - codon
  - genomic
  - protein
  - pathway

codon_features:
  use_heg_features: true
  calculate_rscu: true

genomic_features:
  gc_window_size: 1000
  include_repeats: true

# ... 更多配置选项
```

## 🔄 断点恢复

支持大规模数据处理的断点恢复功能：

```python
# 启用断点恢复
extractor = TemperatureFeatureExtractor(
    enable_checkpoint=True,
    checkpoint_dir="checkpoints",
    checkpoint_interval=100  # 每100个基因组保存一次
)

# 从断点恢复
extractor.extract_batch(
    metadata_file="genomes.tsv",
    output_dir="features/",
    resume=True
)
```

## 📊 质量控制

内置特征验证和质量评估：

```python
from ex_feature.utils.validation import FeatureValidator

validator = FeatureValidator()
validation_result = validator.validate_features(features)

print(f"质量评分: {validation_result['quality_score']:.3f}")
print(f"验证通过: {validation_result['valid']}")
```

## 🔧 扩展新特征

添加自定义特征提取器：

```python
from ex_feature.features.base import BaseTemperatureFeatures

class CustomTemperatureFeatures(BaseTemperatureFeatures):
    def extract_features(self, input_data):
        # 实现自定义特征提取逻辑
        features = {}
        # ... 特征计算
        return features

# 注册到提取器
extractor.register_extractor('custom', CustomTemperatureFeatures())
```

## 📁 项目结构

```
ex_feature/
├── __init__.py              # 主模块入口
├── core/                    # 核心组件
│   ├── extractor.py        # 主要特征提取器
│   ├── manager.py          # 特征管理器
│   └── checkpoint.py       # 断点管理器
├── features/               # 特征提取器
│   ├── codon_temp.py       # 密码子特征
│   ├── genomic_temp.py     # 基因组特征
│   ├── protein_temp.py     # 蛋白质特征
│   ├── pathway_temp.py     # 代谢途径特征
│   ├── phylo_temp.py       # 系统发育特征
│   └── rna_temp.py         # RNA特征
├── utils/                  # 工具模块
│   ├── config.py           # 配置管理
│   ├── parallel.py         # 并行处理
│   └── validation.py       # 特征验证
├── config/                 # 配置文件
│   └── temperature_config.yaml
├── examples/               # 示例脚本
│   └── extract_temperature_features.py
└── README.md
```

## 📈 性能优化

- **多线程处理**: 支持多线程并行特征提取
- **内存优化**: 流式处理大文件，控制内存使用
- **缓存机制**: 智能缓存中间结果，避免重复计算
- **批处理**: 优化的批处理算法，提高吞吐量

## 🧪 测试

```bash
# 运行测试
python -m pytest tests/

# 运行示例
python examples/extract_temperature_features.py --help
```

## 📚 文档

详细文档请参考：
- [API文档](docs/api.md)
- [特征说明](docs/features.md)
- [配置指南](docs/configuration.md)
- [扩展指南](docs/extending.md)

## 🤝 贡献

欢迎贡献代码和建议！请参考 [CONTRIBUTING.md](CONTRIBUTING.md)

## 📄 许可证

本项目基于MIT许可证开源。

## 🙏 致谢

本项目基于以下研究成果：
- DeepMu: 微生物生长预测深度学习框架
- 相关温度适应性研究论文
- 开源生物信息学工具和数据库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue: [GitHub Issues](https://github.com/your-repo/ex_feature/issues)
- 邮箱: <EMAIL>
