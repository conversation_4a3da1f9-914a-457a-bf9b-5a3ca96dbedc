"""DeepMu 的 rRNA 注释模块。

本模块提供使用 Barrnap 工具在基因组序列中识别 rRNA 基因的功能，
支持不同生物界和遗传密码的处理。
"""

import subprocess
import os
import logging
from pathlib import Path
from typing import List, Dict, Optional

class BarrnapWrapper:
    """Barrnap rRNA 预测工具的封装类，带有结构化输入/输出处理。"""

    def __init__(self, barrnap_path: str = "barrnap"):
        """
        用于初始化封装类，指定 barrnap 可执行文件路径。

        参数:
            barrnap_path: barrnap 可执行文件的路径
        """
        self.executable = barrnap_path
        self.valid_kingdoms = ['bac', 'arc', 'euk', 'mito']

    def predict_rrna(
        self,
        input_fasta: str,
        output_gff: Optional[str] = None,
        output_fasta: Optional[str] = None,
        kingdom: str = 'bac',
        threads: int = 1,
        evalue: float = 1e-6,
        lencutoff: float = 0.8,
        reject: float = 0.5,
        quiet: bool = True,
        incseq: bool = False,
        tmp_dir: str = ".",
        overwrite: bool = True
    ) -> Dict:
        """
        使用指定参数运行 Barrnap 进行 rRNA 预测。

        参数:
            input_fasta: 输入 FASTA 文件路径
            output_gff: 保存 GFF 输出的路径（默认输出到标准输出）
            output_fasta: 保存 rRNA 序列的 FASTA 文件路径
            kingdom: 目标生物界（bac/arc/euk/mito）
            threads: 使用的 CPU 线程数
            evalue: HMMER 搜索的 E-value 阈值
            lencutoff: 部分命中片段的长度阈值（0-1）
            reject: 拒绝低于该比例的命中（0-1）
            quiet: 是否抑制进度信息
            incseq: 是否在 GFF 输出中包含完整序列
            tmp_dir: 临时文件目录
            overwrite: 是否覆盖已存在的输出文件（默认 True）

        返回:
            字典，包含：
            - 'success': 是否成功
            - 'features': 解析得到的 rRNA 特征列表
            - 'output_files': 生成的输出文件列表
        """
        # 校验输入
        if kingdom not in self.valid_kingdoms:
            raise ValueError(f"无效的 kingdom: {kingdom}。可选项为 {self.valid_kingdoms}")

        if not os.path.exists(input_fasta):
            raise FileNotFoundError(f"未找到输入 FASTA 文件: {input_fasta}")

        # 构建命令
        cmd = [self.executable]

        # 添加参数
        if quiet:
            cmd.append("--quiet")

        if output_fasta:
            cmd.extend(["--outseq", output_fasta])

        cmd.extend([
            "--kingdom", kingdom,
            "--threads", str(threads),
            "--evalue", str(evalue),
            "--lencutoff", str(lencutoff),
            "--reject", str(reject)
        ])

        if incseq:
            cmd.append("--incseq")

        # 确保输出目录存在
        tmp_dir = os.path.abspath(tmp_dir)
        os.makedirs(tmp_dir, exist_ok=True)

        # 检查输出文件是否已存在
        if (output_gff and os.path.exists(output_gff)) or (output_fasta and os.path.exists(output_fasta)):
            if not overwrite:
                # 如果文件已存在且不覆盖，则跳过 Barrnap
                logging.info(f"输出文件已存在且 overwrite=False，跳过 Barrnap。")
                result = {"success": True, "features": [], "output_files": [], "error": None}

                # 检查哪些文件存在并加入输出列表
                if output_gff and os.path.exists(output_gff):
                    result["output_files"].append(output_gff)
                    # 解析已存在的 GFF 文件
                    result["features"] = self._parse_gff_from_file(output_gff)

                if output_fasta and os.path.exists(output_fasta):
                    result["output_files"].append(output_fasta)

                return result
            else:
                # 如果覆盖，则删除已存在的文件
                logging.info(f"覆盖已存在的输出文件。")
                if output_gff and os.path.exists(output_gff):
                    os.remove(output_gff)
                if output_fasta and os.path.exists(output_fasta):
                    os.remove(output_fasta)

        # 转换输入文件为绝对路径
        abs_input_fasta = os.path.abspath(input_fasta)
        cmd.append(abs_input_fasta)

        # 运行命令
        result = {
            "success": False,
            "features": [],
            "output_files": [],
            "error": None
        }

        try:
            # 处理输出重定向
            stdout = subprocess.PIPE
            output_file_handle = None
            if output_gff:
                output_file_handle = open(output_gff, 'w')
                stdout = output_file_handle
                result["output_files"].append(output_gff)

            cmd_str = ' '.join(cmd)
            logging.info(f"运行 Barrnap 命令: {cmd_str}")
            logging.info(f"工作目录: {tmp_dir}")

            process = subprocess.run(
                cmd,
                stdout=stdout,
                stderr=subprocess.PIPE,
                check=True,
                text=True,
                cwd=tmp_dir
            )

            # 日志输出
            if process.stderr:
                logging.debug(f"Barrnap 标准错误: {process.stderr}")

            # 如果未保存到文件，则解析标准输出
            if not output_gff:
                result["features"] = self._parse_gff(process.stdout.split('\n'))
            else:
                if output_file_handle:
                    output_file_handle.close()
                if os.path.exists(output_gff):
                    result["features"] = self._parse_gff_from_file(output_gff)
                else:
                    logging.warning(f"未找到输出 GFF 文件: {output_gff}")
                    result["error"] = f"未找到输出 GFF 文件: {output_gff}"
                    return result

            if output_fasta:
                if os.path.exists(output_fasta):
                    result["output_files"].append(output_fasta)
                else:
                    logging.warning(f"未找到输出 FASTA 文件: {output_fasta}")

            result["success"] = True

        except subprocess.CalledProcessError as e:
            error_msg = f"Barrnap 运行失败（退出码 {e.returncode}）"
            if e.stderr:
                error_msg += f"\n标准错误: {e.stderr}"
            logging.error(error_msg)
            result["error"] = error_msg
        except FileNotFoundError:
            error_msg = "未找到 Barrnap 可执行文件。请确认已安装并在 PATH 中。"
            logging.error(error_msg)
            result["error"] = error_msg
        except Exception as e:
            error_msg = f"运行时错误: {str(e)}"
            logging.error(error_msg)
            result["error"] = error_msg
        finally:
            # 关闭文件句柄
            if 'output_file_handle' in locals() and output_file_handle and not output_file_handle.closed:
                output_file_handle.close()

        return result

    def _parse_gff_from_file(self, gff_path: str) -> List[Dict]:
        """解析 GFF 输出文件为结构化数据。"""
        with open(gff_path) as f:
            return self._parse_gff(f.readlines())

    def _parse_gff(self, lines: List[str]) -> List[Dict]:
        """解析 GFF 行为结构化特征。"""
        features = []

        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            parts = line.split('\t')
            if len(parts) < 9:
                continue

            attributes = self._parse_attributes(parts[8])
            feature = {
                "seqid": parts[0],
                "source": parts[1],
                "type": parts[2],
                "start": int(parts[3]),
                "end": int(parts[4]),
                "score": float(parts[5]) if parts[5] != '.' else None,
                "strand": parts[6],
                "phase": parts[7],
                "name": attributes.get("Name"),
                "product": attributes.get("product"),
                "full_attributes": attributes
            }
            features.append(feature)

        return features

    def _parse_attributes(self, attr_str: str) -> Dict:
        """解析 GFF 属性字符串为字典。"""
        attributes = {}
        for pair in attr_str.split(';'):
            if '=' in pair:
                key, value = pair.split('=', 1)
                attributes[key.strip()] = value.strip()
        return attributes

    def get_rrna_summary(self, features: List[Dict]) -> Dict[str, int]:
        """
        获取特征中各类 rRNA 的统计摘要。

        参数:
            features: predict_rrna 返回的 rRNA 特征列表

        返回:
            各类 rRNA 类型的计数字典
        """
        counts = {
            '16S': 0,
            '23S': 0,
            '5S': 0,
            '12S': 0,
            'other': 0
        }

        for feat in features:
            product = feat.get("product", "").lower()
            if '16s' in product:
                counts['16S'] += 1
            elif '23s' in product:
                counts['23S'] += 1
            elif '5s' in product:
                counts['5S'] += 1
            elif '12s' in product:
                counts['12S'] += 1
            else:
                counts['other'] += 1

        return counts

