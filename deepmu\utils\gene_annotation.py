"""
DeepMu的预处理工具。

该模块提供用于预处理基因组数据的函数，包括
使用Prodigal进行基因预测和使用KofamScan进行KO注释。
"""

import os
import subprocess
import tempfile
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional
from Bio import SeqIO
from Bio.SeqRecord import SeqRecord
from subprocess import CalledProcessError, PIPE


class GenePredictionError(Exception):
    """基因预测失败时引发的异常。"""
    pass


class KOAnnotationError(Exception):
    """KO注释失败时引发的异常。"""
    pass


class PreprocessingError(Exception):
    """预处理失败时引发的异常。"""
    pass


class GenomePreprocessor:
    """
    基因组数据预处理器。

    该类提供用于预处理基因组数据的函数，包括
    使用Prodigal进行基因预测和使用KofamScan进行KO注释。
    """

    def __init__(self, kofamscan_db_path: Optional[str] = None, tmp_dir: Optional[str] = None, genetic_code: int = 11, cpu: Optional[int] = None):
        """
        初始化预处理器。

        参数:
            kofamscan_db_path: KofamScan数据库路径。如果为None，使用
                             环境变量KOFAMSCAN_DB_PATH。
            tmp_dir: 临时文件目录。如果为None，使用临时目录。
            genetic_code: NCBI genetic code ID (default: 11 for bacterial).
            cpu: Number of CPU cores to use for KofamScan (default: None, uses all available cores).
        """
        """
        Initialize the preprocessor.

        Args:
            kofamscan_db_path: Path to KofamScan database. If None, uses the
                             environment variable KOFAMSCAN_DB_PATH.
            tmp_dir: Directory for temporary files. If None, a temporary directory is used.
            genetic_code: NCBI genetic code ID (default: 11 for bacterial).
        """
        # Store parameters
        self.genetic_code = genetic_code
        self.kofamscan_db_path = kofamscan_db_path
        self.cpu = cpu

        # Set up temporary directory
        self.tmp_dir = tmp_dir if tmp_dir is not None else tempfile.gettempdir()

        # Ensure tmp_dir exists
        os.makedirs(self.tmp_dir, exist_ok=True)

        # Check if prodigal is installed
        try:
            logging.info("Checking Prodigal installation...")
            subprocess.run(["prodigal", "-h"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
            logging.info("Prodigal is installed and available")
        except FileNotFoundError:
            logging.error("Prodigal is not installed")
            raise PreprocessingError("Prodigal is not installed. Please install it first.")

        # Check if KofamScan is installed
        try:
            logging.info("Checking KofamScan installation...")
            subprocess.run(["exec_annotation", "-h"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
            logging.info("KofamScan is installed and available")
            self.kofamscan_available = True
        except FileNotFoundError:
            logging.warning("KofamScan is not installed. KO annotation will be skipped.")
            self.kofamscan_available = False

    def predict_genes(self,
                     genome_file: str,
                     output_file: Optional[str] = None,
                     protein_file: Optional[str] = None,
                     cds_file: Optional[str] = None,
                     meta_mode: bool = False) -> Tuple[str, str]:
        """
        Predict genes using Prodigal.

        Args:
            genome_file: Path to the genome FASTA file.
            output_file: Path to the output GFF file. If None, a temporary file is created.
            protein_file: Path to the output protein FASTA file. If None, a temporary file is created.
            cds_file: Path to the output nucleotide CDS file. If None, a temporary file is created.
            meta_mode: Whether to use metagenome mode.

        Returns:
            Tuple of (protein_file_path, cds_file_path)

        Raises:
            GenePredictionError: If gene prediction fails.
        """
        if not os.path.exists(genome_file):
            raise GenePredictionError(f"Genome file {genome_file} does not exist.")

        # Create temporary files if not provided
        if output_file is None:
            output_file = os.path.join(self.tmp_dir, f"{os.path.basename(genome_file)}.gff")

        if protein_file is None:
            protein_file = os.path.join(self.tmp_dir, f"{os.path.basename(genome_file)}.faa")

        if cds_file is None:
            cds_file = os.path.join(self.tmp_dir, f"{os.path.basename(genome_file)}.ffn")

        # Construct prodigal command
        cmd = [
            "prodigal",
            "-i", genome_file,
            "-o", output_file,
            "-a", protein_file,
            "-d", cds_file,
            "-f", "gff",
            "-g", str(self.genetic_code)
        ]

        # Add meta mode flag if requested
        if meta_mode:
            cmd.extend(["-p", "meta"])

        # Run prodigal
        try:
            logging.info(f"Running Prodigal on {genome_file}")
            if meta_mode:
                logging.info("Using metagenome mode")
            result = subprocess.run(cmd, stdout=PIPE, stderr=PIPE, check=True)
            logging.debug(f"Prodigal stdout: {result.stdout.decode('utf-8')}")
            logging.debug(f"Prodigal stderr: {result.stderr.decode('utf-8')}")

            if not os.path.exists(protein_file) or os.path.getsize(protein_file) == 0:
                logging.error("Prodigal failed to generate protein file")
                raise GenePredictionError("Prodigal failed to generate protein file")
            if not os.path.exists(cds_file) or os.path.getsize(cds_file) == 0:
                logging.error("Prodigal failed to generate CDS file")
                raise GenePredictionError("Prodigal failed to generate CDS file")

            logging.info("Gene prediction completed successfully")
            return protein_file, cds_file

        except CalledProcessError as e:
            logging.error(f"Prodigal failed: {e.stderr.decode('utf-8')}")
            raise GenePredictionError(f"Prodigal failed: {e.stderr.decode('utf-8')}")
        except Exception as e:
            logging.error(f"Unexpected error during gene prediction: {str(e)}")
            raise GenePredictionError(f"Unexpected error during gene prediction: {str(e)}")

    def annotate_ko(self,
                   protein_file: str,
                   output_file: Optional[str] = None) -> Dict[str, Set[str]]:
        """
        Annotate proteins with KO terms using KofamScan.

        Args:
            protein_file: Path to the protein FASTA file.
            output_file: Path to the output KO annotation file. If None, a temporary file is created.

        Returns:
            Dictionary mapping sequence IDs to sets of KO terms.
            If KofamScan is not available, returns an empty dictionary.

        Raises:
            KOAnnotationError: If KO annotation fails.
        """
        if not hasattr(self, 'kofamscan_available') or not self.kofamscan_available:
            logging.warning("KofamScan is not available. Skipping KO annotation.")
            # Create an empty output file
            if output_file:
                with open(output_file, 'w') as f:
                    f.write("# KO annotation skipped - KofamScan not available\n")
            return {}

        if not os.path.exists(protein_file):
            raise KOAnnotationError(f"Protein file {protein_file} does not exist.")

        # Create temporary file if not provided
        if output_file is None:
            output_file = os.path.join(self.tmp_dir, f"{os.path.basename(protein_file)}_ko.txt")

        # Construct KofamScan command
        cmd = ["exec_annotation"]

        # Add database paths if provided
        if self.kofamscan_db_path is not None:
            ko_list_path = os.path.join(self.kofamscan_db_path, "ko_list")
            profiles_path = os.path.join(self.kofamscan_db_path, "profiles")
            cmd.extend(["--profile", profiles_path, "--ko-list", ko_list_path])

        # Add output format and file
        cmd.extend(["-f", "mapper", "-o", output_file])

        # Add CPU parameter if specified
        if self.cpu is not None:
            cmd.extend(["--cpu", str(self.cpu)])

        # Add input file at the end
        cmd.append(protein_file)

        # Run KofamScan
        try:
            logging.info(f"Running KofamScan on {protein_file}")
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
            logging.debug(f"KofamScan stdout: {result.stdout.decode('utf-8')}")
            logging.debug(f"KofamScan stderr: {result.stderr.decode('utf-8')}")

            # Parse KO annotation file
            logging.info("Parsing KofamScan results")
            ko_dict = {}
            with open(output_file, 'r') as f:
                for line in f:
                    if line.startswith("#"):
                        continue
                    parts = line.strip().split("\t")
                    if len(parts) >= 2:
                        gene_id, ko_id = parts[0], parts[1]
                        if gene_id not in ko_dict:
                            ko_dict[gene_id] = set()
                        ko_dict[gene_id].add(ko_id)

            if not ko_dict:
                logging.error("No valid KO annotations found in output file")
                raise KOAnnotationError("No valid KO annotations found in output file")

            logging.info(f"Successfully annotated {len(ko_dict)} sequences with KO terms")
            return ko_dict

        except subprocess.CalledProcessError as e:
            logging.error(f"KofamScan failed: {e.stderr.decode('utf-8')}")
            raise KOAnnotationError(f"KofamScan failed: {e.stderr.decode('utf-8')}")
        except Exception as e:
            logging.error(f"Failed to parse KO annotations: {str(e)}")
            raise KOAnnotationError(f"Failed to parse KO annotations: {str(e)}")


    def preprocess_genome(self,
                         genome_file: str,
                         output_dir: Optional[str] = None,
                         meta_mode: bool = False) -> Tuple[str, str, Dict[str, Set[str]]]:
        """
        Preprocess a genome by predicting genes and annotating with KO terms.

        Args:
            genome_file: Path to the genome FASTA file.
            output_dir: Directory to store output files. If None, temporary files are used.
            meta_mode: Whether to use metagenome mode for gene prediction.

        Returns:
            Tuple of (protein_file_path, cds_file_path, ko_dict) where ko_dict maps sequence IDs to sets of KO terms.

        Raises:
            PreprocessingError: If preprocessing fails.
        """
        try:
            # Create output directory if provided
            if output_dir is not None:
                os.makedirs(output_dir, exist_ok=True)
                protein_file = os.path.join(output_dir, f"{os.path.basename(genome_file)}.faa")
                cds_file = os.path.join(output_dir, f"{os.path.basename(genome_file)}.ffn")
                ko_file = os.path.join(output_dir, f"{os.path.basename(genome_file)}_ko.txt")
            else:
                protein_file = None
                cds_file = None
                ko_file = None

            # Predict genes
            protein_file, cds_file = self.predict_genes(
                genome_file=genome_file,
                protein_file=protein_file,
                cds_file=cds_file,
                meta_mode=meta_mode
            )

            # Annotate with KO terms
            ko_dict = self.annotate_ko(
                protein_file=protein_file,
                output_file=ko_file
            )

            return protein_file, cds_file, ko_dict

        except (GenePredictionError, KOAnnotationError) as e:
            raise PreprocessingError(f"Preprocessing failed: {str(e)}")


class TaxonomyUtils:
    """Utility class for handling taxonomy data."""

    @staticmethod
    def parse_taxonomy_string(taxonomy_string: str) -> Dict[str, str]:
        """Parse a taxonomy string into a dictionary."""
        if not taxonomy_string:
            return {}

        levels = taxonomy_string.split('|')
        tax_dict = {}
        tax_levels = ['root', 'phylum', 'class', 'order', 'family', 'genus']

        for i, level in enumerate(levels):
            if i < len(tax_levels) and level:  # Only add non-empty levels
                tax_dict[tax_levels[i]] = level

        return tax_dict

    @staticmethod
    def format_taxonomy_dict(tax_dict: Dict[str, str]) -> str:
        """Format a taxonomy dictionary as a string."""
        if not tax_dict:
            return ""

        levels = ["root", "phylum", "class", "order", "family", "genus"]
        tax_ids = [tax_dict.get(level, "") for level in levels]

        # Remove trailing empty levels
        while tax_ids and not tax_ids[-1]:
            tax_ids.pop()

        return "|".join(tax_ids)