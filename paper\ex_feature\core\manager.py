"""
特征管理器

负责管理和协调不同类型的特征提取器，提供统一的特征管理接口。

主要功能：
1. 特征提取器注册和管理
2. 特征数据格式化和标准化
3. 特征质量控制和验证
4. 特征元数据管理
5. 特征缓存和优化
"""

import os
import logging
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import pickle
from datetime import datetime

from ..utils.config import TemperatureConfig

logger = logging.getLogger(__name__)

class FeatureManager:
    """
    特征管理器
    
    负责协调和管理所有特征提取器，提供统一的特征管理接口。
    """
    
    def __init__(self, config: TemperatureConfig):
        """
        初始化特征管理器
        
        参数:
            config: 温度配置对象
        """
        self.config = config
        self.extractors = {}
        self.feature_metadata = {}
        self.cache_enabled = config.get('cache_enabled', True)
        self.cache_dir = config.get('cache_dir', 'feature_cache')
        
        if self.cache_enabled:
            os.makedirs(self.cache_dir, exist_ok=True)
        
        logger.info("特征管理器初始化完成")
    
    def register_extractor(self, name: str, extractor: Any) -> None:
        """
        注册特征提取器
        
        参数:
            name: 提取器名称
            extractor: 特征提取器实例
        """
        self.extractors[name] = extractor
        
        # 获取特征元数据
        if hasattr(extractor, 'get_feature_metadata'):
            self.feature_metadata[name] = extractor.get_feature_metadata()
        
        logger.info(f"注册特征提取器: {name}")
    
    def get_extractor(self, name: str) -> Optional[Any]:
        """获取特征提取器"""
        return self.extractors.get(name)
    
    def list_extractors(self) -> List[str]:
        """列出所有已注册的特征提取器"""
        return list(self.extractors.keys())
    
    def extract_features(self, 
                        extractor_name: str,
                        input_data: Union[str, Dict],
                        cache_key: Optional[str] = None) -> Dict[str, Any]:
        """
        使用指定的提取器提取特征
        
        参数:
            extractor_name: 提取器名称
            input_data: 输入数据（文件路径或数据字典）
            cache_key: 缓存键（可选）
            
        返回:
            提取的特征字典
        """
        # 检查缓存
        if cache_key and self.cache_enabled:
            cached_features = self._load_from_cache(cache_key)
            if cached_features is not None:
                logger.debug(f"从缓存加载特征: {cache_key}")
                return cached_features
        
        # 获取提取器
        extractor = self.get_extractor(extractor_name)
        if extractor is None:
            raise ValueError(f"未找到特征提取器: {extractor_name}")
        
        # 提取特征
        try:
            features = extractor.extract_features(input_data)
            
            # 标准化特征格式
            standardized_features = self._standardize_features(features, extractor_name)
            
            # 保存到缓存
            if cache_key and self.cache_enabled:
                self._save_to_cache(cache_key, standardized_features)
            
            return standardized_features
            
        except Exception as e:
            logger.error(f"特征提取失败 ({extractor_name}): {e}")
            raise
    
    def _standardize_features(self, features: Dict[str, Any], extractor_name: str) -> Dict[str, Any]:
        """
        标准化特征格式
        
        参数:
            features: 原始特征字典
            extractor_name: 提取器名称
            
        返回:
            标准化后的特征字典
        """
        standardized = {}
        
        for key, value in features.items():
            # 确保特征值是数值类型
            if isinstance(value, (int, float, np.number)):
                standardized[key] = float(value)
            elif isinstance(value, (list, np.ndarray)):
                # 处理数组类型的特征
                if len(value) == 1:
                    standardized[key] = float(value[0])
                else:
                    # 多维特征展开
                    for i, v in enumerate(value):
                        standardized[f"{key}_{i}"] = float(v)
            elif isinstance(value, dict):
                # 处理嵌套字典
                for sub_key, sub_value in value.items():
                    standardized[f"{key}_{sub_key}"] = float(sub_value)
            else:
                # 其他类型转换为字符串
                standardized[key] = str(value)
        
        # 添加提取器信息
        standardized['_extractor'] = extractor_name
        standardized['_extraction_time'] = datetime.now().isoformat()
        
        return standardized
    
    def _load_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """从缓存加载特征"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.warning(f"加载缓存失败: {e}")
        
        return None
    
    def _save_to_cache(self, cache_key: str, features: Dict[str, Any]) -> None:
        """保存特征到缓存"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(features, f)
        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")
    
    def combine_features(self, feature_dicts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        合并多个特征字典
        
        参数:
            feature_dicts: 特征字典列表
            
        返回:
            合并后的特征字典
        """
        combined = {}
        
        for feature_dict in feature_dicts:
            for key, value in feature_dict.items():
                if key.startswith('_'):
                    # 跳过元数据字段
                    continue
                
                if key in combined:
                    logger.warning(f"特征键冲突: {key}")
                    # 添加后缀避免冲突
                    counter = 1
                    new_key = f"{key}_{counter}"
                    while new_key in combined:
                        counter += 1
                        new_key = f"{key}_{counter}"
                    combined[new_key] = value
                else:
                    combined[key] = value
        
        return combined
    
    def validate_features(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证特征质量
        
        参数:
            features: 特征字典
            
        返回:
            验证结果字典
        """
        validation_result = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'statistics': {}
        }
        
        # 检查特征数量
        feature_count = len([k for k in features.keys() if not k.startswith('_')])
        validation_result['statistics']['feature_count'] = feature_count
        
        if feature_count == 0:
            validation_result['valid'] = False
            validation_result['errors'].append("没有提取到任何特征")
        
        # 检查缺失值
        missing_count = 0
        infinite_count = 0
        
        for key, value in features.items():
            if key.startswith('_'):
                continue
            
            if value is None or (isinstance(value, float) and np.isnan(value)):
                missing_count += 1
            elif isinstance(value, float) and np.isinf(value):
                infinite_count += 1
        
        validation_result['statistics']['missing_count'] = missing_count
        validation_result['statistics']['infinite_count'] = infinite_count
        
        if missing_count > 0:
            validation_result['warnings'].append(f"发现 {missing_count} 个缺失值")
        
        if infinite_count > 0:
            validation_result['warnings'].append(f"发现 {infinite_count} 个无穷值")
        
        # 检查特征值范围
        numeric_features = {k: v for k, v in features.items() 
                          if not k.startswith('_') and isinstance(v, (int, float))}
        
        if numeric_features:
            values = list(numeric_features.values())
            validation_result['statistics']['min_value'] = min(values)
            validation_result['statistics']['max_value'] = max(values)
            validation_result['statistics']['mean_value'] = np.mean(values)
            validation_result['statistics']['std_value'] = np.std(values)
        
        return validation_result
    
    def get_feature_summary(self) -> Dict[str, Any]:
        """获取特征管理器摘要信息"""
        summary = {
            'registered_extractors': len(self.extractors),
            'extractor_names': list(self.extractors.keys()),
            'cache_enabled': self.cache_enabled,
            'cache_dir': self.cache_dir,
            'feature_metadata': self.feature_metadata
        }
        
        if self.cache_enabled and os.path.exists(self.cache_dir):
            cache_files = list(Path(self.cache_dir).glob("*.pkl"))
            summary['cached_features'] = len(cache_files)
        
        return summary
    
    def clear_cache(self) -> None:
        """清空特征缓存"""
        if self.cache_enabled and os.path.exists(self.cache_dir):
            cache_files = list(Path(self.cache_dir).glob("*.pkl"))
            for cache_file in cache_files:
                try:
                    cache_file.unlink()
                except Exception as e:
                    logger.warning(f"删除缓存文件失败 {cache_file}: {e}")
            
            logger.info(f"清空了 {len(cache_files)} 个缓存文件")
        else:
            logger.info("缓存未启用或缓存目录不存在")
