# 🧬 综合基因组特征提取器使用指南

## 📋 概述

综合基因组特征提取器 (`comprehensive_feature_extractor.py`) 是一个集成了所有现有功能的强大工具，专门为 `temp_data\high_quality\resume_test\Bacteria` 目录下的基因组数据提取最全面的特征集。

### 🎯 特征类型

| 特征类别 | 数量 | 描述 | 数据源 |
|----------|------|------|--------|
| **基因组特征** | ~40 | GC含量、序列组成、复杂度 | FASTA文件 |
| **蛋白质特征** | ~35 | 氨基酸组成、理化性质、稳定性 | CDS翻译 |
| **代谢途径特征** | ~20 | 酶系统、KEGG途径、COG分类 | GFF注释 |
| **RNA特征** | ~10 | tRNA/rRNA结构和稳定性 | GFF注释 |
| **系统发育特征** | ~8 | 分类学信息、进化距离 | 物种名称 |
| **密码子特征** | ~70 | 密码子使用偏好性 | CDS序列 |
| **DeepMu特征** | ~30 | 基因预测、注释工具特征 | DeepMu工具 |
| **序列编码特征** | ~8 | 序列编码统计 | 序列分析 |

**总计**: ~220个高质量特征

## 🚀 快速开始

### 1. 基本使用

```bash
# 进入工作目录
cd ex_feature

# 运行综合特征提取 (使用默认设置)
python comprehensive_feature_extractor.py
```

### 2. 自定义参数

```bash
# 指定线程数和输出目录
python comprehensive_feature_extractor.py \
    --threads 8 \
    --output-dir my_comprehensive_features

# 指定数据路径
python comprehensive_feature_extractor.py \
    --bacteria-dir ../temp_data/high_quality/resume_test/Bacteria \
    --download-results ../temp_data/high_quality/resume_test/download_results.tsv \
    --output-dir comprehensive_features \
    --threads 4
```

### 3. 禁用DeepMu工具 (如果工具不可用)

```bash
python comprehensive_feature_extractor.py --no-deepmu
```

## 📖 详细参数说明

### 必需参数

| 参数 | 默认值 | 描述 |
|------|--------|------|
| `--bacteria-dir` | `../temp_data/high_quality/resume_test/Bacteria` | Bacteria目录路径 |
| `--download-results` | `../temp_data/high_quality/resume_test/download_results.tsv` | 下载结果TSV文件 |

### 可选参数

| 参数 | 默认值 | 描述 |
|------|--------|------|
| `--output-dir` | `comprehensive_features` | 输出目录 |
| `--output-file` | `comprehensive_genome_features.tsv` | 输出文件名 |
| `--threads` | `4` | 并行处理线程数 |
| `--no-deepmu` | `False` | 禁用DeepMu工具 |
| `--temp-dir` | 系统临时目录 | 临时文件目录 |
| `--log-level` | `INFO` | 日志级别 (DEBUG/INFO/WARNING/ERROR) |

## 🔧 环境要求

### Python依赖

```bash
# 基础依赖
pip install pandas numpy matplotlib seaborn

# 生物信息学依赖
pip install biopython

# 可选: DeepMu工具依赖
# (如果要使用DeepMu功能)
```

### 外部工具 (可选)

如果要使用DeepMu功能，需要安装以下工具：

1. **Prodigal** - 基因预测
```bash
# Ubuntu/Debian
sudo apt-get install prodigal

# 或从源码编译
wget https://github.com/hyattpd/Prodigal/releases/download/v2.6.3/prodigal.linux
chmod +x prodigal.linux
sudo mv prodigal.linux /usr/local/bin/prodigal
```

2. **tRNAscan-SE** - tRNA预测
```bash
# 下载并安装
wget http://lowelab.ucsc.edu/software/tRNAscan-SE-2.0.9.tar.gz
tar -xzf tRNAscan-SE-2.0.9.tar.gz
cd tRNAscan-SE-2.0
./configure --prefix=/usr/local
make && sudo make install
```

3. **RNAmmer** - rRNA预测
```bash
# 需要从官网下载并安装
# http://www.cbs.dtu.dk/services/RNAmmer/
```

## 📁 输出文件结构

```
comprehensive_features/
├── comprehensive_genome_features.tsv    # 主要特征文件
├── comprehensive_genome_features.md     # 详细分析报告
└── comprehensive_extraction.log         # 运行日志
```

### 特征文件格式

TSV文件包含以下列：

```
taxid | species_name | temperature | organism_type | assembly_level | quality_score | genomic_* | protein_* | pathway_* | rna_* | phylo_* | cds_* | deepmu_* | encoding_*
```

## 🔍 特征详细说明

### 1. 基因组特征 (genomic_*)

- **GC含量**: `gc_content`, `at_content`
- **核苷酸组成**: `a_freq`, `t_freq`, `g_freq`, `c_freq`
- **二核苷酸**: `dinuc_aa`, `dinuc_at`, ..., `dinuc_gg`
- **序列复杂度**: `sequence_entropy`, `kmer2_diversity`
- **重复序列**: `simple_repeat_content`

### 2. 蛋白质特征 (protein_*)

- **氨基酸频率**: `aa_freq_A`, `aa_freq_R`, ..., `aa_freq_Y`
- **理化性质**: `avg_protein_mw`, `avg_hydrophobicity`, `charged_aa_ratio`
- **稳定性指标**: `cysteine_content`, `proline_content`, `glycine_content`
- **温度适应**: `thermophilic_aa_ratio`, `psychrophilic_aa_ratio`

### 3. 代谢途径特征 (pathway_*)

- **酶分类**: `enzyme_EC_1_count`, `enzyme_EC_2_count`, ..., `enzyme_EC_6_count`
- **代谢多样性**: `total_enzymes`, `enzyme_diversity`, `kegg_pathway_diversity`
- **应激响应**: `heat_shock_proteins`, `cold_shock_proteins`
- **核心代谢**: `energy_metabolism_ratio`, `amino_acid_metabolism_ratio`

### 4. RNA特征 (rna_*)

- **tRNA**: `trna_count`, `trna_avg_length`, `trna_gc_content`
- **rRNA**: `rrna_count`, `rrna_avg_length`, `rrna_gc_content`
- **稳定性**: `rna_stability_score`, `modified_bases_potential`

### 5. 系统发育特征 (phylo_*)

- **分类学**: `genus_diversity_score`, `taxonomic_depth`
- **进化**: `evolutionary_distance_score`, `family_conservation_score`
- **生态适应**: `is_extremophile`, `is_pathogen`, `is_marine`, `is_soil`

### 6. 密码子特征 (cds_*)

- **密码子频率**: `codon_aaa_freq`, `codon_aac_freq`, ..., `codon_ttt_freq`
- **GC含量**: `gc1_content`, `gc2_content`, `gc3_content`
- **偏好性**: `codon_adaptation_index`, `effective_number_codons`

### 7. DeepMu特征 (deepmu_*)

- **基因预测**: `gene_count`, `gene_avg_length`, `gene_density`
- **蛋白质**: `protein_count`, `protein_avg_length`, `protein_aa_*_freq`
- **tRNA**: `trna_count`, `trna_diversity`, `trna_avg_score`
- **rRNA**: `rrna_count`, `rrna_types`, `rrna_16s_count`

### 8. 序列编码特征 (encoding_*)

- **序列统计**: `length`, `a_freq`, `t_freq`, `g_freq`, `c_freq`
- **质量指标**: `n_content`, `base_diversity`

## 🎯 使用场景

### 1. 温度预测建模

```python
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score

# 加载特征数据
df = pd.read_csv('comprehensive_features/comprehensive_genome_features.tsv', sep='\t')

# 准备特征和目标
feature_cols = [col for col in df.columns if col not in ['taxid', 'species_name', 'temperature', 'organism_type', 'assembly_level', 'quality_score']]
X = df[feature_cols]
y = df['temperature']

# 训练模型
model = RandomForestRegressor(n_estimators=100, random_state=42)
scores = cross_val_score(model, X, y, cv=5, scoring='neg_mean_squared_error')
print(f"RMSE: {np.sqrt(-scores.mean()):.2f}°C")
```

### 2. 特征重要性分析

```python
# 训练模型并获取特征重要性
model.fit(X, y)
feature_importance = pd.DataFrame({
    'feature': feature_cols,
    'importance': model.feature_importances_
}).sort_values('importance', ascending=False)

print("前10个最重要特征:")
print(feature_importance.head(10))
```

### 3. 生物学分析

```python
# 分析不同特征类别的重要性
categories = ['genomic', 'protein', 'pathway', 'rna', 'phylo', 'cds', 'deepmu', 'encoding']

for category in categories:
    cat_features = [f for f in feature_cols if f.startswith(category)]
    if cat_features:
        cat_importance = feature_importance[feature_importance['feature'].isin(cat_features)]
        print(f"{category}: 平均重要性 = {cat_importance['importance'].mean():.4f}")
```

## 🚨 故障排除

### 常见问题

1. **DeepMu工具不可用**
   ```bash
   # 解决方案: 禁用DeepMu功能
   python comprehensive_feature_extractor.py --no-deepmu
   ```

2. **内存不足**
   ```bash
   # 解决方案: 减少线程数
   python comprehensive_feature_extractor.py --threads 2
   ```

3. **文件路径错误**
   ```bash
   # 解决方案: 检查并指定正确路径
   python comprehensive_feature_extractor.py \
       --bacteria-dir /correct/path/to/Bacteria \
       --download-results /correct/path/to/download_results.tsv
   ```

4. **权限问题**
   ```bash
   # 解决方案: 指定有写权限的输出目录
   python comprehensive_feature_extractor.py --output-dir /tmp/features
   ```

### 日志分析

```bash
# 查看详细日志
python comprehensive_feature_extractor.py --log-level DEBUG

# 检查日志文件
tail -f comprehensive_extraction.log
```

## 📊 性能优化

### 1. 并行处理

```bash
# 根据CPU核心数调整线程数
python comprehensive_feature_extractor.py --threads $(nproc)
```

### 2. 临时目录

```bash
# 使用SSD作为临时目录
python comprehensive_feature_extractor.py --temp-dir /fast/ssd/temp
```

### 3. 内存管理

- 对于大量基因组，建议分批处理
- 监控内存使用情况
- 必要时增加系统内存

## 🔄 扩展功能

### 需要添加的地方

1. **新特征类型**
   - 在 `_extract_single_genome_features()` 方法中添加新的特征提取调用
   - 实现对应的特征提取方法

2. **新的外部工具**
   - 在 `_init_extractors()` 方法中初始化新工具
   - 在 `_extract_deepmu_features()` 中添加新工具的调用

3. **新的分析方法**
   - 添加新的 `_analyze_*()` 方法
   - 在相应的特征提取方法中调用

### 示例: 添加新特征

```python
def _extract_custom_features(self, fasta_file: str) -> Dict[str, float]:
    """提取自定义特征"""
    features = {}
    
    try:
        # 实现自定义特征提取逻辑
        # ...
        
    except Exception as e:
        logger.error(f"提取自定义特征失败: {e}")
    
    return features

# 在 _extract_single_genome_features() 中调用
custom_features = self._extract_custom_features(fasta_file)
for key, value in custom_features.items():
    features[f'custom_{key}'] = value
```

## 📞 技术支持

如果遇到问题，请：

1. 检查日志文件 `comprehensive_extraction.log`
2. 确认所有依赖已正确安装
3. 验证输入文件格式和路径
4. 尝试使用 `--log-level DEBUG` 获取详细信息

---

**开发者**: 基于DeepMu项目和现有特征提取器整合开发  
**版本**: v1.0 (综合版)  
**更新日期**: 2025-07-15
