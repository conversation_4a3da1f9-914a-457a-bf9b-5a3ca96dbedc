"""
模型评估的指标工具。
"""

import numpy as np
import torch
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error, accuracy_score
from typing import Dict, Union, List, Tuple

def calculate_regression_accuracy(y_true_np, y_pred_np, tolerance=0.1):
    """
    通过考虑在真实值一定容差范围内的预测为正确来计算回归任务的准确率。

    参数:
        y_true_np: 真实值 (numpy数组)
        y_pred_np: 预测值 (numpy数组)
        tolerance: 考虑预测正确的相对容差
                  (默认: 0.1 = 真实值的10%)

    返回:
        准确率分数 (浮点数)
    """
    # 确保numpy数组并展平为1D
    y_true_np = np.array(y_true_np).flatten()
    y_pred_np = np.array(y_pred_np).flatten()

    # 计算相对误差
    mask = np.abs(y_true_np) > 1e-8  # 避免除零
    if np.sum(mask) == 0:
        return 0.0

    # 对于非零真实值，使用相对误差
    rel_errors = np.zeros_like(y_true_np, dtype=bool)
    rel_errors[mask] = (np.abs(y_true_np[mask] - y_pred_np[mask]) / np.abs(y_true_np[mask])) <= tolerance

    # 对于零真实值，使用带小阈值的绝对误差
    abs_threshold = np.mean(np.abs(y_true_np[mask])) * tolerance if np.sum(mask) > 0 else 0.01
    rel_errors[~mask] = np.abs(y_pred_np[~mask]) <= abs_threshold

    # 计算准确率
    accuracy = np.mean(rel_errors)
    return float(accuracy)

def calculate_metrics(
    y_true: torch.Tensor,
    y_pred: torch.Tensor,
    prefix: str = "",
    accuracy_tolerance: float = 0.1
) -> Dict[str, float]:
    """
    计算各种回归指标。

    参数:
        y_true: 真实值
        y_pred: 预测值
        prefix: 指标名称的可选前缀 (例如, 'growth_rate_', 'temperature_')
        accuracy_tolerance: 准确率计算的容差 (默认: 0.1 = 10%)

    返回:
        指标字典
    """
    # 转换为numpy用于sklearn指标
    y_true_np = y_true.detach().cpu().numpy()
    y_pred_np = y_pred.detach().cpu().numpy()

    # 计算指标
    metrics = {}

    # 均方误差 (已用作损失)
    mse = mean_squared_error(y_true_np, y_pred_np)
    metrics[f"{prefix}mse"] = mse

    # 均方根误差
    rmse = np.sqrt(mse)
    metrics[f"{prefix}rmse"] = rmse

    # 平均绝对误差
    mae = mean_absolute_error(y_true_np, y_pred_np)
    metrics[f"{prefix}mae"] = mae

    # R平方
    r2 = r2_score(y_true_np, y_pred_np)
    metrics[f"{prefix}r2"] = r2

    # 准确率 (在容差范围内)
    accuracy = calculate_regression_accuracy(y_true_np, y_pred_np, tolerance=accuracy_tolerance)
    metrics[f"{prefix}accuracy"] = accuracy

    # 皮尔逊相关系数
    if len(y_true_np) > 1:  # 相关性至少需要2个样本
        correlation = np.corrcoef(y_true_np, y_pred_np)[0, 1]
        metrics[f"{prefix}correlation"] = correlation
    else:
        metrics[f"{prefix}correlation"] = 0.0

    # 平均绝对百分比误差 (MAPE) 带上限以避免极值
    # 避免除零并限制极值
    mask = np.abs(y_true_np) > 1e-8  # 使用小的epsilon而不是精确的零
    if np.sum(mask) > 0:
        # 计算百分比误差
        percentage_errors = np.abs((y_true_np[mask] - y_pred_np[mask]) / np.abs(y_true_np[mask]))

        # 将极值限制为5.0 (500%)
        percentage_errors = np.minimum(percentage_errors, 5.0)

        # 计算MAPE
        mape = np.mean(percentage_errors) * 100
        metrics[f"{prefix}mape"] = mape
    else:
        metrics[f"{prefix}mape"] = 0.0

    # 对称平均绝对百分比误差 (SMAPE) - 对异常值和小值更稳健
    denominator = np.abs(y_true_np) + np.abs(y_pred_np)
    mask = denominator > 1e-8  # 避免被很小的数除
    if np.sum(mask) > 0:
        smape = np.mean(2.0 * np.abs(y_pred_np[mask] - y_true_np[mask]) / denominator[mask]) * 100
        metrics[f"{prefix}smape"] = smape
    else:
        metrics[f"{prefix}smape"] = 0.0

    return metrics

def calculate_multi_target_metrics(
    targets: Dict[str, torch.Tensor],
    predictions: Dict[str, torch.Tensor]
) -> Dict[str, float]:
    """
    计算多个目标的指标。

    参数:
        targets: 目标张量字典
        predictions: 预测张量字典

    返回:
        所有目标的指标字典
    """
    all_metrics = {}

    # 为每个目标计算指标
    for target_name, target_values in targets.items():
        if target_name in predictions:
            target_metrics = calculate_metrics(
                target_values,
                predictions[target_name],
                prefix=f"{target_name}_"
            )
            all_metrics.update(target_metrics)

    return all_metrics

def format_metrics(metrics: Dict[str, float]) -> str:
    """
    将指标字典格式化为用于日志记录的字符串。

    参数:
        metrics: 指标值字典

    返回:
        格式化的字符串
    """
    return ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
