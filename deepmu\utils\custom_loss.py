"""
DeepMu模型的自定义损失函数。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class CombinedLoss(nn.Module):
    """
    平衡MSE和MAPE的组合损失函数。

    该损失函数有助于平衡绝对误差（通过MSE）和
    相对误差（通过MAPE），以提高模型在不同目标值尺度上的性能。
    """

    def __init__(self, mse_weight=0.5, mape_weight=0.5, epsilon=1e-8):
        """
        初始化组合损失函数。

        参数:
            mse_weight: MSE组件的权重 (默认: 0.5)
            mape_weight: MAPE组件的权重 (默认: 0.5)
            epsilon: 避免除零的小常数 (默认: 1e-8)
        """
        super().__init__()
        self.mse_weight = mse_weight
        self.mape_weight = mape_weight
        self.epsilon = epsilon
        self.mse_loss = nn.MSELoss(reduction='mean')

    def forward(self, y_pred, y_true):
        """
        计算组合损失。

        参数:
            y_pred: 预测值
            y_true: 真实值

        返回:
            组合损失值
        """
        # MSE组件
        mse = self.mse_loss(y_pred, y_true)

        # MAPE组件（防止除零）
        abs_diff = torch.abs(y_pred - y_true)
        abs_val = torch.abs(y_true)
        # 添加epsilon避免除零
        mape = torch.mean(abs_diff / (abs_val + self.epsilon))

        # 组合损失
        combined_loss = self.mse_weight * mse + self.mape_weight * mape

        return combined_loss

class LogCoshLoss(nn.Module):
    """
    Log-cosh损失函数。

    该损失函数对小误差像MSE一样平滑，但对大误差
    像MAE一样对异常值不敏感。
    """

    def __init__(self):
        """初始化log-cosh损失函数。"""
        super().__init__()

    def forward(self, y_pred, y_true):
        """
        计算log-cosh损失。

        参数:
            y_pred: 预测值
            y_true: 真实值

        返回:
            Log-cosh损失值
        """
        diff = y_pred - y_true
        return torch.mean(torch.log(torch.cosh(diff)))

class HuberLoss(nn.Module):
    """
    Huber损失函数。

    该损失函数比MSE对异常值不敏感，但比MAE
    提供更多的梯度信息。
    """

    def __init__(self, delta=1.0):
        """
        初始化Huber损失函数。

        参数:
            delta: 从二次切换到线性的阈值 (默认: 1.0)
        """
        super().__init__()
        self.delta = delta

    def forward(self, y_pred, y_true):
        """
        计算Huber损失。

        参数:
            y_pred: 预测值
            y_true: 真实值

        返回:
            Huber损失值
        """
        abs_diff = torch.abs(y_pred - y_true)
        quadratic = torch.min(abs_diff, torch.tensor(self.delta))
        linear = abs_diff - quadratic
        loss = 0.5 * quadratic**2 + self.delta * linear
        return torch.mean(loss)

class LogTransformedMSELoss(nn.Module):
    """
    对数变换值的MSE损失。

    该损失函数通过在对数空间中工作来关注相对误差。
    """

    def __init__(self, epsilon=1e-8):
        """
        初始化对数变换MSE损失函数。

        参数:
            epsilon: 避免log(0)的小常数 (默认: 1e-8)
        """
        super().__init__()
        self.epsilon = epsilon
        self.mse_loss = nn.MSELoss(reduction='mean')

    def forward(self, y_pred, y_true):
        """
        计算对数变换MSE损失。

        参数:
            y_pred: 预测值
            y_true: 真实值

        返回:
            对数变换MSE损失值
        """
        # 添加epsilon并取对数
        log_pred = torch.log(torch.abs(y_pred) + self.epsilon)
        log_true = torch.log(torch.abs(y_true) + self.epsilon)

        # 在对数空间中计算MSE
        return self.mse_loss(log_pred, log_true)
