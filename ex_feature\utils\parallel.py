"""
并行处理器

提供多线程和多进程的并行处理功能，用于加速特征提取过程。

主要功能：
1. 多线程任务调度
2. 进度监控和报告
3. 错误处理和重试
4. 资源管理和优化
5. 动态负载均衡
"""

import os
import time
import logging
import threading
from typing import List, Callable, Any, Optional, Dict, Tuple
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from queue import Queue, Empty
import multiprocessing as mp

logger = logging.getLogger(__name__)

class ParallelProcessor:
    """
    并行处理器
    
    提供灵活的并行处理功能，支持多线程和多进程。
    """
    
    def __init__(self, 
                 num_workers: int = 4,
                 mode: str = 'thread',
                 max_retries: int = 3,
                 timeout: Optional[float] = None,
                 progress_callback: Optional[Callable] = None):
        """
        初始化并行处理器
        
        参数:
            num_workers: 工作线程/进程数
            mode: 并行模式 ('thread' 或 'process')
            max_retries: 最大重试次数
            timeout: 任务超时时间（秒）
            progress_callback: 进度回调函数
        """
        self.num_workers = num_workers
        self.mode = mode.lower()
        self.max_retries = max_retries
        self.timeout = timeout
        self.progress_callback = progress_callback
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'retried_tasks': 0,
            'start_time': None,
            'end_time': None
        }
        
        # 线程安全的锁
        self._lock = threading.Lock()
        
        logger.info(f"并行处理器初始化完成: {mode}模式, {num_workers}个工作者")
    
    def process_batch(self, 
                     tasks: List[Tuple[Callable, tuple, dict]],
                     description: str = "处理任务") -> List[Any]:
        """
        批量处理任务
        
        参数:
            tasks: 任务列表，每个任务是(函数, 位置参数, 关键字参数)的元组
            description: 任务描述
            
        返回:
            结果列表
        """
        logger.info(f"开始{description}: {len(tasks)}个任务")
        
        # 初始化统计
        with self._lock:
            self.stats['total_tasks'] = len(tasks)
            self.stats['completed_tasks'] = 0
            self.stats['failed_tasks'] = 0
            self.stats['retried_tasks'] = 0
            self.stats['start_time'] = time.time()
        
        results = []
        
        if self.mode == 'thread':
            results = self._process_with_threads(tasks, description)
        elif self.mode == 'process':
            results = self._process_with_processes(tasks, description)
        else:
            raise ValueError(f"不支持的并行模式: {self.mode}")
        
        # 完成统计
        with self._lock:
            self.stats['end_time'] = time.time()
        
        self._log_completion_stats(description)
        
        return results
    
    def _process_with_threads(self, tasks: List[Tuple], description: str) -> List[Any]:
        """使用线程池处理任务"""
        results = [None] * len(tasks)
        
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            # 提交所有任务
            future_to_index = {}
            for i, (func, args, kwargs) in enumerate(tasks):
                future = executor.submit(self._execute_task_with_retry, func, args, kwargs, i)
                future_to_index[future] = i
            
            # 收集结果
            for future in as_completed(future_to_index, timeout=self.timeout):
                index = future_to_index[future]
                
                try:
                    result = future.result()
                    results[index] = result
                    
                    with self._lock:
                        self.stats['completed_tasks'] += 1
                    
                    self._update_progress(description)
                    
                except Exception as e:
                    logger.error(f"任务 {index} 最终失败: {e}")
                    results[index] = None
                    
                    with self._lock:
                        self.stats['failed_tasks'] += 1
        
        return results
    
    def _process_with_processes(self, tasks: List[Tuple], description: str) -> List[Any]:
        """使用进程池处理任务"""
        results = [None] * len(tasks)
        
        # 准备可序列化的任务
        serializable_tasks = []
        for i, (func, args, kwargs) in enumerate(tasks):
            serializable_tasks.append((func, args, kwargs, i))
        
        with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            # 提交所有任务
            future_to_index = {}
            for task_data in serializable_tasks:
                future = executor.submit(self._execute_process_task, task_data)
                future_to_index[future] = task_data[3]  # index
            
            # 收集结果
            for future in as_completed(future_to_index, timeout=self.timeout):
                index = future_to_index[future]
                
                try:
                    result = future.result()
                    results[index] = result
                    
                    with self._lock:
                        self.stats['completed_tasks'] += 1
                    
                    self._update_progress(description)
                    
                except Exception as e:
                    logger.error(f"进程任务 {index} 失败: {e}")
                    results[index] = None
                    
                    with self._lock:
                        self.stats['failed_tasks'] += 1
        
        return results
    
    def _execute_task_with_retry(self, func: Callable, args: tuple, kwargs: dict, task_id: int) -> Any:
        """执行任务并支持重试"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                result = func(*args, **kwargs)
                
                if attempt > 0:
                    with self._lock:
                        self.stats['retried_tasks'] += 1
                    logger.debug(f"任务 {task_id} 在第 {attempt + 1} 次尝试成功")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(f"任务 {task_id} 第 {attempt + 1} 次尝试失败: {e}, "
                                 f"{wait_time}秒后重试")
                    time.sleep(wait_time)
                else:
                    logger.error(f"任务 {task_id} 所有重试都失败了")
        
        raise last_exception
    
    @staticmethod
    def _execute_process_task(task_data: Tuple) -> Any:
        """执行进程任务（静态方法，支持序列化）"""
        func, args, kwargs, task_id = task_data
        
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"进程任务 {task_id} 执行失败: {e}")
            raise
    
    def _update_progress(self, description: str) -> None:
        """更新进度"""
        if self.progress_callback:
            with self._lock:
                progress = self.stats['completed_tasks'] / self.stats['total_tasks']
                self.progress_callback(progress, description)
        
        # 定期输出进度日志
        with self._lock:
            completed = self.stats['completed_tasks']
            total = self.stats['total_tasks']
            
            if completed % max(1, total // 10) == 0 or completed == total:
                progress_pct = (completed / total) * 100
                logger.info(f"{description}进度: {completed}/{total} ({progress_pct:.1f}%)")
    
    def _log_completion_stats(self, description: str) -> None:
        """记录完成统计信息"""
        with self._lock:
            duration = self.stats['end_time'] - self.stats['start_time']
            total = self.stats['total_tasks']
            completed = self.stats['completed_tasks']
            failed = self.stats['failed_tasks']
            retried = self.stats['retried_tasks']
        
        logger.info(f"{description}完成统计:")
        logger.info(f"  总任务数: {total}")
        logger.info(f"  成功完成: {completed}")
        logger.info(f"  失败任务: {failed}")
        logger.info(f"  重试任务: {retried}")
        logger.info(f"  总耗时: {duration:.2f}秒")
        logger.info(f"  平均速度: {completed/duration:.2f}任务/秒")
        
        if failed > 0:
            success_rate = (completed / total) * 100
            logger.warning(f"  成功率: {success_rate:.1f}%")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = self.stats.copy()
        
        if stats['start_time'] and stats['end_time']:
            stats['duration'] = stats['end_time'] - stats['start_time']
            stats['tasks_per_second'] = stats['completed_tasks'] / stats['duration'] if stats['duration'] > 0 else 0
        
        return stats
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        with self._lock:
            self.stats = {
                'total_tasks': 0,
                'completed_tasks': 0,
                'failed_tasks': 0,
                'retried_tasks': 0,
                'start_time': None,
                'end_time': None
            }
    
    @staticmethod
    def get_optimal_worker_count(task_type: str = 'io') -> int:
        """
        获取最优工作者数量
        
        参数:
            task_type: 任务类型 ('io' 或 'cpu')
            
        返回:
            推荐的工作者数量
        """
        cpu_count = mp.cpu_count()
        
        if task_type.lower() == 'io':
            # IO密集型任务，可以使用更多线程
            return min(32, cpu_count * 4)
        elif task_type.lower() == 'cpu':
            # CPU密集型任务，使用CPU核心数
            return cpu_count
        else:
            # 默认使用CPU核心数
            return cpu_count
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        # 清理资源（如果需要）
        pass
