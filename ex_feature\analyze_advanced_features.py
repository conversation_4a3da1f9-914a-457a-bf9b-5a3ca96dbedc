#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级特征分析器

分析提取的高级基因组特征，包括：
1. 蛋白质特征分析
2. 代谢途径特征分析
3. RNA特征分析
4. 系统发育特征分析
5. 综合特征相关性分析
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
import logging
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedFeatureAnalyzer:
    """高级特征分析器"""
    
    def __init__(self, features_file: str, output_dir: str = "advanced_analysis"):
        """初始化分析器"""
        self.features_file = features_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载数据
        self.df = self._load_data()
        
        # 分类特征
        self.feature_categories = self._categorize_features()
        
        logger.info(f"加载了 {len(self.df)} 个基因组，{len(self.feature_categories['all'])} 个特征")
    
    def _load_data(self) -> pd.DataFrame:
        """加载特征数据"""
        try:
            df = pd.read_csv(self.features_file, sep='\t')
            logger.info(f"成功加载高级特征数据: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"加载特征数据失败: {e}")
            raise
    
    def _categorize_features(self) -> dict:
        """分类特征"""
        categories = {
            'genomic': [col for col in self.df.columns if col.startswith('genomic_')],
            'protein': [col for col in self.df.columns if col.startswith('protein_')],
            'pathway': [col for col in self.df.columns if col.startswith('pathway_')],
            'rna': [col for col in self.df.columns if col.startswith('rna_')],
            'phylo': [col for col in self.df.columns if col.startswith('phylo_')],
            'cds': [col for col in self.df.columns if col.startswith('cds_')],
            'meta': ['taxid', 'species_name', 'temperature', 'organism_type', 'assembly_level', 'quality_score']
        }
        
        categories['all'] = [col for col in self.df.columns 
                           if col not in categories['meta']]
        
        return categories
    
    def analyze_all_correlations(self) -> pd.DataFrame:
        """分析所有特征与温度的相关性"""
        logger.info("分析所有特征与温度的相关性")
        
        correlations = []
        
        for feature in self.feature_categories['all']:
            try:
                corr = np.corrcoef(self.df[feature], self.df['temperature'])[0, 1]
                
                if not np.isnan(corr):
                    # 确定特征类别
                    category = 'other'
                    for cat_name, cat_features in self.feature_categories.items():
                        if feature in cat_features:
                            category = cat_name
                            break
                    
                    correlations.append({
                        'feature': feature,
                        'correlation': corr,
                        'abs_correlation': abs(corr),
                        'category': category
                    })
            except Exception as e:
                logger.warning(f"计算特征 {feature} 相关性失败: {e}")
        
        # 转换为DataFrame并排序
        corr_df = pd.DataFrame(correlations)
        corr_df = corr_df.sort_values('abs_correlation', ascending=False)
        
        # 保存相关性结果
        output_file = self.output_dir / "advanced_feature_correlations.tsv"
        corr_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
        
        logger.info(f"相关性分析完成，结果保存到: {output_file}")
        
        return corr_df
    
    def analyze_by_category(self, corr_df: pd.DataFrame):
        """按类别分析特征"""
        logger.info("按类别分析特征")
        
        category_stats = {}
        
        for category in ['genomic', 'protein', 'pathway', 'rna', 'phylo', 'cds']:
            cat_features = corr_df[corr_df['category'] == category]
            
            if len(cat_features) > 0:
                stats = {
                    'count': len(cat_features),
                    'mean_correlation': cat_features['abs_correlation'].mean(),
                    'max_correlation': cat_features['abs_correlation'].max(),
                    'top_feature': cat_features.iloc[0]['feature'] if len(cat_features) > 0 else None,
                    'top_correlation': cat_features.iloc[0]['correlation'] if len(cat_features) > 0 else None
                }
                category_stats[category] = stats
        
        # 保存类别统计
        stats_df = pd.DataFrame(category_stats).T
        output_file = self.output_dir / "category_statistics.tsv"
        stats_df.to_csv(output_file, sep='\t', encoding='utf-8')
        
        logger.info(f"类别统计保存到: {output_file}")
        
        return category_stats
    
    def generate_advanced_visualizations(self, corr_df: pd.DataFrame, category_stats: dict):
        """生成高级可视化图表"""
        logger.info("生成高级可视化图表")
        
        # 1. 综合分析图
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        
        # 温度分布
        axes[0, 0].hist(self.df['temperature'], bins=6, alpha=0.7, edgecolor='black', color='skyblue')
        axes[0, 0].set_xlabel('Temperature (°C)')
        axes[0, 0].set_ylabel('Number of Genomes')
        axes[0, 0].set_title('Temperature Distribution')
        axes[0, 0].grid(alpha=0.3)
        
        # 特征类别相关性比较
        categories = list(category_stats.keys())
        mean_corrs = [category_stats[cat]['mean_correlation'] for cat in categories]
        max_corrs = [category_stats[cat]['max_correlation'] for cat in categories]
        
        x = np.arange(len(categories))
        width = 0.35
        
        axes[0, 1].bar(x - width/2, mean_corrs, width, label='Mean Correlation', alpha=0.7)
        axes[0, 1].bar(x + width/2, max_corrs, width, label='Max Correlation', alpha=0.7)
        axes[0, 1].set_xlabel('Feature Category')
        axes[0, 1].set_ylabel('Correlation with Temperature')
        axes[0, 1].set_title('Feature Category Performance')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(categories, rotation=45)
        axes[0, 1].legend()
        axes[0, 1].grid(alpha=0.3)
        
        # 蛋白质特征：氨基酸组成与温度
        if 'protein_aa_freq_G' in self.df.columns and 'protein_aa_freq_A' in self.df.columns:
            axes[0, 2].scatter(self.df['protein_aa_freq_G'], self.df['temperature'], 
                              alpha=0.8, s=100, color='red', label='Glycine')
            axes[0, 2].scatter(self.df['protein_aa_freq_A'], self.df['temperature'], 
                              alpha=0.8, s=100, color='blue', label='Alanine')
            axes[0, 2].set_xlabel('Amino Acid Frequency')
            axes[0, 2].set_ylabel('Temperature (°C)')
            axes[0, 2].set_title('Amino Acid Composition vs Temperature')
            axes[0, 2].legend()
        
        # RNA特征：GC含量与温度
        if 'rna_trna_gc_content' in self.df.columns:
            axes[1, 0].scatter(self.df['rna_trna_gc_content'], self.df['temperature'], 
                              alpha=0.8, s=100, color='green')
            axes[1, 0].set_xlabel('tRNA GC Content')
            axes[1, 0].set_ylabel('Temperature (°C)')
            axes[1, 0].set_title('tRNA GC Content vs Temperature')
            
            # 添加趋势线
            if not self.df['rna_trna_gc_content'].isna().all():
                z = np.polyfit(self.df['rna_trna_gc_content'].dropna(), 
                              self.df.loc[self.df['rna_trna_gc_content'].notna(), 'temperature'], 1)
                p = np.poly1d(z)
                x_trend = np.linspace(self.df['rna_trna_gc_content'].min(), 
                                    self.df['rna_trna_gc_content'].max(), 100)
                axes[1, 0].plot(x_trend, p(x_trend), "r--", alpha=0.8)
        
        # 系统发育特征分布
        if 'phylo_is_extremophile' in self.df.columns:
            extremophile_counts = self.df['phylo_is_extremophile'].value_counts()
            axes[1, 1].pie(extremophile_counts.values, labels=['Non-extremophile', 'Extremophile'], 
                          autopct='%1.1f%%', startangle=90)
            axes[1, 1].set_title('Extremophile Distribution')
        
        # 前10个最重要特征
        top_10_features = corr_df.head(10)
        y_pos = np.arange(len(top_10_features))
        
        colors = ['red' if x < 0 else 'blue' for x in top_10_features['correlation']]
        bars = axes[1, 2].barh(y_pos, top_10_features['correlation'], color=colors, alpha=0.7)
        axes[1, 2].set_yticks(y_pos)
        axes[1, 2].set_yticklabels([f.split('_')[-1][:15] for f in top_10_features['feature']], fontsize=8)
        axes[1, 2].set_xlabel('Correlation with Temperature')
        axes[1, 2].set_title('Top 10 Most Important Features')
        axes[1, 2].grid(axis='x', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'advanced_feature_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 特征类别详细分析
        self._generate_category_detailed_plots(corr_df)
        
        logger.info("高级可视化图表生成完成")
    
    def _generate_category_detailed_plots(self, corr_df: pd.DataFrame):
        """生成各类别的详细分析图"""
        
        categories = ['protein', 'pathway', 'rna', 'phylo']
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        for i, category in enumerate(categories):
            ax = axes[i]
            
            cat_features = corr_df[corr_df['category'] == category].head(8)
            
            if len(cat_features) > 0:
                y_pos = np.arange(len(cat_features))
                colors = ['red' if x < 0 else 'blue' for x in cat_features['correlation']]
                
                bars = ax.barh(y_pos, cat_features['correlation'], color=colors, alpha=0.7)
                ax.set_yticks(y_pos)
                ax.set_yticklabels([f.replace(f'{category}_', '')[:20] for f in cat_features['feature']], 
                                  fontsize=8)
                ax.set_xlabel('Correlation with Temperature')
                ax.set_title(f'Top {category.title()} Features')
                ax.grid(axis='x', alpha=0.3)
                
                # 添加数值标签
                for j, (bar, corr) in enumerate(zip(bars, cat_features['correlation'])):
                    ax.text(corr + (0.01 if corr > 0 else -0.01), j, f'{corr:.3f}', 
                           va='center', ha='left' if corr > 0 else 'right', fontsize=7)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'category_detailed_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_comprehensive_report(self, corr_df: pd.DataFrame, category_stats: dict):
        """生成综合分析报告"""
        logger.info("生成综合分析报告")
        
        # 计算基本统计
        temp_stats = self.df['temperature'].describe()
        
        # 找出每个类别的最佳特征
        best_features = {}
        for category in category_stats.keys():
            cat_features = corr_df[corr_df['category'] == category]
            if len(cat_features) > 0:
                best_features[category] = cat_features.iloc[0]
        
        report_content = f"""# 高级基因组特征综合分析报告

## 分析概览
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {len(self.df)}
- 总特征数量: {len(self.feature_categories['all'])}
- 温度范围: {temp_stats['min']:.1f}°C - {temp_stats['max']:.1f}°C

## 特征类别性能排名

基于与温度的平均相关性排序：

| 排名 | 特征类别 | 特征数量 | 平均相关性 | 最大相关性 | 最佳特征 |
|------|----------|----------|------------|------------|----------|
"""
        
        # 按平均相关性排序
        sorted_categories = sorted(category_stats.items(), 
                                 key=lambda x: x[1]['mean_correlation'], reverse=True)
        
        for rank, (category, stats) in enumerate(sorted_categories, 1):
            best_feature = best_features.get(category, {})
            feature_name = best_feature.get('feature', 'N/A').replace(f'{category}_', '') if best_feature else 'N/A'
            
            report_content += f"| {rank} | **{category.title()}** | {stats['count']} | {stats['mean_correlation']:.4f} | {stats['max_correlation']:.4f} | {feature_name} |\n"
        
        report_content += f"""
## 关键发现

### 1. 最重要的温度预测特征

以下是与温度相关性最强的前15个特征：

| 排名 | 特征名称 | 相关系数 | 特征类别 | 生物学意义 |
|------|----------|----------|----------|------------|
"""
        
        for i, (_, row) in enumerate(corr_df.head(15).iterrows(), 1):
            feature_name = row['feature']
            correlation = row['correlation']
            category = row['category']
            
            # 生物学意义解释
            if 'protein_aa_freq' in feature_name:
                meaning = f"氨基酸{feature_name.split('_')[-1]}频率，影响蛋白质稳定性"
            elif 'protein_' in feature_name:
                meaning = "蛋白质理化性质，影响热稳定性"
            elif 'pathway_' in feature_name:
                meaning = "代谢途径特征，反映温度适应策略"
            elif 'rna_' in feature_name:
                meaning = "RNA结构特征，影响转录稳定性"
            elif 'phylo_' in feature_name:
                meaning = "系统发育特征，反映进化适应"
            elif 'cds_' in feature_name:
                meaning = "密码子使用特征，影响翻译效率"
            else:
                meaning = "基因组结构特征"
            
            short_name = feature_name.replace(f'{category}_', '') if category != 'other' else feature_name
            report_content += f"| {i} | {short_name} | {correlation:+.4f} | {category} | {meaning} |\n"
        
        # 添加各类别的详细分析
        report_content += f"""
## 各类别详细分析

### 1. 蛋白质特征 ({category_stats.get('protein', {}).get('count', 0)} 个特征)
"""
        
        if 'protein' in category_stats:
            protein_features = corr_df[corr_df['category'] == 'protein'].head(5)
            report_content += "**最重要的蛋白质特征:**\n"
            for _, row in protein_features.iterrows():
                feature_name = row['feature'].replace('protein_', '')
                report_content += f"- **{feature_name}**: r = {row['correlation']:+.4f}\n"
            
            report_content += f"""
**生物学解释:**
- 氨基酸组成直接影响蛋白质的热稳定性
- 疏水性氨基酸在高温环境中更稳定
- 带电氨基酸影响蛋白质的等电点和稳定性
- 芳香族氨基酸提供额外的结构稳定性

"""
        
        report_content += f"""### 2. 代谢途径特征 ({category_stats.get('pathway', {}).get('count', 0)} 个特征)
"""
        
        if 'pathway' in category_stats:
            pathway_features = corr_df[corr_df['category'] == 'pathway'].head(5)
            report_content += "**最重要的代谢特征:**\n"
            for _, row in pathway_features.iterrows():
                feature_name = row['feature'].replace('pathway_', '')
                report_content += f"- **{feature_name}**: r = {row['correlation']:+.4f}\n"
            
            report_content += f"""
**生物学解释:**
- 酶系统的多样性反映代谢适应能力
- 热激蛋白和冷激蛋白直接响应温度变化
- 能量代谢途径在不同温度下有不同的效率
- 应激响应系统是温度适应的关键

"""
        
        report_content += f"""### 3. RNA特征 ({category_stats.get('rna', {}).get('count', 0)} 个特征)
"""
        
        if 'rna' in category_stats:
            rna_features = corr_df[corr_df['category'] == 'rna'].head(5)
            report_content += "**最重要的RNA特征:**\n"
            for _, row in rna_features.iterrows():
                feature_name = row['feature'].replace('rna_', '')
                report_content += f"- **{feature_name}**: r = {row['correlation']:+.4f}\n"
            
            report_content += f"""
**生物学解释:**
- tRNA和rRNA的GC含量影响RNA稳定性
- RNA二级结构在高温下需要更强的稳定性
- 修饰碱基增强RNA的热稳定性
- RNA数量反映转录活性和代谢水平

"""
        
        report_content += f"""### 4. 系统发育特征 ({category_stats.get('phylo', {}).get('count', 0)} 个特征)
"""
        
        if 'phylo' in category_stats:
            phylo_features = corr_df[corr_df['category'] == 'phylo'].head(5)
            report_content += "**最重要的系统发育特征:**\n"
            for _, row in phylo_features.iterrows():
                feature_name = row['feature'].replace('phylo_', '')
                report_content += f"- **{feature_name}**: r = {row['correlation']:+.4f}\n"
            
            report_content += f"""
**生物学解释:**
- 极端环境微生物具有特殊的温度适应机制
- 进化距离反映温度适应的系统发育保守性
- 生态位特征与温度偏好密切相关
- 分类学位置影响温度适应策略

"""
        
        report_content += f"""
## 温度预测建议

### 1. 特征选择策略
基于分析结果，建议按以下优先级选择特征：

1. **蛋白质特征** (平均相关性: {category_stats.get('protein', {}).get('mean_correlation', 0):.4f})
   - 氨基酸组成特征
   - 蛋白质稳定性指标
   - 理化性质特征

2. **密码子特征** (平均相关性: {category_stats.get('cds', {}).get('mean_correlation', 0):.4f})
   - 密码子使用频率
   - GC含量在密码子位置

3. **基因组特征** (平均相关性: {category_stats.get('genomic', {}).get('mean_correlation', 0):.4f})
   - GC含量和序列组成
   - 序列复杂度特征

### 2. 模型建议
- **线性模型**: 使用前20个最相关特征
- **非线性模型**: 结合所有类别的特征
- **集成模型**: 分别训练各类别特征，然后集成

### 3. 生物学验证
- 验证蛋白质稳定性与温度的关系
- 分析代谢途径的温度依赖性
- 研究RNA结构稳定性的温度效应

## 数据质量评估

### 优势
1. **多层次特征**: 从基因组到蛋白质到代谢的完整覆盖
2. **生物学相关性**: 所有特征都有明确的生物学意义
3. **功能注释**: 结合GFF注释提取功能特征
4. **系统发育信息**: 考虑进化和分类学因素

### 局限性
1. 样本数量相对较少 ({len(self.df)} 个基因组)
2. 温度范围相对集中 ({temp_stats['min']:.1f}-{temp_stats['max']:.1f}°C)
3. 部分功能注释可能不完整

## 输出文件
- **特征相关性**: advanced_feature_correlations.tsv
- **类别统计**: category_statistics.tsv
- **可视化图表**: advanced_feature_analysis.png, category_detailed_analysis.png
- **分析报告**: comprehensive_advanced_report.md

---
*报告由高级基因组特征分析器自动生成*
"""
        
        # 保存报告
        report_file = self.output_dir / "comprehensive_advanced_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"综合分析报告已生成: {report_file}")
    
    def run_complete_analysis(self):
        """运行完整的高级特征分析"""
        logger.info("开始完整的高级特征分析流程")
        
        # 1. 相关性分析
        corr_df = self.analyze_all_correlations()
        
        # 2. 类别分析
        category_stats = self.analyze_by_category(corr_df)
        
        # 3. 生成可视化
        self.generate_advanced_visualizations(corr_df, category_stats)
        
        # 4. 生成综合报告
        self.generate_comprehensive_report(corr_df, category_stats)
        
        logger.info("✅ 完整的高级特征分析流程完成!")
        
        return {
            'correlations': corr_df,
            'category_stats': category_stats,
            'output_dir': self.output_dir
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="高级基因组特征分析器")
    
    parser.add_argument('--features', required=True, help='高级特征TSV文件路径')
    parser.add_argument('--output-dir', default='advanced_analysis', help='输出目录')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # 检查输入文件
    if not os.path.exists(args.features):
        logger.error(f"特征文件不存在: {args.features}")
        return 1
    
    try:
        # 创建分析器
        analyzer = AdvancedFeatureAnalyzer(
            features_file=args.features,
            output_dir=args.output_dir
        )
        
        # 运行完整分析
        results = analyzer.run_complete_analysis()
        
        # 输出结果摘要
        logger.info("=" * 60)
        logger.info("🎉 高级特征分析完成！主要结果:")
        logger.info(f"   📊 分析了 {len(analyzer.df)} 个细菌基因组")
        logger.info(f"   🔍 评估了 {len(analyzer.feature_categories['all'])} 个高级特征")
        logger.info(f"   📁 结果保存在: {results['output_dir']}")
        
        # 显示最重要的特征
        top_5_features = results['correlations'].head(5)
        logger.info("\n🏆 前5个最重要的温度预测特征:")
        for i, (_, row) in enumerate(top_5_features.iterrows(), 1):
            feature_name = row['feature']
            category = row['category']
            short_name = feature_name.replace(f'{category}_', '') if category != 'other' else feature_name
            logger.info(f"   {i}. {short_name} ({category}): r = {row['correlation']:+.4f}")
        
        # 显示类别性能
        logger.info("\n📈 特征类别性能:")
        sorted_categories = sorted(results['category_stats'].items(), 
                                 key=lambda x: x[1]['mean_correlation'], reverse=True)
        for category, stats in sorted_categories:
            logger.info(f"   {category}: {stats['mean_correlation']:.4f} (平均), {stats['max_correlation']:.4f} (最大)")
        
        logger.info("=" * 60)
        
        return 0
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
