"""DeepMu 的预处理模块。

本模块提供用于基因组数据预处理的函数，包括使用 Prodigal 进行基因预测和使用 KofamScan 进行 KO 注释。

典型用法:
    from deepmu.utils.preprocess import preprocess_genome

    # 预处理单个基因组
    cds_file, ko_file = preprocess_genome(
        genome_file="genome.fna",
        output_dir="preprocessed",
        kofamscan_db_path="/path/to/kofamscan_db"
    )
"""

import os
import logging
import concurrent.futures
import pandas as pd
from pathlib import Path
from typing import Dict, Tuple, Optional

from .gene_annotation import GenomePreprocessor, PreprocessingError
from .trna_annotation import tRNAscanSEWrapper
from .rrna_annotation import BarrnapWrapper

def preprocess_genome(
    genome_file: str,
    output_dir: str,
    kofamscan_db_path: Optional[str] = None,
    genetic_code: int = 11,
    meta_mode: bool = False,
    cpu: Optional[int] = None,
    identify_trnas: bool = False,
    trnascan_path: Optional[str] = None,
    identify_rrnas: bool = False,
    barrnap_path: Optional[str] = None,
    kingdom: str = 'bac',
    skip_existing: bool = False,
    metadata_file: Optional[str] = None
) -> Tuple[str, str, Optional[str], Optional[str]]:
    """
    通过基因预测、KO 注释，并可选地识别 tRNA 和 rRNA，对基因组进行预处理。

    参数:
        genome_file: 基因组 FASTA 文件路径
        output_dir: 输出文件存放目录
        kofamscan_db_path: KofamScan 数据库路径
        genetic_code: NCBI 遗传密码子 ID（默认: 11，细菌用）
        meta_mode: 是否使用宏基因组模式进行基因预测
        cpu: 用于处理的 CPU 核心数
        identify_trnas: 是否使用 tRNAscan-SE 识别 tRNA
        trnascan_path: tRNAscan-SE 可执行文件路径（默认: 使用系统 PATH）
        identify_rrnas: 是否使用 Barrnap 识别 rRNA
        barrnap_path: Barrnap 可执行文件路径（默认: 使用系统 PATH）
        kingdom: rRNA 预测的物种类型（bac/arc/euk/mito，默认: bac）
        skip_existing: 如果输出文件已存在是否跳过处理（默认: False）
        metadata_file: 包含 kingdom 信息的元数据文件路径（可选）

    返回:
        包含以下内容的元组:
            - CDS 序列文件（FASTA 格式）路径
            - KO 注释文件（TSV 格式）路径
            - tRNA 注释文件路径（如果 identify_trnas=True，否则为 None）
            - rRNA 注释文件路径（如果 identify_rrnas=True，否则为 None）

    异常:
        PreprocessingError: 如果预处理失败
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 初始化预处理器
    try:
        preprocessor = GenomePreprocessor(
            kofamscan_db_path=kofamscan_db_path,
            genetic_code=genetic_code,
            cpu=cpu
        )
    except PreprocessingError as e:
        raise PreprocessingError(f"初始化预处理器失败: {e}")

    # 设置输出路径
    base_name = os.path.splitext(os.path.basename(genome_file))[0]
    cds_file = os.path.join(output_dir, f"{base_name}_cds.ffn")
    protein_file = os.path.join(output_dir, f"{base_name}_cds.faa")
    ko_file = os.path.join(output_dir, f"{base_name}_ko.tsv")
    trna_file = os.path.join(output_dir, f"{base_name}_tRNA.tsv") if identify_trnas else None
    rrna_file = os.path.join(output_dir, f"{base_name}_rrna.gff") if identify_rrnas else None

    # 如果提供了元数据文件，则从中获取 kingdom
    if metadata_file and os.path.exists(metadata_file):
        try:
            metadata_df = pd.read_csv(metadata_file, sep='\t')
            # 从文件名中提取 genome_id
            genome_id = base_name

            # 检查元数据中是否存在 genome_id 且有 kingdom 列
            if 'genome_id' in metadata_df.columns and 'kingdom' in metadata_df.columns:
                genome_row = metadata_df[metadata_df['genome_id'] == genome_id]
                if not genome_row.empty and not pd.isna(genome_row['kingdom'].iloc[0]):
                    # 从元数据中更新 kingdom
                    kingdom_from_metadata = genome_row['kingdom'].iloc[0].lower()
                    if kingdom_from_metadata in ['bac', 'arc', 'euk', 'mito']:
                        logging.info(f"为 {genome_id} 使用元数据中的 kingdom '{kingdom_from_metadata}'")
                        kingdom = kingdom_from_metadata
                    else:
                        logging.warning(f"{genome_id} 的元数据中 kingdom '{kingdom_from_metadata}' 无效。使用默认值: {kingdom}")
        except Exception as e:
            logging.warning(f"读取元数据 kingdom 时出错: {e}。使用默认值: {kingdom}")

    # 检查已存在的输出文件，如果不跳过则删除
    if not skip_existing:
        for file_path in [cds_file, protein_file, ko_file]:
            if os.path.exists(file_path):
                logging.info(f"删除已存在文件: {file_path}")
                os.remove(file_path)

        # 同时检查 tRNA 和 rRNA 文件
        if identify_trnas:
            trna_tsv = os.path.join(output_dir, f"{base_name}_tRNA.tsv")
            trna_fasta = os.path.join(output_dir, f"{base_name}_tRNA.fasta")
            for file_path in [trna_tsv, trna_fasta]:
                if os.path.exists(file_path):
                    logging.info(f"删除已存在文件: {file_path}")
                    os.remove(file_path)

        if identify_rrnas:
            rrna_gff = os.path.join(output_dir, f"{base_name}_rrna.gff")
            rrna_fasta = os.path.join(output_dir, f"{base_name}_rrna.fasta")
            for file_path in [rrna_gff, rrna_fasta]:
                if os.path.exists(file_path):
                    logging.info(f"删除已存在文件: {file_path}")
                    os.remove(file_path)

    try:
        # 定义并行执行的函数
        def run_gene_prediction():
            logging.info(f"使用 Prodigal 对 {genome_file} 进行基因预测")
            # 记录将要运行的命令
            prodigal_cmd = f"prodigal -i {genome_file} -a {protein_file} -d {cds_file} -g {genetic_code} {'-p meta' if meta_mode else ''}"
            logging.info(f"Prodigal 命令: {prodigal_cmd}")
            return preprocessor.predict_genes(
                genome_file=genome_file,
                protein_file=protein_file,
                cds_file=cds_file,
                meta_mode=meta_mode
            )

        def run_trna_identification():
            if not identify_trnas:
                return None

            try:
                logging.info(f"使用 tRNAscan-SE 识别 {genome_file} 中的 tRNA")
                scanner = tRNAscanSEWrapper(trnascan_path or "tRNAscan-SE")
                result = scanner.predict_tRNAs(
                    input_fasta=genome_file,
                    output_prefix=base_name,
                    kingdom=kingdom,  # tRNA 和 rRNA 都使用相同的 kingdom 参数
                    genetic_code=genetic_code,
                    output_dir=output_dir,
                    overwrite=not skip_existing  # 如果不跳过则覆盖
                )

                if result["success"]:
                    logging.info(f"在 {genome_file} 中找到 {len(result['predicted_tRNAs'])} 个 tRNA")
                    return next((f for f in result["output_files"] if f.endswith("_tRNA.tsv")), None)
                else:
                    logging.warning(f"tRNAscan-SE 失败: {result.get('error', '未知错误')}")
                    return None
            except Exception as e:
                logging.warning(f"识别 tRNA 时出错: {e}")
                return None

        def run_rrna_identification():
            if not identify_rrnas:
                return None

            try:
                logging.info(f"使用 Barrnap 识别 {genome_file} 中的 rRNA")
                scanner = BarrnapWrapper(barrnap_path or "barrnap")
                result = scanner.predict_rrna(
                    input_fasta=genome_file,
                    output_gff=rrna_file,
                    output_fasta=os.path.join(output_dir, f"{base_name}_rrna.fasta"),
                    kingdom=kingdom,
                    threads=cpu or 1,
                    overwrite=not skip_existing  # 如果不跳过则覆盖
                )

                if result["success"]:
                    rrna_counts = scanner.get_rrna_summary(result["features"])
                    logging.info(f"在 {genome_file} 中找到 rRNA: {rrna_counts}")
                    return rrna_file
                else:
                    logging.warning(f"Barrnap 失败: {result.get('error', '未知错误')}")
                    return None
            except Exception as e:
                logging.warning(f"识别 rRNA 时出错: {e}")
                return None

        # 并行运行基因预测、tRNA 和 rRNA 识别
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            gene_prediction_future = executor.submit(run_gene_prediction)
            trna_future = executor.submit(run_trna_identification)
            rrna_future = executor.submit(run_rrna_identification)

            # 等待基因预测完成后再进行 KO 注释
            protein_file, cds_file = gene_prediction_future.result()
            trna_file = trna_future.result()
            rrna_file = rrna_future.result()

        # 使用 KO 注释（基因预测完成后）
        ko_dict = preprocessor.annotate_ko(
            protein_file=protein_file,
            output_file=ko_file
        )

        return cds_file, ko_file, trna_file, rrna_file

    except Exception as e:
        logging.error(f"preprocess_genome 出错: {e}")
        raise PreprocessingError(f"预处理失败: {e}")

def preprocess_batch(
    input_dir: str,
    output_dir: str,
    kofamscan_db_path: Optional[str] = None,
    genetic_code: int = 11,
    meta_mode: bool = False,
    cpu: Optional[int] = None,
    identify_trnas: bool = False,
    trnascan_path: Optional[str] = None,
    identify_rrnas: bool = False,
    barrnap_path: Optional[str] = None,
    kingdom: str = 'bac',
    skip_existing: bool = False,
    metadata_file: Optional[str] = None
) -> Dict[str, Tuple[str, str, Optional[str], Optional[str]]]:
    """
    批量预处理目录下的多个基因组。

    参数:
        input_dir: 包含基因组 FASTA 文件的目录
        output_dir: 输出文件存放目录
        kofamscan_db_path: KofamScan 数据库路径
        genetic_code: NCBI 遗传密码子 ID（默认: 11，细菌用）
        meta_mode: 是否使用宏基因组模式进行基因预测
        cpu: 用于处理的 CPU 核心数
        identify_trnas: 是否使用 tRNAscan-SE 识别 tRNA
        trnascan_path: tRNAscan-SE 可执行文件路径（默认: 使用系统 PATH）
        identify_rrnas: 是否使用 Barrnap 识别 rRNA
        barrnap_path: Barrnap 可执行文件路径（默认: 使用系统 PATH）
        kingdom: rRNA 预测的物种类型（bac/arc/euk/mito，默认: bac）
        skip_existing: 如果输出文件已存在是否跳过处理（默认: False）
        metadata_file: 包含 kingdom 信息的元数据文件路径（可选）

    返回:
        字典，键为 genome_id，值为 (cds_file, ko_file, trna_file, rrna_file) 路径的元组
    """
    results = {}

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 处理每个基因组文件
    for filename in os.listdir(input_dir):
        if filename.endswith(('.fna', '.fa', '.fasta')):
            genome_id = os.path.splitext(filename)[0]
            genome_file = os.path.join(input_dir, filename)
            genome_output_dir = os.path.join(output_dir, genome_id)

            try:
                cds_file, ko_file, trna_file, rrna_file = preprocess_genome(
                    genome_file=genome_file,
                    output_dir=genome_output_dir,
                    kofamscan_db_path=kofamscan_db_path,
                    genetic_code=genetic_code,
                    meta_mode=meta_mode,
                    cpu=cpu,
                    identify_trnas=identify_trnas,
                    trnascan_path=trnascan_path,
                    identify_rrnas=identify_rrnas,
                    barrnap_path=barrnap_path,
                    kingdom=kingdom,
                    skip_existing=skip_existing,
                    metadata_file=metadata_file
                )
                results[genome_id] = (cds_file, ko_file, trna_file, rrna_file)
            except PreprocessingError as e:
                print(f"警告: 处理 {filename} 失败: {e}")
                continue

    return results
