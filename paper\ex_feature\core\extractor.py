"""
温度预测特征提取器

这是温度预测特征提取的主要接口，整合了多种特征类型的提取，
支持多线程处理和断点恢复功能。

主要功能：
1. 统一的特征提取接口
2. 多线程并行处理
3. 断点恢复机制
4. 特征验证和质量控制
5. 灵活的配置管理
"""

import os
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import json

from .manager import FeatureManager
from .checkpoint import CheckpointManager
from ..utils.parallel import ParallelProcessor
from ..utils.validation import FeatureValidator
from ..utils.config import TemperatureConfig
from ..features.codon_temp import CodonTemperatureFeatures
from ..features.genomic_temp import GenomicTemperatureFeatures
from ..features.protein_temp import ProteinTemperatureFeatures
from ..features.pathway_temp import PathwayTemperatureFeatures
from ..features.phylo_temp import PhylogeneticTemperatureFeatures
from ..features.rna_temp import RNATemperatureFeatures

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TemperatureFeatureExtractor:
    """
    温度预测特征提取器
    
    这个类提供了完整的温度预测特征提取解决方案，包括：
    - 多种生物学特征的提取
    - 多线程并行处理
    - 断点恢复机制
    - 特征验证和质量控制
    """
    
    def __init__(self, 
                 num_threads: int = 4,
                 enable_checkpoint: bool = True,
                 checkpoint_dir: str = "checkpoints",
                 checkpoint_interval: int = 100,
                 feature_types: List[str] = None,
                 config_file: Optional[str] = None,
                 validation_enabled: bool = True,
                 log_level: str = "INFO"):
        """
        初始化温度特征提取器
        
        参数:
            num_threads: 并行线程数
            enable_checkpoint: 是否启用断点恢复
            checkpoint_dir: 断点文件保存目录
            checkpoint_interval: 断点保存间隔（处理多少个基因组后保存一次）
            feature_types: 要提取的特征类型列表
            config_file: 配置文件路径
            validation_enabled: 是否启用特征验证
            log_level: 日志级别
        """
        # 设置日志级别
        logging.getLogger().setLevel(getattr(logging, log_level.upper()))
        
        # 加载配置
        self.config = TemperatureConfig(config_file) if config_file else TemperatureConfig()
        
        # 基本参数
        self.num_threads = num_threads
        self.enable_checkpoint = enable_checkpoint
        self.checkpoint_interval = checkpoint_interval
        self.validation_enabled = validation_enabled
        
        # 特征类型
        if feature_types is None:
            feature_types = ['codon', 'genomic', 'protein', 'pathway', 'phylo', 'rna']
        self.feature_types = feature_types
        
        # 初始化组件
        self.feature_manager = FeatureManager(self.config)
        self.checkpoint_manager = CheckpointManager(checkpoint_dir) if enable_checkpoint else None
        self.parallel_processor = ParallelProcessor(num_threads)
        self.validator = FeatureValidator() if validation_enabled else None
        
        # 初始化特征提取器
        self._init_feature_extractors()
        
        logger.info(f"温度特征提取器初始化完成")
        logger.info(f"线程数: {num_threads}")
        logger.info(f"特征类型: {', '.join(feature_types)}")
        logger.info(f"断点恢复: {'启用' if enable_checkpoint else '禁用'}")
    
    def _init_feature_extractors(self):
        """初始化各种特征提取器"""
        self.extractors = {}
        
        if 'codon' in self.feature_types:
            self.extractors['codon'] = CodonTemperatureFeatures(self.config)
        
        if 'genomic' in self.feature_types:
            self.extractors['genomic'] = GenomicTemperatureFeatures(self.config)
            
        if 'protein' in self.feature_types:
            self.extractors['protein'] = ProteinTemperatureFeatures(self.config)
            
        if 'pathway' in self.feature_types:
            self.extractors['pathway'] = PathwayTemperatureFeatures(self.config)
            
        if 'phylo' in self.feature_types:
            self.extractors['phylo'] = PhylogeneticTemperatureFeatures(self.config)
            
        if 'rna' in self.feature_types:
            self.extractors['rna'] = RNATemperatureFeatures(self.config)
        
        logger.info(f"初始化了 {len(self.extractors)} 个特征提取器")
    
    def extract_single(self,
                      genome_id: str,
                      genome_file: Optional[str] = None,
                      cds_file: Optional[str] = None,
                      protein_file: Optional[str] = None,
                      ko_file: Optional[str] = None,
                      taxid: Optional[str] = None,
                      **kwargs) -> Dict[str, Any]:
        """
        提取单个基因组的温度预测特征
        
        参数:
            genome_id: 基因组ID
            genome_file: 基因组序列文件路径
            cds_file: CDS序列文件路径
            protein_file: 蛋白质序列文件路径
            ko_file: KO注释文件路径
            taxid: NCBI分类学ID
            **kwargs: 其他参数
            
        返回:
            Dict[str, Any]: 提取的特征字典
        """
        logger.info(f"开始提取基因组 {genome_id} 的温度预测特征")
        
        features = {'genome_id': genome_id}
        extraction_stats = {}
        
        # 提取各种类型的特征
        for feature_type, extractor in self.extractors.items():
            try:
                start_time = time.time()
                
                # 根据特征类型调用相应的提取方法
                if feature_type == 'codon' and cds_file:
                    type_features = extractor.extract_features(cds_file, ko_file)
                elif feature_type == 'genomic' and genome_file:
                    type_features = extractor.extract_features(genome_file)
                elif feature_type == 'protein' and protein_file:
                    type_features = extractor.extract_features(protein_file, ko_file)
                elif feature_type == 'pathway' and ko_file:
                    type_features = extractor.extract_features(ko_file)
                elif feature_type == 'phylo' and taxid:
                    type_features = extractor.extract_features(taxid)
                elif feature_type == 'rna' and genome_file:
                    type_features = extractor.extract_features(genome_file)
                else:
                    logger.warning(f"跳过 {feature_type} 特征提取：缺少必要的输入文件")
                    continue
                
                # 添加特征类型前缀
                prefixed_features = {f"{feature_type}_{k}": v for k, v in type_features.items()}
                features.update(prefixed_features)
                
                # 记录提取统计
                extraction_time = time.time() - start_time
                extraction_stats[feature_type] = {
                    'feature_count': len(type_features),
                    'extraction_time': extraction_time
                }
                
                logger.debug(f"{feature_type} 特征提取完成: {len(type_features)} 个特征, "
                           f"耗时 {extraction_time:.2f}s")
                
            except Exception as e:
                logger.error(f"提取 {feature_type} 特征时出错: {e}")
                extraction_stats[feature_type] = {'error': str(e)}
        
        # 特征验证
        if self.validator:
            validation_result = self.validator.validate_features(features)
            features['_validation'] = validation_result
        
        # 添加提取统计信息
        features['_extraction_stats'] = extraction_stats
        
        logger.info(f"基因组 {genome_id} 特征提取完成: 总共 {len(features)} 个特征")
        
        return features

    def extract_batch(self,
                     metadata_file: str,
                     output_dir: str,
                     genome_dir: Optional[str] = None,
                     cds_dir: Optional[str] = None,
                     protein_dir: Optional[str] = None,
                     ko_dir: Optional[str] = None,
                     resume: bool = True) -> None:
        """
        批量提取多个基因组的温度预测特征

        参数:
            metadata_file: 元数据文件路径（TSV格式）
            output_dir: 输出目录
            genome_dir: 基因组文件目录
            cds_dir: CDS文件目录
            protein_dir: 蛋白质文件目录
            ko_dir: KO注释文件目录
            resume: 是否从断点恢复
        """
        logger.info(f"开始批量特征提取")
        logger.info(f"元数据文件: {metadata_file}")
        logger.info(f"输出目录: {output_dir}")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 读取元数据
        metadata = pd.read_csv(metadata_file, sep='\t')
        total_genomes = len(metadata)
        logger.info(f"找到 {total_genomes} 个基因组")

        # 检查断点恢复
        processed_genomes = set()
        if resume and self.checkpoint_manager:
            checkpoint_data = self.checkpoint_manager.load_checkpoint("batch_extraction")
            if checkpoint_data:
                processed_genomes = set(checkpoint_data.get('processed_genomes', []))
                logger.info(f"从断点恢复: 已处理 {len(processed_genomes)} 个基因组")

        # 准备处理任务
        tasks = []
        for idx, row in metadata.iterrows():
            genome_id = row['genome_id']

            # 跳过已处理的基因组
            if genome_id in processed_genomes:
                continue

            # 构建文件路径
            files = self._build_file_paths(row, genome_dir, cds_dir, protein_dir, ko_dir)

            # 检查输出文件是否已存在
            output_file = os.path.join(output_dir, f"{genome_id}_temp_features.npz")
            if os.path.exists(output_file) and resume:
                processed_genomes.add(genome_id)
                continue

            tasks.append((genome_id, files, output_file))

        logger.info(f"需要处理 {len(tasks)} 个基因组")

        # 并行处理
        self._process_batch_parallel(tasks, processed_genomes, total_genomes)

        logger.info("批量特征提取完成")

    def _build_file_paths(self, row, genome_dir, cds_dir, protein_dir, ko_dir):
        """构建文件路径"""
        genome_id = row['genome_id']
        files = {}

        if genome_dir:
            genome_file = os.path.join(genome_dir, f"{genome_id}.fna")
            files['genome_file'] = genome_file if os.path.exists(genome_file) else None

        if cds_dir:
            cds_file = os.path.join(cds_dir, f"{genome_id}_cds.fna")
            files['cds_file'] = cds_file if os.path.exists(cds_file) else None

        if protein_dir:
            protein_file = os.path.join(protein_dir, f"{genome_id}_proteins.faa")
            files['protein_file'] = protein_file if os.path.exists(protein_file) else None

        if ko_dir:
            ko_file = os.path.join(ko_dir, f"{genome_id}_ko.tsv")
            files['ko_file'] = ko_file if os.path.exists(ko_file) else None

        # 从元数据获取其他信息
        files['taxid'] = row.get('taxid', None)

        return files

    def _process_batch_parallel(self, tasks, processed_genomes, total_genomes):
        """并行处理批量任务"""
        completed_count = len(processed_genomes)

        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            # 提交任务
            future_to_task = {
                executor.submit(self._process_single_task, task): task
                for task in tasks
            }

            # 处理完成的任务
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                genome_id, files, output_file = task

                try:
                    # 获取结果
                    features = future.result()

                    # 保存特征
                    if features:
                        np.savez_compressed(output_file, **features)
                        logger.info(f"保存特征到: {output_file}")

                    # 更新进度
                    processed_genomes.add(genome_id)
                    completed_count += 1

                    # 保存断点
                    if (self.checkpoint_manager and
                        completed_count % self.checkpoint_interval == 0):
                        self._save_checkpoint(processed_genomes, completed_count, total_genomes)

                    # 显示进度
                    progress = (completed_count / total_genomes) * 100
                    logger.info(f"进度: {completed_count}/{total_genomes} ({progress:.1f}%)")

                except Exception as e:
                    logger.error(f"处理基因组 {genome_id} 时出错: {e}")

        # 最终保存断点
        if self.checkpoint_manager:
            self._save_checkpoint(processed_genomes, completed_count, total_genomes)

    def _process_single_task(self, task):
        """处理单个任务"""
        genome_id, files, output_file = task

        try:
            features = self.extract_single(
                genome_id=genome_id,
                **files
            )
            return features
        except Exception as e:
            logger.error(f"提取基因组 {genome_id} 特征时出错: {e}")
            return None

    def _save_checkpoint(self, processed_genomes, completed_count, total_genomes):
        """保存断点"""
        checkpoint_data = {
            'processed_genomes': list(processed_genomes),
            'completed_count': completed_count,
            'total_genomes': total_genomes,
            'timestamp': time.time()
        }
        self.checkpoint_manager.save_checkpoint("batch_extraction", checkpoint_data)
        logger.info(f"保存断点: {completed_count}/{total_genomes}")

    def get_feature_info(self) -> Dict[str, Any]:
        """获取特征提取器信息"""
        info = {
            'version': '1.0.0',
            'feature_types': self.feature_types,
            'num_threads': self.num_threads,
            'checkpoint_enabled': self.enable_checkpoint,
            'validation_enabled': self.validation_enabled,
            'extractors': {}
        }

        for feature_type, extractor in self.extractors.items():
            info['extractors'][feature_type] = {
                'class': extractor.__class__.__name__,
                'feature_count': len(extractor.get_feature_names()) if hasattr(extractor, 'get_feature_names') else 'unknown'
            }

        return info
