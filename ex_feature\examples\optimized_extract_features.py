#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的温度特征提取示例脚本 - 集成DeepMu utils工具

这个脚本使用优化的特征提取器，结合了DeepMu项目的utils工具，
提供更强大的特征提取、预处理、特征选择和数据验证功能。

主要改进：
1. 集成DeepMu的异常处理和日志系统
2. 自动预处理基因组数据
3. 智能特征选择和数据缩放
4. 支持多种输出格式
5. 提供详细的处理报告

使用方法:
    # 单个基因组特征提取
    python optimized_extract_features.py single --genome genome.fna --output features.npz
    
    # 批量特征提取（推荐）
    python optimized_extract_features.py batch --metadata genomes.tsv --output-dir features/ --threads 8
    
    # 带特征选择的批量提取
    python optimized_extract_features.py batch --metadata genomes.tsv --output-dir features/ --feature-selection --max-features 30
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from optimized_extractor import OptimizedTemperatureExtractor
except ImportError:
    print("错误: 无法导入OptimizedTemperatureExtractor")
    print("请确保optimized_extractor.py文件存在且可访问")
    sys.exit(1)

def setup_logging(log_level: str = 'INFO', log_file: str = None):
    """设置日志配置"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=handlers
    )

def extract_single(args):
    """单个基因组特征提取"""
    print("=== 单个基因组特征提取 ===")
    
    # 创建提取器
    extractor = OptimizedTemperatureExtractor(
        num_threads=args.threads,
        enable_preprocessing=args.enable_preprocessing,
        enable_feature_selection=False,  # 单个基因组不需要特征选择
        enable_scaling=args.enable_scaling,
        scaler_type=args.scaler_type,
        output_dir=os.path.dirname(args.output) or ".",
        log_level=args.log_level
    )
    
    try:
        # 提取特征
        features = extractor.extract_single_genome(
            genome_id=args.genome_id or Path(args.genome).stem,
            genome_file=args.genome,
            cds_file=args.cds,
            protein_file=args.protein,
            ko_file=args.ko,
            temperature=args.temperature
        )
        
        # 保存结果
        import numpy as np
        feature_vector, feature_names = extractor._features_to_vector(features)
        
        np.savez_compressed(
            args.output,
            features=feature_vector.reshape(1, -1),
            feature_names=feature_names,
            genome_id=args.genome_id or Path(args.genome).stem
        )
        
        print(f"✅ 特征提取完成!")
        print(f"   基因组: {args.genome}")
        print(f"   特征数: {len(feature_names)}")
        print(f"   输出: {args.output}")
        
    except Exception as e:
        print(f"❌ 特征提取失败: {e}")
        return 1
    
    return 0

def extract_batch(args):
    """批量特征提取"""
    print("=== 批量特征提取 ===")
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志文件
    log_file = output_dir / "extraction.log" if args.save_log else None
    if log_file:
        setup_logging(args.log_level, str(log_file))
    
    # 创建提取器
    extractor = OptimizedTemperatureExtractor(
        num_threads=args.threads,
        enable_preprocessing=args.enable_preprocessing,
        enable_feature_selection=args.feature_selection,
        enable_scaling=args.enable_scaling,
        scaler_type=args.scaler_type,
        feature_selection_method=args.selection_method,
        max_features=args.max_features,
        output_dir=str(output_dir),
        log_level=args.log_level
    )
    
    try:
        start_time = time.time()
        
        # 批量提取特征
        output_file = extractor.extract_batch(
            metadata_file=args.metadata,
            genome_col=args.genome_col,
            cds_col=args.cds_col,
            protein_col=args.protein_col,
            ko_col=args.ko_col,
            temperature_col=args.temperature_col,
            genome_id_col=args.genome_id_col,
            output_file=args.output_file
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 批量特征提取完成!")
        print(f"   元数据文件: {args.metadata}")
        print(f"   输出文件: {output_file}")
        print(f"   处理时间: {duration:.2f} 秒")
        print(f"   线程数: {args.threads}")
        
        if args.feature_selection:
            print(f"   特征选择: {args.selection_method}")
            print(f"   最大特征数: {args.max_features}")
        
        # 生成处理报告
        if args.generate_report:
            generate_processing_report(output_dir, args, duration)
        
    except Exception as e:
        print(f"❌ 批量特征提取失败: {e}")
        return 1
    
    return 0

def generate_processing_report(output_dir: Path, args, duration: float):
    """生成处理报告"""
    try:
        report_file = output_dir / "processing_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 温度特征提取处理报告\n\n")
            f.write(f"## 处理概览\n")
            f.write(f"- 处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"- 元数据文件: {args.metadata}\n")
            f.write(f"- 输出目录: {output_dir}\n")
            f.write(f"- 处理时长: {duration:.2f} 秒\n")
            f.write(f"- 线程数: {args.threads}\n\n")
            
            f.write(f"## 配置参数\n")
            f.write(f"- 预处理: {'启用' if args.enable_preprocessing else '禁用'}\n")
            f.write(f"- 特征选择: {'启用' if args.feature_selection else '禁用'}\n")
            f.write(f"- 数据缩放: {'启用' if args.enable_scaling else '禁用'}\n")
            
            if args.feature_selection:
                f.write(f"- 选择方法: {args.selection_method}\n")
                f.write(f"- 最大特征数: {args.max_features}\n")
            
            if args.enable_scaling:
                f.write(f"- 缩放方法: {args.scaler_type}\n")
            
            f.write(f"\n## 输出文件\n")
            f.write(f"- 特征文件: {args.output_file}\n")
            f.write(f"- TSV文件: {Path(args.output_file).with_suffix('.tsv')}\n")
            f.write(f"- 日志文件: extraction.log\n")
            f.write(f"- 处理报告: processing_report.md\n")
        
        print(f"📊 处理报告已生成: {report_file}")
        
    except Exception as e:
        print(f"⚠️  生成处理报告失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="优化的温度特征提取器 - 集成DeepMu utils工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 单个基因组提取
    single_parser = subparsers.add_parser('single', help='单个基因组特征提取')
    single_parser.add_argument('--genome', required=True, help='基因组FASTA文件路径')
    single_parser.add_argument('--cds', help='CDS FASTA文件路径')
    single_parser.add_argument('--protein', help='蛋白质FASTA文件路径')
    single_parser.add_argument('--ko', help='KO注释文件路径')
    single_parser.add_argument('--temperature', type=float, help='已知温度')
    single_parser.add_argument('--genome-id', help='基因组ID')
    single_parser.add_argument('--output', required=True, help='输出文件路径')
    
    # 批量提取
    batch_parser = subparsers.add_parser('batch', help='批量特征提取')
    batch_parser.add_argument('--metadata', required=True, help='元数据TSV文件路径')
    batch_parser.add_argument('--output-dir', required=True, help='输出目录')
    batch_parser.add_argument('--output-file', default='batch_features.npz', help='输出文件名')
    
    # 列名配置
    batch_parser.add_argument('--genome-col', default='genome_file', help='基因组文件列名')
    batch_parser.add_argument('--cds-col', default='cds_file', help='CDS文件列名')
    batch_parser.add_argument('--protein-col', default='protein_file', help='蛋白质文件列名')
    batch_parser.add_argument('--ko-col', default='ko_file', help='KO文件列名')
    batch_parser.add_argument('--temperature-col', default='temperature', help='温度列名')
    batch_parser.add_argument('--genome-id-col', default='genome_id', help='基因组ID列名')
    
    # 共同参数
    for p in [single_parser, batch_parser]:
        p.add_argument('--threads', type=int, default=4, help='线程数')
        p.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                      default='INFO', help='日志级别')
        p.add_argument('--enable-preprocessing', action='store_true', 
                      help='启用预处理（需要DeepMu）')
        p.add_argument('--enable-scaling', action='store_true', default=True,
                      help='启用数据缩放')
        p.add_argument('--scaler-type', choices=['standard', 'minmax'], 
                      default='standard', help='缩放器类型')
    
    # 批量特有参数
    batch_parser.add_argument('--feature-selection', action='store_true', 
                             help='启用特征选择')
    batch_parser.add_argument('--selection-method', 
                             choices=['mutual_info', 'f_regression', 'random_forest'], 
                             default='mutual_info', help='特征选择方法')
    batch_parser.add_argument('--max-features', type=int, default=50, 
                             help='最大特征数')
    batch_parser.add_argument('--save-log', action='store_true', 
                             help='保存日志到文件')
    batch_parser.add_argument('--generate-report', action='store_true', 
                             help='生成处理报告')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # 设置日志
    if not hasattr(args, 'save_log') or not args.save_log:
        setup_logging(args.log_level)
    
    # 执行相应命令
    if args.command == 'single':
        return extract_single(args)
    elif args.command == 'batch':
        return extract_batch(args)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
