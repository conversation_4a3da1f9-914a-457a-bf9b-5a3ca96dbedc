"""
DeepMu的可视化和指标保存工具。
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional
import seaborn as sns
from pathlib import Path

def save_metrics_to_tsv(
    metrics_history: Dict[str, List[float]],
    output_path: str,
    filename: str = "metrics.tsv"
) -> str:
    """
    将训练指标历史保存到TSV文件。

    参数:
        metrics_history: 以指标名称为键、值列表为值的字典
        output_path: 保存TSV文件的目录
        filename: TSV文件名称

    返回:
        保存的TSV文件路径
    """
    # 如果输出目录不存在则创建
    os.makedirs(output_path, exist_ok=True)

    # 将指标历史转换为DataFrame
    metrics_df = pd.DataFrame(metrics_history)

    # 添加epoch列
    metrics_df['epoch'] = range(1, len(metrics_df) + 1)

    # 重新排列列，将epoch放在第一位
    cols = metrics_df.columns.tolist()
    cols = ['epoch'] + [col for col in cols if col != 'epoch']
    metrics_df = metrics_df[cols]

    # 保存为TSV
    file_path = os.path.join(output_path, filename)
    metrics_df.to_csv(file_path, sep='\t', index=False)

    return file_path

def plot_metrics(
    metrics_history: Dict[str, List[float]],
    output_path: str,
    prefix: str = "",
    figsize: tuple = (12, 8),
    dpi: int = 100
) -> List[str]:
    """
    为训练指标生成图表。

    参数:
        metrics_history: 以指标名称为键、值列表为值的字典
        output_path: 保存图表的目录
        prefix: 图表文件名前缀
        figsize: 图表尺寸
        dpi: 图表DPI

    返回:
        保存的图表路径列表
    """
    # 如果输出目录不存在则创建
    os.makedirs(output_path, exist_ok=True)

    # 设置图表样式
    sns.set_style("whitegrid")
    plt.rcParams.update({'font.size': 12})

    saved_plots = []

    # 按类型分组指标
    metric_groups = {}

    # 按前缀分组指标 (例如, growth_rate_, temperature_)
    for metric_name in metrics_history.keys():
        if metric_name == 'epoch':
            continue

        # 如果指标没有值则跳过
        if not metrics_history[metric_name] or len(metrics_history[metric_name]) == 0:
            continue

        parts = metric_name.split('_')
        if len(parts) > 2:
            # 对于像growth_rate_r2, growth_rate_rmse这样的指标
            group_prefix = f"{parts[0]}_{parts[1]}"
            metric_type = '_'.join(parts[2:])

            if group_prefix not in metric_groups:
                metric_groups[group_prefix] = {}

            metric_groups[group_prefix][metric_name] = metrics_history[metric_name]
        else:
            # For metrics without a specific prefix like loss
            if 'general' not in metric_groups:
                metric_groups['general'] = {}

            metric_groups['general'][metric_name] = metrics_history[metric_name]

    # Plot each group of metrics
    for group_name, group_metrics in metric_groups.items():
        # Create a figure for this group
        fig, ax = plt.subplots(figsize=figsize)

        # Plot each metric in the group
        for metric_name, values in group_metrics.items():
            # Get a clean label for the metric
            if '_' in metric_name:
                label = metric_name.split('_')[-1].upper()
            else:
                label = metric_name.upper()

            # Plot the metric
            ax.plot(range(1, len(values) + 1), values, marker='o', linestyle='-', label=label)

        # Set plot title and labels
        title = f"{group_name.replace('_', ' ').title()} Metrics"
        ax.set_title(title)
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Value')

        # Add legend
        ax.legend()

        # Add grid
        ax.grid(True, linestyle='--', alpha=0.7)

        # Save the plot
        plot_filename = f"{prefix}_{group_name}_metrics.png" if prefix else f"{group_name}_metrics.png"
        plot_path = os.path.join(output_path, plot_filename)
        plt.tight_layout()
        plt.savefig(plot_path, dpi=dpi)
        plt.close(fig)

        saved_plots.append(plot_path)

    # Create a combined plot for train vs validation metrics
    train_val_metrics = {}

    # Find train/val pairs
    for metric_name in metrics_history.keys():
        if metric_name.startswith('train_') and f'val_{metric_name[6:]}' in metrics_history:
            base_name = metric_name[6:]
            train_val_metrics[base_name] = {
                'train': metrics_history[f'train_{base_name}'],
                'val': metrics_history[f'val_{base_name}']
            }

    # Plot train vs val metrics
    if train_val_metrics:
        for base_name, values in train_val_metrics.items():
            fig, ax = plt.subplots(figsize=figsize)

            ax.plot(range(1, len(values['train']) + 1), values['train'],
                   marker='o', linestyle='-', label=f'Train {base_name}')
            ax.plot(range(1, len(values['val']) + 1), values['val'],
                   marker='s', linestyle='-', label=f'Validation {base_name}')

            # Set plot title and labels
            title = f"{base_name.replace('_', ' ').title()} - Train vs Validation"
            ax.set_title(title)
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Value')

            # Add legend
            ax.legend()

            # Add grid
            ax.grid(True, linestyle='--', alpha=0.7)

            # Save the plot
            plot_filename = f"{prefix}_{base_name}_train_val.png" if prefix else f"{base_name}_train_val.png"
            plot_path = os.path.join(output_path, plot_filename)
            plt.tight_layout()
            plt.savefig(plot_path, dpi=dpi)
            plt.close(fig)

            saved_plots.append(plot_path)

    # Create a loss plot
    if 'train_loss' in metrics_history and 'val_loss' in metrics_history:
        fig, ax = plt.subplots(figsize=figsize)

        ax.plot(range(1, len(metrics_history['train_loss']) + 1), metrics_history['train_loss'],
               marker='o', linestyle='-', label='Train Loss')
        ax.plot(range(1, len(metrics_history['val_loss']) + 1), metrics_history['val_loss'],
               marker='s', linestyle='-', label='Validation Loss')

        # Set plot title and labels
        ax.set_title('Training and Validation Loss')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Loss')

        # Add legend
        ax.legend()

        # Add grid
        ax.grid(True, linestyle='--', alpha=0.7)

        # Save the plot
        plot_filename = f"{prefix}_loss.png" if prefix else "loss.png"
        plot_path = os.path.join(output_path, plot_filename)
        plt.tight_layout()
        plt.savefig(plot_path, dpi=dpi)
        plt.close(fig)

        saved_plots.append(plot_path)

    return saved_plots

def create_summary_report(
    metrics_history: Dict[str, List[float]],
    output_path: str,
    filename: str = "training_summary.txt"
) -> str:
    """
    创建训练指标的摘要报告。

    参数:
        metrics_history: 以指标名称为键、值列表为值的字典
        output_path: 保存报告的目录
        filename: 报告文件名称

    返回:
        保存的报告路径
    """
    # 如果输出目录不存在则创建
    os.makedirs(output_path, exist_ok=True)

    # 获取每个指标的最终值
    final_metrics = {}
    for metric_name, values in metrics_history.items():
        if values and len(values) > 0:
            final_metrics[metric_name] = values[-1]

    # 获取每个指标的最佳值
    best_metrics = {}
    for metric_name, values in metrics_history.items():
        if not values or len(values) == 0:
            continue

        if 'loss' in metric_name:
            # 对于损失指标，越低越好
            best_metrics[metric_name] = min(values)
        elif any(term in metric_name for term in ['r2', 'correlation']):
            # For R2 and correlation, higher is better
            best_metrics[metric_name] = max(values)
        elif any(term in metric_name for term in ['mse', 'rmse', 'mae', 'mape', 'smape']):
            # For error metrics, lower is better
            best_metrics[metric_name] = min(values)

    # Create the report
    report = []
    report.append("=" * 80)
    report.append("TRAINING SUMMARY REPORT")
    report.append("=" * 80)
    report.append("")

    report.append("FINAL METRICS:")
    report.append("-" * 80)
    for metric_name, value in sorted(final_metrics.items()):
        report.append(f"{metric_name.ljust(30)}: {value:.6f}")
    report.append("")

    report.append("BEST METRICS:")
    report.append("-" * 80)
    for metric_name, value in sorted(best_metrics.items()):
        report.append(f"{metric_name.ljust(30)}: {value:.6f}")
    report.append("")

    # Save the report
    file_path = os.path.join(output_path, filename)
    with open(file_path, 'w') as f:
        f.write('\n'.join(report))

    return file_path
