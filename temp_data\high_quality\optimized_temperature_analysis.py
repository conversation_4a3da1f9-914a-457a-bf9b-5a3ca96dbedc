#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的基因组温度特征分析

基于55个优化特征进行深入的温度预测分析
"""

import pandas as pd
import numpy as np
import os
from typing import Dict, List, Tuple

class OptimizedTemperatureAnalyzer:
    """优化的温度分析器"""
    
    def __init__(self, features_file):
        """初始化分析器"""
        self.features_file = features_file
        self.df = None
        self.feature_categories = self._define_feature_categories()
        
    def _define_feature_categories(self) -> Dict[str, List[str]]:
        """定义特征类别"""
        return {
            'basic_genome': [
                'genome_size', 'contig_count', 'avg_contig_length', 'max_contig_length',
                'min_contig_length', 'contig_length_std', 'n50', 'n_content'
            ],
            'gc_features': [
                'gc_content', 'at_content', 'gc_mean', 'gc_std', 'gc_min', 'gc_max',
                'gc_range', 'gc_cv', 'gc_skew', 'at_skew', 'gc_skew_std', 'gc_skew_range'
            ],
            'nucleotide_composition': [
                'a_content', 't_content', 'g_content', 'c_content', 'cpg_content'
            ],
            'dinucleotide_features': [
                'dinuc_aa', 'dinuc_at', 'dinuc_ag', 'dinuc_ac', 'dinuc_ta', 'dinuc_tt',
                'dinuc_tg', 'dinuc_tc', 'dinuc_ga', 'dinuc_gt', 'dinuc_gg', 'dinuc_gc',
                'dinuc_ca', 'dinuc_ct', 'dinuc_cg', 'dinuc_cc'
            ],
            'complexity_features': [
                'kmer2_diversity', 'kmer2_complexity', 'kmer3_diversity', 'kmer3_complexity',
                'kmer4_diversity', 'kmer4_complexity', 'sequence_entropy'
            ],
            'repeat_features': [
                'simple_repeat_content', 'palindrome_density'
            ],
            'thermostability_features': [
                'avg_hydrogen_bonds', 'thermal_stability_index', 'avg_stacking_energy'
            ],
            'structural_features': [
                'genome_fragmentation', 'largest_contig_ratio'
            ]
        }
    
    def load_data(self):
        """加载数据"""
        print("=== 加载优化特征数据 ===")
        
        self.df = pd.read_csv(self.features_file, sep='\t')
        print(f"数据形状: {self.df.shape}")
        print(f"基因组数量: {len(self.df)}")
        
        # 分离特征和目标变量
        self.feature_columns = [col for col in self.df.columns 
                               if col not in ['taxid', 'species_name', 'organism_type', 'optimum_temperature']]
        
        print(f"特征数量: {len(self.feature_columns)}")
        print(f"温度范围: {self.df['optimum_temperature'].min():.1f}°C - {self.df['optimum_temperature'].max():.1f}°C")
        
    def analyze_feature_categories(self):
        """按类别分析特征"""
        print("\n=== 按类别分析特征 ===")
        
        category_correlations = {}
        
        for category, features in self.feature_categories.items():
            print(f"\n{category.upper().replace('_', ' ')} 特征:")
            
            available_features = [f for f in features if f in self.df.columns]
            if not available_features:
                print("  无可用特征")
                continue
            
            correlations = []
            for feature in available_features:
                corr = np.corrcoef(self.df[feature], self.df['optimum_temperature'])[0, 1]
                if not np.isnan(corr):
                    correlations.append((feature, corr))
            
            # 按绝对相关系数排序
            correlations.sort(key=lambda x: abs(x[1]), reverse=True)
            category_correlations[category] = correlations
            
            print(f"  最相关的前3个特征:")
            for i, (feature, corr) in enumerate(correlations[:3], 1):
                print(f"    {i}. {feature:25s}: {corr:+7.4f}")
        
        return category_correlations
    
    def find_best_features(self, top_n=15):
        """找出最佳的温度预测特征"""
        print(f"\n=== 最佳温度预测特征 (前{top_n}个) ===")
        
        correlations = []
        
        for feature in self.feature_columns:
            corr = np.corrcoef(self.df[feature], self.df['optimum_temperature'])[0, 1]
            if not np.isnan(corr):
                correlations.append({
                    'feature': feature,
                    'correlation': corr,
                    'abs_correlation': abs(corr),
                    'category': self._get_feature_category(feature)
                })
        
        # 按绝对相关系数排序
        correlations_df = pd.DataFrame(correlations)
        correlations_df = correlations_df.sort_values('abs_correlation', ascending=False)
        
        print("排名  特征名称                    相关系数    类别")
        print("-" * 65)
        for i, (_, row) in enumerate(correlations_df.head(top_n).iterrows(), 1):
            print(f"{i:2d}.   {row['feature']:25s}  {row['correlation']:+8.4f}   {row['category']}")
        
        return correlations_df
    
    def _get_feature_category(self, feature: str) -> str:
        """获取特征所属类别"""
        for category, features in self.feature_categories.items():
            if feature in features:
                return category
        return 'other'
    
    def analyze_temperature_patterns(self):
        """分析温度模式"""
        print("\n=== 温度模式分析 ===")
        
        # 按温度分组
        temp_groups = []
        for _, row in self.df.iterrows():
            temp = row['optimum_temperature']
            if temp <= 20:
                group = "低温菌 (≤20°C)"
            elif temp <= 25:
                group = "中低温菌 (21-25°C)"
            elif temp <= 30:
                group = "中温菌 (26-30°C)"
            else:
                group = "高温菌 (>30°C)"
            temp_groups.append(group)
        
        self.df['temp_group'] = temp_groups
        
        # 关键特征的温度组差异
        key_features = [
            'gc_content', 'thermal_stability_index', 'avg_hydrogen_bonds',
            'sequence_entropy', 'dinuc_gc', 'dinuc_at'
        ]
        
        print("温度组间关键特征差异:")
        for feature in key_features:
            if feature in self.df.columns:
                group_means = self.df.groupby('temp_group')[feature].mean()
                print(f"\n{feature}:")
                for group, mean_val in group_means.items():
                    print(f"  {group:15s}: {mean_val:.6f}")
    
    def multi_feature_prediction(self, correlations_df):
        """多特征线性预测"""
        print("\n=== 多特征线性预测 ===")
        
        # 选择前5个最相关的特征
        top_features = correlations_df.head(5)['feature'].tolist()
        print(f"使用特征: {', '.join(top_features)}")
        
        # 简单的多元线性回归（手工实现）
        X = self.df[top_features].values
        y = self.df['optimum_temperature'].values
        
        # 添加截距项
        X_with_intercept = np.column_stack([np.ones(len(X)), X])
        
        try:
            # 最小二乘法求解: β = (X'X)^(-1)X'y
            XtX = np.dot(X_with_intercept.T, X_with_intercept)
            Xty = np.dot(X_with_intercept.T, y)
            coefficients = np.linalg.solve(XtX, Xty)
            
            # 预测
            y_pred = np.dot(X_with_intercept, coefficients)
            
            # 计算误差
            mse = np.mean((y - y_pred) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(y - y_pred))
            r2 = 1 - (np.sum((y - y_pred) ** 2) / np.sum((y - np.mean(y)) ** 2))
            
            print(f"\n多元线性回归结果:")
            print(f"  RMSE: {rmse:.2f}°C")
            print(f"  MAE:  {mae:.2f}°C")
            print(f"  R²:   {r2:.3f}")
            
            print(f"\n回归系数:")
            print(f"  截距: {coefficients[0]:8.4f}")
            for i, feature in enumerate(top_features, 1):
                print(f"  {feature:25s}: {coefficients[i]:8.4f}")
            
            # 显示预测结果
            print(f"\n预测结果:")
            for i, (actual, predicted) in enumerate(zip(y, y_pred)):
                taxid = self.df.iloc[i]['taxid']
                species = self.df.iloc[i]['species_name']
                error = actual - predicted
                print(f"  taxid {taxid:2d} ({species:12s}): 实际 {actual:4.1f}°C, "
                      f"预测 {predicted:4.1f}°C, 误差 {error:+5.1f}°C")
            
            return {
                'features': top_features,
                'coefficients': coefficients,
                'rmse': rmse,
                'mae': mae,
                'r2': r2,
                'predictions': y_pred
            }
            
        except np.linalg.LinAlgError:
            print("矩阵奇异，无法求解多元线性回归")
            return None
    
    def feature_importance_analysis(self, correlations_df):
        """特征重要性分析"""
        print("\n=== 特征重要性分析 ===")
        
        # 按类别统计重要特征
        category_importance = {}
        for category in self.feature_categories.keys():
            category_features = correlations_df[
                correlations_df['feature'].apply(lambda x: self._get_feature_category(x) == category)
            ]
            if len(category_features) > 0:
                avg_importance = category_features['abs_correlation'].mean()
                max_importance = category_features['abs_correlation'].max()
                category_importance[category] = {
                    'avg': avg_importance,
                    'max': max_importance,
                    'count': len(category_features)
                }
        
        print("按类别的特征重要性:")
        print("类别                    平均重要性  最大重要性  特征数量")
        print("-" * 60)
        for category, stats in sorted(category_importance.items(), 
                                    key=lambda x: x[1]['avg'], reverse=True):
            print(f"{category:20s}  {stats['avg']:8.4f}    {stats['max']:8.4f}    {stats['count']:4d}")
    
    def generate_comprehensive_report(self, correlations_df, category_correlations, prediction_result):
        """生成综合分析报告"""
        print("\n=== 生成综合分析报告 ===")
        
        report_content = f"""# 优化基因组温度特征综合分析报告

## 分析概览
- 分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
- 基因组数量: {len(self.df)}
- 特征数量: {len(self.feature_columns)}
- 温度范围: {self.df['optimum_temperature'].min():.1f}°C - {self.df['optimum_temperature'].max():.1f}°C

## 最重要的温度预测特征 (前15个)
"""
        
        for i, (_, row) in enumerate(correlations_df.head(15).iterrows(), 1):
            report_content += f"{i:2d}. **{row['feature']}** ({row['category']}): {row['correlation']:+.4f}\n"
        
        report_content += f"""
## 按类别的特征重要性分析

### 1. 热稳定性特征 (核心)
这些特征直接反映基因组的热稳定性：
"""
        
        thermo_features = category_correlations.get('thermostability_features', [])
        for feature, corr in thermo_features:
            report_content += f"- **{feature}**: {corr:+.4f}\n"
        
        report_content += f"""
### 2. GC含量特征 (关键)
GC含量与温度适应性密切相关：
"""
        
        gc_features = category_correlations.get('gc_features', [])
        for feature, corr in gc_features[:5]:
            report_content += f"- **{feature}**: {corr:+.4f}\n"
        
        if prediction_result:
            report_content += f"""
## 多特征预测模型结果
- 使用特征: {', '.join(prediction_result['features'])}
- RMSE: {prediction_result['rmse']:.2f}°C
- MAE: {prediction_result['mae']:.2f}°C
- R²: {prediction_result['r2']:.3f}

### 回归方程
温度 = {prediction_result['coefficients'][0]:.4f}"""
            
            for i, feature in enumerate(prediction_result['features'], 1):
                coef = prediction_result['coefficients'][i]
                sign = '+' if coef >= 0 else ''
                report_content += f" {sign}{coef:.4f}×{feature}"
        
        report_content += f"""

## 生物学解释

### 高温适应机制
1. **高GC含量**: 增加DNA双链稳定性
2. **强氢键**: 提高分子间相互作用
3. **特定二核苷酸**: GC富集的二核苷酸增加
4. **低序列复杂度**: 减少不稳定结构

### 低温适应机制
1. **低GC含量**: 降低DNA熔解温度
2. **高AT含量**: 减少氢键强度
3. **特定序列模式**: AT富集的序列增加
4. **高序列复杂度**: 增加结构灵活性

## 特征工程建议

### 最有价值的特征类别
1. **热稳定性特征**: 直接相关，生物学意义明确
2. **GC含量特征**: 经典指标，预测能力强
3. **二核苷酸特征**: 细粒度信息，补充单核苷酸
4. **序列复杂度**: 反映基因组组织特征

### 模型改进方向
1. **特征组合**: 尝试特征交互项
2. **非线性模型**: 使用随机森林、神经网络等
3. **特征选择**: 使用递归特征消除
4. **数据增强**: 增加更多温度范围的样本

## 输出文件
- 特征相关性: optimized_feature_correlations.tsv
- 分析报告: optimized_temperature_analysis_report.md

## 技术优势
相比基础版本，优化版本提供了：
- 55个vs26个特征 (增加112%)
- 更细粒度的GC含量分析
- 热稳定性专门特征
- 序列复杂度和重复序列分析
- 更准确的温度预测能力
"""
        
        # 保存报告
        with open('optimized_temperature_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        # 保存相关性数据
        correlations_df.to_csv('optimized_feature_correlations.tsv', sep='\t', index=False, encoding='utf-8')
        
        print("综合分析报告已保存: optimized_temperature_analysis_report.md")
        print("特征相关性已保存: optimized_feature_correlations.tsv")

def main():
    """主函数"""
    print("优化基因组温度特征综合分析")
    print("=" * 50)
    
    # 检查特征文件
    features_file = "optimized_features/optimized_genome_temperature_features.tsv"
    if not os.path.exists(features_file):
        print(f"❌ 特征文件不存在: {features_file}")
        print("请先运行优化特征提取器生成特征数据")
        return
    
    # 创建分析器
    analyzer = OptimizedTemperatureAnalyzer(features_file)
    
    # 加载数据
    analyzer.load_data()
    
    # 按类别分析特征
    category_correlations = analyzer.analyze_feature_categories()
    
    # 找出最佳特征
    correlations_df = analyzer.find_best_features(15)
    
    # 温度模式分析
    analyzer.analyze_temperature_patterns()
    
    # 多特征预测
    prediction_result = analyzer.multi_feature_prediction(correlations_df)
    
    # 特征重要性分析
    analyzer.feature_importance_analysis(correlations_df)
    
    # 生成综合报告
    analyzer.generate_comprehensive_report(correlations_df, category_correlations, prediction_result)
    
    print("\n✅ 优化温度特征分析完成！")

if __name__ == "__main__":
    main()
