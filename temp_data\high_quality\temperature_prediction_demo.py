#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基因组序列最适生长温度预测演示

使用提取的基因组特征训练机器学习模型来预测微生物的最适生长温度
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

class TemperaturePredictionModel:
    """温度预测模型"""
    
    def __init__(self, features_file):
        """初始化模型"""
        self.features_file = features_file
        self.df = None
        self.X = None
        self.y = None
        self.scaler = StandardScaler()
        self.models = {}
        self.results = {}
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("=== 加载和准备数据 ===")
        
        # 读取特征数据
        self.df = pd.read_csv(self.features_file, sep='\t')
        print(f"数据形状: {self.df.shape}")
        print(f"基因组数量: {len(self.df)}")
        
        # 分离特征和目标变量
        feature_columns = [col for col in self.df.columns 
                          if col not in ['taxid', 'species_name', 'organism_type', 'optimum_temperature']]
        
        self.X = self.df[feature_columns]
        self.y = self.df['optimum_temperature']
        
        print(f"特征数量: {len(feature_columns)}")
        print(f"温度范围: {self.y.min():.1f}°C - {self.y.max():.1f}°C")
        
        # 检查缺失值
        missing_features = self.X.isnull().sum()
        if missing_features.sum() > 0:
            print(f"发现缺失值: {missing_features[missing_features > 0]}")
            self.X = self.X.fillna(self.X.mean())
        
        # 标准化特征
        self.X_scaled = self.scaler.fit_transform(self.X)
        
        print("数据准备完成")
        
    def train_models(self):
        """训练多个模型"""
        print("\n=== 训练预测模型 ===")
        
        # 定义模型
        self.models = {
            'Linear Regression': LinearRegression(),
            'Ridge Regression': Ridge(alpha=1.0),
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42)
        }
        
        # 交叉验证评估
        for name, model in self.models.items():
            print(f"\n训练 {name}...")
            
            # 使用标准化数据进行交叉验证
            cv_scores = cross_val_score(model, self.X_scaled, self.y, 
                                      cv=min(5, len(self.df)), 
                                      scoring='neg_mean_squared_error')
            
            rmse_scores = np.sqrt(-cv_scores)
            
            self.results[name] = {
                'cv_rmse_mean': rmse_scores.mean(),
                'cv_rmse_std': rmse_scores.std(),
                'model': model
            }
            
            print(f"  交叉验证 RMSE: {rmse_scores.mean():.2f} ± {rmse_scores.std():.2f}°C")
        
        # 选择最佳模型
        best_model_name = min(self.results.keys(), 
                             key=lambda x: self.results[x]['cv_rmse_mean'])
        print(f"\n最佳模型: {best_model_name}")
        
        # 训练最佳模型
        self.best_model = self.results[best_model_name]['model']
        self.best_model.fit(self.X_scaled, self.y)
        
        return best_model_name
    
    def analyze_feature_importance(self, model_name):
        """分析特征重要性"""
        print(f"\n=== 特征重要性分析 ({model_name}) ===")
        
        model = self.results[model_name]['model']
        
        if hasattr(model, 'feature_importances_'):
            # 树模型的特征重要性
            importances = model.feature_importances_
            feature_names = self.X.columns
            
            # 创建特征重要性DataFrame
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': importances
            }).sort_values('importance', ascending=False)
            
            print("前10个最重要的特征:")
            for i, (_, row) in enumerate(importance_df.head(10).iterrows(), 1):
                print(f"  {i:2d}. {row['feature']:20s}: {row['importance']:.4f}")
            
            return importance_df
        
        elif hasattr(model, 'coef_'):
            # 线性模型的系数
            coefficients = np.abs(model.coef_)
            feature_names = self.X.columns
            
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': coefficients
            }).sort_values('importance', ascending=False)
            
            print("前10个最重要的特征 (绝对系数):")
            for i, (_, row) in enumerate(importance_df.head(10).iterrows(), 1):
                print(f"  {i:2d}. {row['feature']:20s}: {row['importance']:.4f}")
            
            return importance_df
        
        else:
            print("该模型不支持特征重要性分析")
            return None
    
    def make_predictions(self):
        """进行预测并评估"""
        print("\n=== 预测结果 ===")
        
        # 对训练数据进行预测
        y_pred = self.best_model.predict(self.X_scaled)
        
        # 计算评估指标
        mse = mean_squared_error(self.y, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(self.y, y_pred)
        r2 = r2_score(self.y, y_pred)
        
        print(f"训练集评估结果:")
        print(f"  RMSE: {rmse:.2f}°C")
        print(f"  MAE:  {mae:.2f}°C")
        print(f"  R²:   {r2:.3f}")
        
        # 创建预测结果DataFrame
        results_df = self.df[['taxid', 'species_name', 'organism_type', 'optimum_temperature']].copy()
        results_df['predicted_temperature'] = y_pred
        results_df['prediction_error'] = results_df['optimum_temperature'] - results_df['predicted_temperature']
        results_df['absolute_error'] = np.abs(results_df['prediction_error'])
        
        print(f"\n预测结果详情:")
        for _, row in results_df.iterrows():
            print(f"  taxid {row['taxid']:2d}: 实际 {row['optimum_temperature']:4.1f}°C, "
                  f"预测 {row['predicted_temperature']:4.1f}°C, "
                  f"误差 {row['prediction_error']:+5.1f}°C")
        
        return results_df
    
    def generate_visualization(self, results_df, importance_df):
        """生成可视化图表"""
        print("\n=== 生成可视化图表 ===")
        
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial']
            plt.rcParams['axes.unicode_minus'] = False
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            
            # 1. 实际vs预测温度散点图
            axes[0, 0].scatter(results_df['optimum_temperature'], 
                              results_df['predicted_temperature'], 
                              alpha=0.7, s=100)
            axes[0, 0].plot([results_df['optimum_temperature'].min(), 
                            results_df['optimum_temperature'].max()],
                           [results_df['optimum_temperature'].min(), 
                            results_df['optimum_temperature'].max()], 
                           'r--', alpha=0.8)
            axes[0, 0].set_xlabel('实际温度 (°C)')
            axes[0, 0].set_ylabel('预测温度 (°C)')
            axes[0, 0].set_title('实际温度 vs 预测温度')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 预测误差分布
            axes[0, 1].hist(results_df['prediction_error'], bins=8, alpha=0.7, edgecolor='black')
            axes[0, 1].axvline(0, color='red', linestyle='--', alpha=0.8)
            axes[0, 1].set_xlabel('预测误差 (°C)')
            axes[0, 1].set_ylabel('频次')
            axes[0, 1].set_title('预测误差分布')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 特征重要性 (如果有)
            if importance_df is not None:
                top_features = importance_df.head(10)
                axes[1, 0].barh(range(len(top_features)), top_features['importance'])
                axes[1, 0].set_yticks(range(len(top_features)))
                axes[1, 0].set_yticklabels(top_features['feature'])
                axes[1, 0].set_xlabel('重要性')
                axes[1, 0].set_title('前10个重要特征')
                axes[1, 0].grid(True, alpha=0.3)
            else:
                axes[1, 0].text(0.5, 0.5, '特征重要性不可用', 
                               ha='center', va='center', transform=axes[1, 0].transAxes)
                axes[1, 0].set_title('特征重要性')
            
            # 4. 模型性能比较
            model_names = list(self.results.keys())
            rmse_values = [self.results[name]['cv_rmse_mean'] for name in model_names]
            rmse_stds = [self.results[name]['cv_rmse_std'] for name in model_names]
            
            axes[1, 1].bar(range(len(model_names)), rmse_values, 
                          yerr=rmse_stds, capsize=5, alpha=0.7)
            axes[1, 1].set_xticks(range(len(model_names)))
            axes[1, 1].set_xticklabels(model_names, rotation=45)
            axes[1, 1].set_ylabel('RMSE (°C)')
            axes[1, 1].set_title('模型性能比较')
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig('temperature_prediction_analysis.png', dpi=300, bbox_inches='tight')
            print("可视化图表已保存: temperature_prediction_analysis.png")
            plt.close()
            
        except Exception as e:
            print(f"生成图表失败: {e}")
    
    def generate_report(self, best_model_name, results_df, importance_df):
        """生成预测报告"""
        print("\n=== 生成预测报告 ===")
        
        # 计算统计信息
        rmse = np.sqrt(mean_squared_error(results_df['optimum_temperature'], 
                                         results_df['predicted_temperature']))
        mae = mean_absolute_error(results_df['optimum_temperature'], 
                                 results_df['predicted_temperature'])
        r2 = r2_score(results_df['optimum_temperature'], 
                     results_df['predicted_temperature'])
        
        report_content = f"""# 基因组序列最适生长温度预测报告

## 模型概览
- 最佳模型: {best_model_name}
- 训练数据: {len(results_df)} 个基因组
- 特征数量: {len(self.X.columns)}
- 温度范围: {results_df['optimum_temperature'].min():.1f}°C - {results_df['optimum_temperature'].max():.1f}°C

## 模型性能
- RMSE: {rmse:.2f}°C
- MAE: {mae:.2f}°C
- R²: {r2:.3f}

## 模型比较
"""
        
        for name, result in self.results.items():
            report_content += f"- **{name}**: {result['cv_rmse_mean']:.2f} ± {result['cv_rmse_std']:.2f}°C\n"
        
        if importance_df is not None:
            report_content += f"""
## 重要特征 (前10个)
"""
            for i, (_, row) in enumerate(importance_df.head(10).iterrows(), 1):
                report_content += f"{i}. **{row['feature']}**: {row['importance']:.4f}\n"
        
        report_content += f"""
## 预测结果详情
"""
        
        for _, row in results_df.iterrows():
            report_content += f"- **taxid {row['taxid']}** ({row['species_name']}): "
            report_content += f"实际 {row['optimum_temperature']:.1f}°C, "
            report_content += f"预测 {row['predicted_temperature']:.1f}°C, "
            report_content += f"误差 {row['prediction_error']:+.1f}°C\n"
        
        report_content += f"""
## 使用建议
1. 当前模型基于少量数据训练，建议增加更多基因组数据
2. 可以考虑添加更多特征，如tRNA特征、基因功能特征等
3. 建议使用独立测试集验证模型性能
4. 可以尝试深度学习方法进一步提升预测精度

## 输出文件
- 预测结果: temperature_predictions.tsv
- 可视化图表: temperature_prediction_analysis.png
- 分析报告: temperature_prediction_report.md
"""
        
        # 保存报告
        with open('temperature_prediction_report.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        # 保存预测结果
        results_df.to_csv('temperature_predictions.tsv', sep='\t', index=False, encoding='utf-8')
        
        print("预测报告已保存: temperature_prediction_report.md")
        print("预测结果已保存: temperature_predictions.tsv")

def main():
    """主函数"""
    print("基因组序列最适生长温度预测演示")
    print("=" * 50)
    
    # 检查特征文件
    features_file = "test_features/genome_temperature_features.tsv"
    if not os.path.exists(features_file):
        print(f"❌ 特征文件不存在: {features_file}")
        print("请先运行特征提取器生成特征数据")
        return
    
    # 创建预测模型
    model = TemperaturePredictionModel(features_file)
    
    # 加载和准备数据
    model.load_and_prepare_data()
    
    # 训练模型
    best_model_name = model.train_models()
    
    # 分析特征重要性
    importance_df = model.analyze_feature_importance(best_model_name)
    
    # 进行预测
    results_df = model.make_predictions()
    
    # 生成可视化
    model.generate_visualization(results_df, importance_df)
    
    # 生成报告
    model.generate_report(best_model_name, results_df, importance_df)
    
    print("\n✅ 温度预测演示完成！")

if __name__ == "__main__":
    import os
    main()
