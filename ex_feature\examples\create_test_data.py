#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试数据集

这个脚本创建一些测试基因组文件和元数据，用于演示特征提取功能。
"""

import os
import pandas as pd
from pathlib import Path

def create_test_genome(file_path: str, genome_id: str, gc_content: float = 0.5):
    """创建测试基因组文件"""
    
    # 根据GC含量生成序列
    sequence_length = 5000
    sequence = ""
    
    import random
    random.seed(hash(genome_id) % 1000)  # 使用genome_id作为种子确保可重现
    
    for _ in range(sequence_length):
        if random.random() < gc_content:
            sequence += random.choice(['G', 'C'])
        else:
            sequence += random.choice(['A', 'T'])
    
    # 写入FASTA格式
    with open(file_path, 'w') as f:
        f.write(f">{genome_id}\n")
        # 每行60个字符
        for i in range(0, len(sequence), 60):
            f.write(sequence[i:i+60] + "\n")

def create_test_dataset(output_dir: str = "test_data"):
    """创建测试数据集"""
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 定义测试基因组
    test_genomes = [
        {"genome_id": "psychrophile_1", "temperature": 5.0, "gc_content": 0.35, "organism_type": "psychrophile"},
        {"genome_id": "psychrophile_2", "temperature": 10.0, "gc_content": 0.38, "organism_type": "psychrophile"},
        {"genome_id": "mesophile_1", "temperature": 25.0, "gc_content": 0.45, "organism_type": "mesophile"},
        {"genome_id": "mesophile_2", "temperature": 30.0, "gc_content": 0.48, "organism_type": "mesophile"},
        {"genome_id": "mesophile_3", "temperature": 37.0, "gc_content": 0.52, "organism_type": "mesophile"},
        {"genome_id": "thermophile_1", "temperature": 55.0, "gc_content": 0.62, "organism_type": "thermophile"},
        {"genome_id": "thermophile_2", "temperature": 65.0, "gc_content": 0.68, "organism_type": "thermophile"},
        {"genome_id": "hyperthermophile_1", "temperature": 80.0, "gc_content": 0.72, "organism_type": "hyperthermophile"},
        {"genome_id": "hyperthermophile_2", "temperature": 95.0, "gc_content": 0.75, "organism_type": "hyperthermophile"},
    ]
    
    # 创建基因组文件
    genome_files = []
    for genome_info in test_genomes:
        genome_file = output_path / f"{genome_info['genome_id']}.fna"
        create_test_genome(
            str(genome_file), 
            genome_info['genome_id'], 
            genome_info['gc_content']
        )
        genome_files.append(str(genome_file))
        print(f"创建基因组文件: {genome_file}")
    
    # 创建元数据文件
    metadata = pd.DataFrame([
        {
            'genome_id': genome_info['genome_id'],
            'genome_file': str(output_path / f"{genome_info['genome_id']}.fna"),
            'temperature': genome_info['temperature'],
            'organism_type': genome_info['organism_type'],
            'gc_content_expected': genome_info['gc_content']
        }
        for genome_info in test_genomes
    ])
    
    metadata_file = output_path / "genomes.tsv"
    metadata.to_csv(metadata_file, sep='\t', index=False)
    print(f"创建元数据文件: {metadata_file}")
    
    # 创建README文件
    readme_content = f"""# 测试数据集

这个数据集包含 {len(test_genomes)} 个模拟的微生物基因组，用于测试温度特征提取功能。

## 文件说明

### 基因组文件
"""
    
    for genome_info in test_genomes:
        readme_content += f"- `{genome_info['genome_id']}.fna`: {genome_info['organism_type']} (温度: {genome_info['temperature']}°C, GC含量: {genome_info['gc_content']:.1%})\n"
    
    readme_content += f"""
### 元数据文件
- `genomes.tsv`: 包含所有基因组的元数据信息

## 使用方法

### 批量特征提取
```bash
python ../simple_extract_features.py batch --metadata genomes.tsv --output-dir features/ --threads 4
```

### 单个基因组特征提取
```bash
python ../simple_extract_features.py single --genome psychrophile_1.fna --output psychrophile_1_features.npz
```

## 数据特点

- **低温菌 (Psychrophiles)**: 温度 ≤ 15°C, 低GC含量
- **中温菌 (Mesophiles)**: 温度 20-45°C, 中等GC含量  
- **高温菌 (Thermophiles)**: 温度 45-80°C, 高GC含量
- **超高温菌 (Hyperthermophiles)**: 温度 ≥ 80°C, 很高GC含量

这种设计模拟了真实微生物中温度与GC含量的相关性。
"""
    
    readme_file = output_path / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"创建说明文件: {readme_file}")
    
    print(f"\n✅ 测试数据集创建完成!")
    print(f"   输出目录: {output_path}")
    print(f"   基因组数量: {len(test_genomes)}")
    print(f"   温度范围: {min(g['temperature'] for g in test_genomes):.1f}°C - {max(g['temperature'] for g in test_genomes):.1f}°C")
    
    return str(output_path)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="创建测试数据集")
    parser.add_argument('--output-dir', default='test_data', help='输出目录')
    
    args = parser.parse_args()
    
    create_test_dataset(args.output_dir)
