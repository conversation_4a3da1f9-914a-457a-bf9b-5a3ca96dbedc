"""
DeepMu的特征选择工具。

该模块提供用于选择微生物生长速率和最适温度预测
最重要特征的函数。
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Union
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Lasso
import logging

logger = logging.getLogger(__name__)

class FeatureSelector:
    """DeepMu模型的特征选择器。"""

    def __init__(self, method='mutual_info', k=10, alpha=0.01):
        """
        初始化特征选择器。

        参数:
            method: 特征选择方法 ('mutual_info', 'f_regression', 'random_forest', 'lasso')
            k: 要选择的特征数量 (用于过滤方法)
            alpha: 正则化强度 (用于Lasso)
        """
        self.method = method
        self.k = k
        self.alpha = alpha
        self.selected_indices = None
        self.feature_importances = None
        self.feature_names = None

    def fit(self, X: np.ndarray, y: np.ndarray, feature_names: Optional[List[str]] = None):
        """
        将特征选择器拟合到数据。

        参数:
            X: 特征矩阵 (n_samples, n_features)
            y: 目标值 (n_samples,)
            feature_names: 特征名称 (可选)

        返回:
            自身
        """
        if feature_names is not None:
            self.feature_names = feature_names
        else:
            self.feature_names = [f"feature_{i}" for i in range(X.shape[1])]

        # 处理不同的选择方法
        if self.method == 'mutual_info':
            selector = SelectKBest(mutual_info_regression, k=min(self.k, X.shape[1]))
            selector.fit(X, y)
            self.selected_indices = selector.get_support(indices=True)
            self.feature_importances = np.zeros(X.shape[1])
            self.feature_importances[self.selected_indices] = selector.scores_[self.selected_indices]

        elif self.method == 'f_regression':
            selector = SelectKBest(f_regression, k=min(self.k, X.shape[1]))
            selector.fit(X, y)
            self.selected_indices = selector.get_support(indices=True)
            self.feature_importances = np.zeros(X.shape[1])
            self.feature_importances[self.selected_indices] = selector.scores_[self.selected_indices]

        elif self.method == 'random_forest':
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X, y)
            self.feature_importances = model.feature_importances_
            # 选择前k个特征
            self.selected_indices = np.argsort(self.feature_importances)[-self.k:]

        elif self.method == 'lasso':
            model = Lasso(alpha=self.alpha, random_state=42)
            model.fit(X, y)
            self.feature_importances = np.abs(model.coef_)
            # 选择非零系数的特征
            self.selected_indices = np.where(self.feature_importances > 0)[0]
            if len(self.selected_indices) > self.k:
                # 如果太多特征有非零系数，选择前k个
                top_indices = np.argsort(self.feature_importances[self.selected_indices])[-self.k:]
                self.selected_indices = self.selected_indices[top_indices]

        else:
            raise ValueError(f"未知的特征选择方法: {self.method}")

        return self

    def transform(self, X: np.ndarray) -> np.ndarray:
        """
        通过仅选择重要特征来转换数据。

        参数:
            X: 特征矩阵 (n_samples, n_features)

        返回:
            转换后的特征矩阵 (n_samples, k)
        """
        if self.selected_indices is None:
            raise ValueError("选择器尚未拟合。请先调用fit()。")

        return X[:, self.selected_indices]

    def fit_transform(self, X: np.ndarray, y: np.ndarray, feature_names: Optional[List[str]] = None) -> np.ndarray:
        """
        将选择器拟合到数据并转换它。

        参数:
            X: 特征矩阵 (n_samples, n_features)
            y: 目标值 (n_samples,)
            feature_names: 特征名称 (可选)

        返回:
            转换后的特征矩阵 (n_samples, k)
        """
        self.fit(X, y, feature_names)
        return self.transform(X)

    def get_selected_features(self) -> List[str]:
        """
        获取所选特征的名称。

        返回:
            所选特征名称列表
        """
        if self.selected_indices is None:
            raise ValueError("选择器尚未拟合。请先调用fit()。")

        return [self.feature_names[i] for i in self.selected_indices]

    def get_feature_importances(self) -> Dict[str, float]:
        """
        获取所有特征的重要性分数。

        返回:
            将特征名称映射到重要性分数的字典
        """
        if self.feature_importances is None:
            raise ValueError("选择器尚未拟合。请先调用fit()。")

        return {self.feature_names[i]: self.feature_importances[i] for i in range(len(self.feature_names))}

    def get_top_features(self, n: int = 10) -> Dict[str, float]:
        """
        获取前n个最重要的特征。

        参数:
            n: 要返回的顶级特征数量

        返回:
            将特征名称映射到重要性分数的字典
        """
        if self.feature_importances is None:
            raise ValueError("选择器尚未拟合。请先调用fit()。")

        top_indices = np.argsort(self.feature_importances)[-n:]
        return {self.feature_names[i]: self.feature_importances[i] for i in top_indices}


def select_features_from_dataset(dataset, target_name='growth_rate', method='mutual_info', k=10):
    """
    从数据集中选择重要特征。

    参数:
        dataset: 具有__getitem__方法的数据集对象
        target_name: 目标变量名称 ('growth_rate' 或 'optimal_temperature')
        method: 特征选择方法
        k: 要选择的特征数量

    返回:
        拟合到数据的FeatureSelector对象
    """
    # 收集所有特征和目标
    all_features = []
    all_targets = []
    feature_names = []
    first_item = True
    feature_sizes = {}
    sample_count = 0

    # 第一遍：确定一致的特征大小
    for i in range(len(dataset)):
        try:
            features, targets = dataset[i]

            # 检查每个特征的大小
            for key, value in features.items():
                if isinstance(value, torch.Tensor) and value.numel() > 0:
                    # 获取展平张量的大小
                    size = value.numel()

                    # 更新特征大小跟踪
                    if key not in feature_sizes:
                        feature_sizes[key] = {'size': size, 'count': 1}
                    else:
                        # 如果大小不匹配，标记为可变
                        if feature_sizes[key]['size'] != size:
                            feature_sizes[key]['variable'] = True
                        feature_sizes[key]['count'] += 1

            sample_count += 1
        except Exception as e:
            logger.warning(f"Error processing sample {i} during feature size analysis: {e}")

    # Filter out features with variable sizes or low occurrence
    valid_features = {}
    for key, info in feature_sizes.items():
        if not info.get('variable', False) and info['count'] >= sample_count * 0.9:  # Present in at least 90% of samples
            valid_features[key] = info['size']
            # Generate feature names
            if info['size'] == 1:
                feature_names.append(key)
            else:
                feature_names.extend([f"{key}_{j}" for j in range(info['size'])])

    logger.info(f"Found {len(valid_features)} valid features with consistent sizes")

    # Second pass: extract features with consistent sizes
    for i in range(len(dataset)):
        try:
            features, targets = dataset[i]

            # Flatten and concatenate all valid feature tensors
            flat_features = []
            for key, value in features.items():
                if key in valid_features and isinstance(value, torch.Tensor) and value.numel() > 0:
                    # Convert tensor to numpy and flatten
                    flat_value = value.detach().cpu().numpy().flatten()

                    # Ensure consistent size
                    if flat_value.size == valid_features[key]:
                        flat_features.append(flat_value)

            # Concatenate all flattened features
            if flat_features and len(flat_features) == len(valid_features):
                try:
                    feature_vector = np.concatenate(flat_features)
                    all_features.append(feature_vector)

                    # Get target value
                    if target_name in targets:
                        target_value = targets[target_name].detach().cpu().numpy()
                        all_targets.append(target_value)
                except Exception as e:
                    logger.warning(f"Error concatenating features for sample {i}: {e}")
        except Exception as e:
            logger.warning(f"Error processing sample {i} during feature extraction: {e}")

    # Check if we have enough samples
    if len(all_features) < 10:
        logger.warning(f"Not enough valid samples for feature selection: {len(all_features)}")
        # Create a dummy selector with no feature selection
        selector = FeatureSelector(method=method, k=k)
        selector.feature_names = feature_names
        selector.selected_indices = np.arange(min(k, len(feature_names)))
        selector.feature_importances = np.ones(len(feature_names))
        return selector

    # Convert to numpy arrays
    try:
        X = np.array(all_features)
        y = np.array(all_targets)

        # Verify shapes
        logger.info(f"Feature matrix shape: {X.shape}, Target vector shape: {y.shape}")

        # Create and fit feature selector
        selector = FeatureSelector(method=method, k=min(k, X.shape[1]))
        selector.fit(X, y, feature_names)

        return selector
    except Exception as e:
        logger.error(f"Error during feature selection: {e}")
        # Create a dummy selector with no feature selection
        selector = FeatureSelector(method=method, k=k)
        selector.feature_names = feature_names
        selector.selected_indices = np.arange(min(k, len(feature_names)))
        selector.feature_importances = np.ones(len(feature_names))
        return selector


def apply_feature_selection(model, selector, feature_mapping=None):
    """
    Apply feature selection to a model by modifying its feature processing.

    Args:
        model: PyTorch model
        selector: Fitted FeatureSelector object
        feature_mapping: Dictionary mapping feature names to model components

    Returns:
        Modified model
    """
    # If no feature mapping is provided, try to infer it
    if feature_mapping is None:
        feature_mapping = {}
        for name in selector.get_selected_features():
            if '_' in name:
                base_name = name.split('_')[0]
                feature_mapping[name] = base_name
            else:
                feature_mapping[name] = name

    # Store selected features in the model
    model.selected_features = selector.get_selected_features()
    model.feature_importances = selector.get_feature_importances()
    model.feature_mapping = feature_mapping

    # Get unique base feature names that should be kept
    unique_base_features = set()
    for feature_name in model.selected_features:
        base_name = model.feature_mapping.get(feature_name, feature_name)
        unique_base_features.add(base_name)

    model.unique_base_features = list(unique_base_features)

    # We don't modify the forward method directly, as it's complex and has many dependencies
    # Instead, we'll add a feature filtering method that can be called before processing

    def filter_features(self, x):
        """
        Filter input features to only include selected ones.

        Args:
            x: Dictionary of input features

        Returns:
            Filtered dictionary with only selected features
        """
        # Don't filter if no feature selection is applied
        if not hasattr(self, 'unique_base_features'):
            return x

        # Handle empty dictionaries
        if not x:
            return x

        # Keep all features that are in the unique base features list
        filtered_x = {}
        for key, value in x.items():
            if key in self.unique_base_features:
                filtered_x[key] = value

        # If filtered dictionary is empty but original had values, keep at least one feature
        if not filtered_x and x:
            # Take the first feature from the original dictionary
            first_key = next(iter(x))
            filtered_x[first_key] = x[first_key]

        return filtered_x

    # Add the filter_features method to the model
    import types
    model.filter_features = types.MethodType(filter_features, model)

    # Store the original forward method
    model._original_forward = model.forward

    # Create a new forward method that applies filtering
    def new_forward(self, x):
        # Apply feature filtering
        filtered_x = self.filter_features(x)

        # Call the original forward method with filtered features
        return self._original_forward(filtered_x)

    # Replace the forward method
    model.forward = types.MethodType(new_forward, model)

    return model


def create_feature_importance_report(selector, output_file=None):
    """
    Create a report of feature importances.

    Args:
        selector: Fitted FeatureSelector object
        output_file: Path to save the report (optional)

    Returns:
        Report string
    """
    importances = selector.get_feature_importances()
    sorted_features = sorted(importances.items(), key=lambda x: x[1], reverse=True)

    report = []
    report.append("=" * 80)
    report.append("FEATURE IMPORTANCE REPORT")
    report.append("=" * 80)
    report.append("")

    report.append("TOP FEATURES:")
    report.append("-" * 80)
    for i, (feature, importance) in enumerate(sorted_features[:20], 1):
        report.append(f"{i:2d}. {feature.ljust(30)}: {importance:.6f}")
    report.append("")

    report.append("SELECTED FEATURES:")
    report.append("-" * 80)
    selected_features = selector.get_selected_features()
    for i, feature in enumerate(selected_features, 1):
        importance = importances[feature]
        report.append(f"{i:2d}. {feature.ljust(30)}: {importance:.6f}")
    report.append("")

    report_str = "\n".join(report)

    # Save to file if requested
    if output_file:
        with open(output_file, 'w') as f:
            f.write(report_str)

    return report_str
